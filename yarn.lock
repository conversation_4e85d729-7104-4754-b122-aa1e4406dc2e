# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.16.7", "@babel/code-frame@^7.8.3":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/code-frame/download/@babel/code-frame-7.16.7.tgz"
  integrity sha512-iAXqUn8IIeBTNd72xsFlgaXHkMBMt6y4HJp1tIaK465CWLT/fG1aqB7ykr95gHHmlBdGbFeWWfyB4NJJ0nmeIg==
  dependencies:
    "@babel/highlight" "^7.16.7"

"@babel/compat-data@^7.13.11", "@babel/compat-data@^7.16.4":
  version "7.16.4"
  resolved "https://registry.npmmirror.com/@babel/compat-data/download/@babel/compat-data-7.16.4.tgz?cache=0&sync_timestamp=1637102977873&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcompat-data%2Fdownload%2F%40babel%2Fcompat-data-7.16.4.tgz"
  integrity sha512-1o/jo7D+kC9ZjHX5v+EHrdjl3PhxMrLSOTGsOdHJ+KL8HCaEK6ehrVL2RS6oHDZp+L7xLirLrPmQtEng769J/Q==

"@babel/core@^7.11.0":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.16.7.tgz"
  integrity sha512-aeLaqcqThRNZYmbMqtulsetOQZ/5gbR/dWruUCJcpas4Qoyy+QeagfDsPdMrqwsPRDNxJvBlRiZxxX7THO7qtA==
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/generator" "^7.16.7"
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helpers" "^7.16.7"
    "@babel/parser" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.16.7"
    "@babel/types" "^7.16.7"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.1.2"
    semver "^6.3.0"
    source-map "^0.5.0"

"@babel/generator@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/generator/download/@babel/generator-7.16.7.tgz"
  integrity sha512-/ST3Sg8MLGY5HVYmrjOgL60ENux/HfO/CsUh7y4MalThufhE/Ff/6EibFDHi4jiDCaWfJKoqbE6oTh21c5hrRg==
  dependencies:
    "@babel/types" "^7.16.7"
    jsesc "^2.5.1"
    source-map "^0.5.0"

"@babel/helper-annotate-as-pure@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.16.7.tgz"
  integrity sha512-s6t2w/IPQVTAET1HitoowRGXooX8mCgtuP5195wD/QJPV6wYjpujCGF7JuMODVX2ZAJOf1GT6DT9MHEZvLOFSw==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.16.7.tgz"
  integrity sha512-C6FdbRaxYjwVu/geKW4ZeQ0Q31AftgRcdSnZ5/jsH6BzCJbtvXvhpfkbkThYSuutZA7nCXpPR6AD9zd1dprMkA==
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-compilation-targets@^7.13.0", "@babel/helper-compilation-targets@^7.16.7", "@babel/helper-compilation-targets@^7.9.6":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.16.7.tgz"
  integrity sha512-mGojBwIWcwGD6rfqgRXVlVYmPAv7eOpIemUG3dGnDdCY4Pae70ROij3XmfrH6Fa1h1aiDylpglbZyktfzyo/hA==
  dependencies:
    "@babel/compat-data" "^7.16.4"
    "@babel/helper-validator-option" "^7.16.7"
    browserslist "^4.17.5"
    semver "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.16.7.tgz"
  integrity sha512-kIFozAvVfK05DM4EVQYKK+zteWvY85BFdGBRQBytRyY3y+6PX0DkDOn/CZ3lEuczCfrCxEzwt0YtP/87YPTWSw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-member-expression-to-functions" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"

"@babel/helper-create-regexp-features-plugin@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.16.7.tgz"
  integrity sha512-fk5A6ymfp+O5+p2yCkXAu5Kyj6v0xh0RBeNcAkYUMDvvAAoxvSKXn+Jb37t/yWFiQVDFK1ELpUTD8/aLhCPu+g==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    regexpu-core "^4.7.1"

"@babel/helper-define-polyfill-provider@^0.3.0":
  version "0.3.0"
  resolved "https://registry.npmmirror.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.3.0.tgz?cache=0&sync_timestamp=1636799771768&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fhelper-define-polyfill-provider%2Fdownload%2F%40babel%2Fhelper-define-polyfill-provider-0.3.0.tgz"
  integrity sha512-7hfT8lUljl/tM3h+izTX/pO3W3frz2ok6Pk+gzys8iJqDfZrZy2pXjRTZAvG2YmfHun1X4q8/UZRLatMfqc5Tg==
  dependencies:
    "@babel/helper-compilation-targets" "^7.13.0"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/traverse" "^7.13.0"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"
    semver "^6.1.2"

"@babel/helper-environment-visitor@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-environment-visitor/download/@babel/helper-environment-visitor-7.16.7.tgz"
  integrity sha512-SLLb0AAn6PkUeAfKJCCOl9e1R53pQlGAfc4y4XuMRZfqeMYLE0dM1LMhqbGAlGQY0lfw5/ohoYWAe9V1yibRag==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-explode-assignable-expression@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.16.7.tgz"
  integrity sha512-KyUenhWMC8VrxzkGP0Jizjo4/Zx+1nNZhgocs+gLzyZyB8SHidhoq9KK/8Ato4anhwsivfkBLftky7gvzbZMtQ==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-function-name@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-function-name/download/@babel/helper-function-name-7.16.7.tgz"
  integrity sha512-QfDfEnIUyyBSR3HtrtGECuZ6DAyCkYFp7GHl75vFtTnn6pjKeK0T1DB5lLkFvBea8MdaiUABx3osbgLyInoejA==
  dependencies:
    "@babel/helper-get-function-arity" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-get-function-arity@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.16.7.tgz"
  integrity sha512-flc+RLSOBXzNzVhcLu6ujeHUrD6tANAOU5ojrRx/as+tbzf8+stUCj7+IfRRoAbEZqj/ahXEMsjhOhgeZsrnTw==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-hoist-variables@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.16.7.tgz"
  integrity sha512-m04d/0Op34H5v7pbZw6pSKP7weA6lsMvfiIAMeIvkY/R4xQtBSMFEigu9QTZ2qB/9l22vsxtM8a+Q8CzD255fg==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-member-expression-to-functions@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.16.7.tgz"
  integrity sha512-VtJ/65tYiU/6AbMTDwyoXGPKHgTsfRarivm+YbB5uAzKUyuPjgZSgAFeG87FCigc7KNHu2Pegh1XIT3lXjvz3Q==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.16.7", "@babel/helper-module-imports@^7.8.3":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.16.7.tgz"
  integrity sha512-LVtS6TqjJHFc+nYeITRo6VLXve70xmq7wPhWTqDJusJEgGmkAACWwMiTNrvfoQo6hEhFwAIixNkvB0jPXDL8Wg==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-module-transforms@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.16.7.tgz"
  integrity sha512-gaqtLDxJEFCeQbYp9aLAefjhkKdjKcdh6DB7jniIGU3Pz52WAmP268zK0VgPz9hUNkMSYeH976K2/Y6yPadpng==
  dependencies:
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-simple-access" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    "@babel/helper-validator-identifier" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-optimise-call-expression@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.16.7.tgz"
  integrity sha512-EtgBhg7rd/JcnpZFXpBy0ze1YRfdm7BnBX4uKMBd3ixa3RGAE002JZB66FJyNH7g0F38U05pXmA5P8cBh7z+1w==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.13.0", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.16.7.tgz"
  integrity sha512-Qg3Nk7ZxpgMrsox6HreY1ZNKdBq7K72tDSliA6dCl5f007jR4ne8iD5UzuNnCJH2xBf2BEEVGr+/OL6Gdp7RxA==

"@babel/helper-remap-async-to-generator@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.16.7.tgz"
  integrity sha512-C3o117GnP/j/N2OWo+oepeWbFEKRfNaay+F1Eo5Mj3A1SRjyx+qaFhm23nlipub7Cjv2azdUUiDH+VlpdwUFRg==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-wrap-function" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-replace-supers@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.16.7.tgz"
  integrity sha512-y9vsWilTNaVnVh6xiJfABzsNpgDPKev9HnAgz6Gb1p6UUwf9NepdlsV7VXGCftJM+jqD5f7JIEubcpLjZj5dBw==
  dependencies:
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-member-expression-to-functions" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/traverse" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-simple-access@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.16.7.tgz"
  integrity sha512-ZIzHVyoeLMvXMN/vok/a4LWRy8G2v205mNP0XOuf9XRLyX5/u9CnVulUtDgUTama3lT+bf/UqucuZjqiGuTS1g==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-skip-transparent-expression-wrappers@^7.16.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.16.0.tgz?cache=0&sync_timestamp=1635567047917&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fhelper-skip-transparent-expression-wrappers%2Fdownload%2F%40babel%2Fhelper-skip-transparent-expression-wrappers-7.16.0.tgz"
  integrity sha1-DuM4gHAUfDrgUeSH7KPrsOLouwk=
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-split-export-declaration@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.16.7.tgz"
  integrity sha512-xbWoy/PFoxSWazIToT9Sif+jJTlrMcndIsaOKvTA6u7QEo7ilkRZpjew18/W3c7nm8fXdUDXh02VXTbZ0pGDNw==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-validator-identifier@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.16.7.tgz"
  integrity sha512-hsEnFemeiW4D08A5gUAZxLBTXpZ39P+a+DGDsHw1yxqyQ/jzFEnxf5uTEGp+3bzAbNOxU1paTgYS4ECU/IgfDw==

"@babel/helper-validator-option@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.16.7.tgz"
  integrity sha512-TRtenOuRUVo9oIQGPC5G9DgK4743cdxvtOw0weQNpZXaS16SCBi5MNjZF8vba3ETURjZpTbVn7Vvcf2eAwFozQ==

"@babel/helper-wrap-function@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.16.7.tgz"
  integrity sha512-7a9sABeVwcunnztZZ7WTgSw6jVYLzM1wua0Z4HIXm9S3/HC96WKQTkFgGEaj5W06SHHihPJ6Le6HzS5cGOQMNw==
  dependencies:
    "@babel/helper-function-name" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helpers@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/helpers/download/@babel/helpers-7.16.7.tgz"
  integrity sha512-9ZDoqtfY7AuEOt3cxchfii6C7GDyyMBffktR5B2jvWv8u2+efwvpnVKXMWzNehqy68tKgAfSwfdw/lWpthS2bw==
  dependencies:
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/highlight@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/highlight/download/@babel/highlight-7.16.7.tgz"
  integrity sha512-aKpPMfLvGO3Q97V0qhw/V2SWNWlwfJknuwAunU7wZLSfrM4xTBvg7E5opUVi1kJTBKihE38CPg4nBiqX83PWYw==
  dependencies:
    "@babel/helper-validator-identifier" "^7.16.7"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.16.4", "@babel/parser@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/parser/download/@babel/parser-7.16.7.tgz"
  integrity sha512-sR4eaSrnM7BV7QPzGfEX5paG/6wrZM3I0HDzfIAK06ESvo9oy3xBuVBxE3MbQaKNhvg8g/ixjMWo2CGpzpHsDA==

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/download/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.16.7.tgz"
  integrity sha512-anv/DObl7waiGEnC24O9zqL0pSuI9hljihqiDuFHC8d7/bjr/4RLGPWuc8rYOff/QPzbEPSkzG8wGG9aDuhHRg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/download/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.16.7.tgz"
  integrity sha512-di8vUHRdf+4aJ7ltXhaDbPoszdkh59AQtJM5soLsuHpQJdFQZOA4uGj0V2u/CZ8bJ/u8ULDL5yq6FO/bCXnKHw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.16.7"

"@babel/plugin-proposal-async-generator-functions@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-async-generator-functions/download/@babel/plugin-proposal-async-generator-functions-7.16.7.tgz"
  integrity sha512-TTXBT3A5c11eqRzaC6beO6rlFT3Mo9C2e8eB44tTr52ESXSK2CIc2fOp1ynpAwQA8HhBMho+WXhMHWlAe3xkpw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-remap-async-to-generator" "^7.16.7"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.16.7", "@babel/plugin-proposal-class-properties@^7.8.3":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.16.7.tgz"
  integrity sha512-IobU0Xme31ewjYOShSIqd/ZGM/r/cuOz2z0MDbNrhF5FW+ZVgi0f2lyeoj9KFPDOAqsYxmLWZte1WOwlvY9aww==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-proposal-class-static-block@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-class-static-block/download/@babel/plugin-proposal-class-static-block-7.16.7.tgz"
  integrity sha512-dgqJJrcZoG/4CkMopzhPJjGxsIe9A8RlkQLnL/Vhhx8AA9ZuaRwGSlscSh42hazc7WSrya/IK7mTeoF0DP9tEw==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-decorators@^7.8.3":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.16.7.tgz"
  integrity sha512-DoEpnuXK14XV9btI1k8tzNGCutMclpj4yru8aXKoHlVmbO1s+2A+g2+h4JhcjrxkFJqzbymnLG6j/niOf3iFXQ==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-decorators" "^7.16.7"

"@babel/plugin-proposal-dynamic-import@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-dynamic-import/download/@babel/plugin-proposal-dynamic-import-7.16.7.tgz"
  integrity sha512-I8SW9Ho3/8DRSdmDdH3gORdyUuYnk1m4cMxUAdu5oy4n3OfN8flDEH+d60iG7dUfi0KkYwSvoalHzzdRzpWHTg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-namespace-from@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-export-namespace-from/download/@babel/plugin-proposal-export-namespace-from-7.16.7.tgz"
  integrity sha512-ZxdtqDXLRGBL64ocZcs7ovt71L3jhC1RGSyR996svrCi3PYqHNkb3SwPJCs8RIzD86s+WPpt2S73+EHCGO+NUA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-json-strings/download/@babel/plugin-proposal-json-strings-7.16.7.tgz"
  integrity sha512-lNZ3EEggsGY78JavgbHsK9u5P3pQaW7k4axlgFLYkMd7UBsiNahCITShLjNQschPyjtO6dADrL24757IdhBrsQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-logical-assignment-operators/download/@babel/plugin-proposal-logical-assignment-operators-7.16.7.tgz"
  integrity sha512-K3XzyZJGQCr00+EtYtrDjmwX7o7PLK6U9bi1nCwkQioRFVUv6dJoxbQjtWVtP+bCPy82bONBKG8NPyQ4+i6yjg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-nullish-coalescing-operator/download/@babel/plugin-proposal-nullish-coalescing-operator-7.16.7.tgz"
  integrity sha512-aUOrYU3EVtjf62jQrCj63pYZ7k6vns2h/DQvHPWGmsJRYzWXZ6/AsfgpiRy6XiuIDADhJzP2Q9MwSMKauBQ+UQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-numeric-separator/download/@babel/plugin-proposal-numeric-separator-7.16.7.tgz"
  integrity sha512-vQgPMknOIgiuVqbokToyXbkY/OmmjAzr/0lhSIbG/KmnzXPGwW/AdhdKpi+O4X/VkWiWjnkKOBiqJrTaC98VKw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.16.7.tgz"
  integrity sha512-3O0Y4+dw94HA86qSg9IHfyPktgR7q3gpNVAeiKQd+8jBKFaU5NQS1Yatgo4wY+UFNuLjvxcSmzcsHqrhgTyBUA==
  dependencies:
    "@babel/compat-data" "^7.16.4"
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.16.7"

"@babel/plugin-proposal-optional-catch-binding@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.16.7.tgz"
  integrity sha512-eMOH/L4OvWSZAE1VkHbr1vckLG1WUcHGJSLqqQwl2GaUqG6QjddvrOaTUMNYiv77H5IKPMZ9U9P7EaHwvAShfA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-optional-chaining/download/@babel/plugin-proposal-optional-chaining-7.16.7.tgz"
  integrity sha512-eC3xy+ZrUcBtP7x+sq62Q/HYd674pPTb/77XZMb5wbDPGWIdUbSr4Agr052+zaUPSb+gGRnjxXfKFvx5iMJ+DA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-private-methods/download/@babel/plugin-proposal-private-methods-7.16.7.tgz"
  integrity sha512-7twV3pzhrRxSwHeIvFE6coPgvo+exNDOiGUMg39o2LiLo1Y+4aKpfkcLGcg1UHonzorCt7SNXnoMyCnnIOA8Sw==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-proposal-private-property-in-object@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.16.7.tgz"
  integrity sha512-rMQkjcOFbm+ufe3bTZLyOfsOUOxyvLXZJCTARhJr+8UMSoZmqTe1K1BgkFcrW37rAchWg57yI69ORxiWvUINuQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.16.7", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.16.7.tgz"
  integrity sha512-QRK0YI/40VLhNVGIjRNAAQkEHws0cswSdFFjpFyt943YmJIU1da9uW63Iu6NFV6CxTZW5eTDCrwZUstBWgp/Rg==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz"
  integrity sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-class-static-block/download/@babel/plugin-syntax-class-static-block-7.14.5.tgz"
  integrity sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-decorators@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.16.7.tgz"
  integrity sha512-vQ+PxL+srA7g6Rx6I1e15m55gftknl2X8GCUW1JTlkTaXZLJOS0UcaY0eK9jYT7IYf4awn6qwyghVHLDz1WyMw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz?cache=0&sync_timestamp=1624608013305&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-syntax-dynamic-import%2Fdownload%2F%40babel%2Fplugin-syntax-dynamic-import-7.8.3.tgz"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  version "7.8.3"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-export-namespace-from/download/@babel/plugin-syntax-export-namespace-from-7.8.3.tgz"
  integrity sha1-AolkqbqA28CUyRXEh618TnpmRlo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.2.0", "@babel/plugin-syntax-jsx@^7.8.3":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.16.7.tgz"
  integrity sha512-Esxmk7YjA8QysKeT3VhTXvF6y77f/a91SIs4pWb4H2eWGQkCKFgQaG6hdoEVZtGsrAcb2K5BW66XsOErD4WU3Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-private-property-in-object/download/@babel/plugin-syntax-private-property-in-object-7.14.5.tgz"
  integrity sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.14.5.tgz"
  integrity sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-arrow-functions@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.16.7.tgz"
  integrity sha512-9ffkFFMbvzTvv+7dTp/66xvZAWASuPD5Tl9LK3Z9vhOmANo6j94rik+5YMBt4CwHVMWLWpMsriIc2zsa3WW3xQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-async-to-generator@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.16.7.tgz"
  integrity sha512-pFEfjnK4DfXCfAlA5I98BYdDJD8NltMzx19gt6DAmfE+2lXRfPUoa0/5SUjT4+TDE1W/rcxU/1lgN55vpAjjdg==
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-remap-async-to-generator" "^7.16.7"

"@babel/plugin-transform-block-scoped-functions@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.16.7.tgz"
  integrity sha512-JUuzlzmF40Z9cXyytcbZEZKckgrQzChbQJw/5PuEHYeqzCsvebDx0K0jWnIIVcmmDOAVctCgnYs0pMcrYj2zJg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-block-scoping@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.16.7.tgz"
  integrity sha512-ObZev2nxVAYA4bhyusELdo9hb3H+A56bxH3FZMbEImZFiEDYVHXQSJ1hQKFlDnlt8G9bBrCZ5ZpURZUrV4G5qQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-classes@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.16.7.tgz"
  integrity sha512-WY7og38SFAGYRe64BrjKf8OrE6ulEHtr5jEYaZMwox9KebgqPi67Zqz8K53EKk1fFEJgm96r32rkKZ3qA2nCWQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.16.7.tgz"
  integrity sha512-gN72G9bcmenVILj//sv1zLNaPyYcOzUho2lIJBMh/iakJ9ygCo/hEF9cpGb61SCMEDxbbyBoVQxrt+bWKu5KGw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-destructuring@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.16.7.tgz"
  integrity sha512-VqAwhTHBnu5xBVDCvrvqJbtLUa++qZaWC0Fgr2mqokBlulZARGyIvZDoqbPlPaKImQ9dKAcCzbv+ul//uqu70A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-dotall-regex@^7.16.7", "@babel/plugin-transform-dotall-regex@^7.4.4":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.16.7.tgz"
  integrity sha512-Lyttaao2SjZF6Pf4vk1dVKv8YypMpomAbygW+mU5cYP3S5cWTfCJjG8xV6CFdzGFlfWK81IjL9viiTvpb6G7gQ==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-duplicate-keys@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.16.7.tgz"
  integrity sha512-03DvpbRfvWIXyK0/6QiR1KMTWeT6OcQ7tbhjrXyFS02kjuX/mu5Bvnh5SDSWHxyawit2g5aWhKwI86EE7GUnTw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-exponentiation-operator@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.16.7.tgz"
  integrity sha512-8UYLSlyLgRixQvlYH3J2ekXFHDFLQutdy7FfFAMm3CPZ6q9wHCwnUyiXpQCe3gVVnQlHc5nsuiEVziteRNTXEA==
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-for-of@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.16.7.tgz"
  integrity sha512-/QZm9W92Ptpw7sjI9Nx1mbcsWz33+l8kuMIQnDwgQBG5s3fAfQvkRjQ7NqXhtNcKOnPkdICmUHyCaWW06HCsqg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-function-name@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.16.7.tgz"
  integrity sha512-SU/C68YVwTRxqWj5kgsbKINakGag0KTgq9f2iZEXdStoAbOzLHEBRYzImmA6yFo8YZhJVflvXmIHUO7GWHmxxA==
  dependencies:
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-literals@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.16.7.tgz"
  integrity sha512-6tH8RTpTWI0s2sV6uq3e/C9wPo4PTqqZps4uF0kzQ9/xPLFQtipynvmT1g/dOfEJ+0EQsHhkQ/zyRId8J2b8zQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-member-expression-literals@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.16.7.tgz"
  integrity sha512-mBruRMbktKQwbxaJof32LT9KLy2f3gH+27a5XSuXo6h7R3vqltl0PgZ80C8ZMKw98Bf8bqt6BEVi3svOh2PzMw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-modules-amd@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.16.7.tgz"
  integrity sha512-KaaEtgBL7FKYwjJ/teH63oAmE3lP34N3kshz8mm4VMAw7U3PxjVwwUmxEFksbgsNUaO3wId9R2AVQYSEGRa2+g==
  dependencies:
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.16.7.tgz"
  integrity sha512-h2RP2kE7He1ZWKyAlanMZrAbdv+Acw1pA8dQZhE025WJZE2z0xzFADAinXA9fxd5bn7JnM+SdOGcndGx1ARs9w==
  dependencies:
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-simple-access" "^7.16.7"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.16.7.tgz"
  integrity sha512-DuK5E3k+QQmnOqBR9UkusByy5WZWGRxfzV529s9nPra1GE7olmxfqO2FHobEOYSPIjPBTr4p66YDcjQnt8cBmw==
  dependencies:
    "@babel/helper-hoist-variables" "^7.16.7"
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-identifier" "^7.16.7"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.16.7.tgz"
  integrity sha512-EMh7uolsC8O4xhudF2F6wedbSHm1HHZ0C6aJ7K67zcDNidMzVcxWdGr+htW9n21klm+bOn+Rx4CBsAntZd3rEQ==
  dependencies:
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-named-capturing-groups-regex@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.16.7.tgz"
  integrity sha512-kFy35VwmwIQwCjwrAQhl3+c/kr292i4KdLPKp5lPH03Ltc51qnFlIADoyPxc/6Naz3ok3WdYKg+KK6AH+D4utg==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"

"@babel/plugin-transform-new-target@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.16.7.tgz"
  integrity sha512-xiLDzWNMfKoGOpc6t3U+etCE2yRnn3SM09BXqWPIZOBpL2gvVrBWUKnsJx0K/ADi5F5YC5f8APFfWrz25TdlGg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-object-super@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.16.7.tgz"
  integrity sha512-14J1feiQVWaGvRxj2WjyMuXS2jsBkgB3MdSN5HuC2G5nRspa5RK9COcs82Pwy5BuGcjb+fYaUj94mYcOj7rCvw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"

"@babel/plugin-transform-parameters@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.16.7.tgz"
  integrity sha512-AT3MufQ7zZEhU2hwOA11axBnExW0Lszu4RL/tAlUJBuNoRak+wehQW8h6KcXOcgjY42fHtDxswuMhMjFEuv/aw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-property-literals@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.16.7.tgz"
  integrity sha512-z4FGr9NMGdoIl1RqavCqGG+ZuYjfZ/hkCIeuH6Do7tXmSm0ls11nYVSJqFEUOSJbDab5wC6lRE/w6YjVcr6Hqw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-regenerator@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.16.7.tgz"
  integrity sha512-mF7jOgGYCkSJagJ6XCujSQg+6xC1M77/03K2oBmVJWoFGNUtnVJO4WHKJk3dnPC8HCcj4xBQP1Egm8DWh3Pb3Q==
  dependencies:
    regenerator-transform "^0.14.2"

"@babel/plugin-transform-reserved-words@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.16.7.tgz"
  integrity sha512-KQzzDnZ9hWQBjwi5lpY5v9shmm6IVG0U9pB18zvMu2i4H90xpT4gmqwPYsn8rObiadYe2M0gmgsiOIF5A/2rtg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-runtime@^7.11.0":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.16.7.tgz"
  integrity sha512-2FoHiSAWkdq4L06uaDN3rS43i6x28desUVxq+zAFuE6kbWYQeiLPJI5IC7Sg9xKYVcrBKSQkVUfH6aeQYbl9QA==
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    babel-plugin-polyfill-corejs2 "^0.3.0"
    babel-plugin-polyfill-corejs3 "^0.4.0"
    babel-plugin-polyfill-regenerator "^0.3.0"
    semver "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.16.7.tgz"
  integrity sha512-hah2+FEnoRoATdIb05IOXf+4GzXYTq75TVhIn1PewihbpyrNWUt2JbudKQOETWw6QpLe+AIUpJ5MVLYTQbeeUg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-spread@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.16.7.tgz"
  integrity sha512-+pjJpgAngb53L0iaA5gU/1MLXJIfXcYepLgXB3esVRf4fqmj8f2cxM3/FKaHsZms08hFQJkFccEWuIpm429TXg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"

"@babel/plugin-transform-sticky-regex@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.16.7.tgz"
  integrity sha512-NJa0Bd/87QV5NZZzTuZG5BPJjLYadeSZ9fO6oOUoL4iQx+9EEuw/eEM92SrsT19Yc2jgB1u1hsjqDtH02c3Drw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-template-literals@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.16.7.tgz"
  integrity sha512-VwbkDDUeenlIjmfNeDX/V0aWrQH2QiVyJtwymVQSzItFDTpxfyJh3EVaQiS0rIN/CqbLGr0VcGmuwyTdZtdIsA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-typeof-symbol@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.16.7.tgz"
  integrity sha512-p2rOixCKRJzpg9JB4gjnG4gjWkWa89ZoYUnl9snJ1cWIcTH/hvxZqfO+WjG6T8DRBpctEol5jw1O5rA8gkCokQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-unicode-escapes@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.16.7.tgz"
  integrity sha512-TAV5IGahIz3yZ9/Hfv35TV2xEm+kaBDaZQCn2S/hG9/CZ0DktxJv9eKfPc7yYCvOYR4JGx1h8C+jcSOvgaaI/Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-unicode-regex@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.16.7.tgz"
  integrity sha512-oC5tYYKw56HO75KZVLQ+R/Nl3Hro9kf8iG0hXoaHP7tjAyCpvqBiSNe6vGrZni1Z6MggmUOC6A7VP7AVmw225Q==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/preset-env@^7.11.0":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/preset-env/download/@babel/preset-env-7.16.7.tgz"
  integrity sha512-urX3Cee4aOZbRWOSa3mKPk0aqDikfILuo+C7qq7HY0InylGNZ1fekq9jmlr3pLWwZHF4yD7heQooc2Pow2KMyQ==
  dependencies:
    "@babel/compat-data" "^7.16.4"
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-option" "^7.16.7"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.16.7"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.16.7"
    "@babel/plugin-proposal-async-generator-functions" "^7.16.7"
    "@babel/plugin-proposal-class-properties" "^7.16.7"
    "@babel/plugin-proposal-class-static-block" "^7.16.7"
    "@babel/plugin-proposal-dynamic-import" "^7.16.7"
    "@babel/plugin-proposal-export-namespace-from" "^7.16.7"
    "@babel/plugin-proposal-json-strings" "^7.16.7"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.16.7"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.16.7"
    "@babel/plugin-proposal-numeric-separator" "^7.16.7"
    "@babel/plugin-proposal-object-rest-spread" "^7.16.7"
    "@babel/plugin-proposal-optional-catch-binding" "^7.16.7"
    "@babel/plugin-proposal-optional-chaining" "^7.16.7"
    "@babel/plugin-proposal-private-methods" "^7.16.7"
    "@babel/plugin-proposal-private-property-in-object" "^7.16.7"
    "@babel/plugin-proposal-unicode-property-regex" "^7.16.7"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.16.7"
    "@babel/plugin-transform-async-to-generator" "^7.16.7"
    "@babel/plugin-transform-block-scoped-functions" "^7.16.7"
    "@babel/plugin-transform-block-scoping" "^7.16.7"
    "@babel/plugin-transform-classes" "^7.16.7"
    "@babel/plugin-transform-computed-properties" "^7.16.7"
    "@babel/plugin-transform-destructuring" "^7.16.7"
    "@babel/plugin-transform-dotall-regex" "^7.16.7"
    "@babel/plugin-transform-duplicate-keys" "^7.16.7"
    "@babel/plugin-transform-exponentiation-operator" "^7.16.7"
    "@babel/plugin-transform-for-of" "^7.16.7"
    "@babel/plugin-transform-function-name" "^7.16.7"
    "@babel/plugin-transform-literals" "^7.16.7"
    "@babel/plugin-transform-member-expression-literals" "^7.16.7"
    "@babel/plugin-transform-modules-amd" "^7.16.7"
    "@babel/plugin-transform-modules-commonjs" "^7.16.7"
    "@babel/plugin-transform-modules-systemjs" "^7.16.7"
    "@babel/plugin-transform-modules-umd" "^7.16.7"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.16.7"
    "@babel/plugin-transform-new-target" "^7.16.7"
    "@babel/plugin-transform-object-super" "^7.16.7"
    "@babel/plugin-transform-parameters" "^7.16.7"
    "@babel/plugin-transform-property-literals" "^7.16.7"
    "@babel/plugin-transform-regenerator" "^7.16.7"
    "@babel/plugin-transform-reserved-words" "^7.16.7"
    "@babel/plugin-transform-shorthand-properties" "^7.16.7"
    "@babel/plugin-transform-spread" "^7.16.7"
    "@babel/plugin-transform-sticky-regex" "^7.16.7"
    "@babel/plugin-transform-template-literals" "^7.16.7"
    "@babel/plugin-transform-typeof-symbol" "^7.16.7"
    "@babel/plugin-transform-unicode-escapes" "^7.16.7"
    "@babel/plugin-transform-unicode-regex" "^7.16.7"
    "@babel/preset-modules" "^0.1.5"
    "@babel/types" "^7.16.7"
    babel-plugin-polyfill-corejs2 "^0.3.0"
    babel-plugin-polyfill-corejs3 "^0.4.0"
    babel-plugin-polyfill-regenerator "^0.3.0"
    core-js-compat "^3.19.1"
    semver "^6.3.0"

"@babel/preset-modules@^0.1.5":
  version "0.1.5"
  resolved "https://registry.npmmirror.com/@babel/preset-modules/download/@babel/preset-modules-0.1.5.tgz"
  integrity sha1-75Odbn8miCfhhBY43G/5VRXhFdk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/runtime@^7.11.0", "@babel/runtime@^7.8.4":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/runtime/download/@babel/runtime-7.16.7.tgz"
  integrity sha512-9E9FJowqAsytyOY6LG+1KuueckRL+aQW+mKvXRXnuFGyRAyepJPmEo9vgMfXUA6O9u3IeEdv9MAkppFcaQwogQ==
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@^7.0.0", "@babel/template@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/template/download/@babel/template-7.16.7.tgz"
  integrity sha512-I8j/x8kHUrbYRTUxXrrMbfCa7jxkE7tZre39x3kjr9hvI82cK1FfqLygotcWN5kdPGWcLdWMHpSBavse5tWw3w==
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/parser" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.13.0", "@babel/traverse@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/traverse/download/@babel/traverse-7.16.7.tgz"
  integrity sha512-8KWJPIb8c2VvY8AJrydh6+fVRo2ODx1wYBU2398xJVq0JomuLBZmVQzLPBblJgHIGYG4znCpUZUZ0Pt2vdmVYQ==
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/generator" "^7.16.7"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-hoist-variables" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    "@babel/parser" "^7.16.7"
    "@babel/types" "^7.16.7"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.16.0", "@babel/types@^7.16.7", "@babel/types@^7.4.4":
  version "7.16.7"
  resolved "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.16.7.tgz"
  integrity sha512-E8HuV7FO9qLpx6OtoGfUQ2cjIYnbFwvZWYBS+87EwtdMvmUPJSwykpovFB+8insbpF0uJcpr8KMUi64XZntZcg==
  dependencies:
    "@babel/helper-validator-identifier" "^7.16.7"
    to-fast-properties "^2.0.0"

"@ctrl/tinycolor@^3.4.0":
  version "3.4.0"
  resolved "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.4.0.tgz"
  integrity sha512-JZButFdZ1+/xAfpguQHoabIXkcqRRKpMrWKBkpEZZyxfY9C1DpADFB8PEqGSTeFr135SaTRfKqGKx5xSCLI7ZQ==

"@element-plus/icons-vue@^0.2.4":
  version "0.2.4"
  resolved "https://registry.npmmirror.com/@element-plus/icons-vue/-/icons-vue-0.2.4.tgz"
  integrity sha512-RsJNyL58rwxtsjeMy34o8txkL6UlME1stWsUlRpTac6UE9Bx9gdJvnDXbIKhOJqBLX17fBjmposdrn6VTqim2w==

"@hapi/address@2.x.x":
  version "2.1.4"
  resolved "https://registry.npmmirror.com/@hapi/address/download/@hapi/address-2.1.4.tgz"
  integrity sha1-XWftQ/P9QaadS5/3tW58DR0KgeU=

"@hapi/bourne@1.x.x":
  version "1.3.2"
  resolved "https://registry.npmmirror.com/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz"
  integrity sha1-CnCVreoGckPOMoPhtWuKj0U7JCo=

"@hapi/hoek@8.x.x", "@hapi/hoek@^8.3.0":
  version "8.5.1"
  resolved "https://registry.npmmirror.com/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz"
  integrity sha1-/elgZMpEbeyMVajC8TCVewcMbgY=

"@hapi/joi@^15.0.1":
  version "15.1.1"
  resolved "https://registry.npmmirror.com/@hapi/joi/download/@hapi/joi-15.1.1.tgz"
  integrity sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc=
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  version "3.1.6"
  resolved "https://registry.nlark.com/@hapi/topo/download/@hapi/topo-3.1.6.tgz"
  integrity sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck=
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@intervolga/optimize-cssnano-plugin@^1.0.5":
  version "1.0.6"
  resolved "https://registry.nlark.com/@intervolga/optimize-cssnano-plugin/download/@intervolga/optimize-cssnano-plugin-1.0.6.tgz"
  integrity sha1-vnx4RhKLiPapsdEmGgrQbrXA/fg=
  dependencies:
    cssnano "^4.0.0"
    cssnano-preset-default "^4.0.0"
    postcss "^7.0.0"

"@mrmlnc/readdir-enhanced@^2.2.1":
  version "2.2.1"
  resolved "https://registry.nlark.com/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz"
  integrity sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4=
  dependencies:
    call-me-maybe "^1.0.1"
    glob-to-regexp "^0.3.0"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.nlark.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://registry.nlark.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.stat@^1.1.2":
  version "1.1.3"
  resolved "https://registry.nlark.com/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz"
  integrity sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.nlark.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@popperjs/core@^2.10.2":
  version "2.11.0"
  resolved "https://registry.npmmirror.com/@popperjs/core/-/core-2.11.0.tgz"
  integrity sha512-zrsUxjLOKAzdewIDRWy9nsV1GQsKBCWaGwsZQlCgr6/q+vjyZhFgqedLfFBuI9anTPEUT4APq9Mu0SZBTzIcGQ==

"@samverschueren/stream-to-observable@^0.3.0":
  version "0.3.1"
  resolved "https://registry.npmmirror.com/@samverschueren/stream-to-observable/download/@samverschueren/stream-to-observable-0.3.1.tgz"
  integrity sha1-ohEXsZ7pvnDDeewYd1N+8uHGMwE=
  dependencies:
    any-observable "^0.3.0"

"@soda/friendly-errors-webpack-plugin@^1.7.1":
  version "1.8.1"
  resolved "https://registry.npmmirror.com/@soda/friendly-errors-webpack-plugin/download/@soda/friendly-errors-webpack-plugin-1.8.1.tgz?cache=0&sync_timestamp=1636965503646&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40soda%2Ffriendly-errors-webpack-plugin%2Fdownload%2F%40soda%2Ffriendly-errors-webpack-plugin-1.8.1.tgz"
  integrity sha512-h2ooWqP8XuFqTXT+NyAFbrArzfQA7R6HTezADrvD9Re8fxMLTPPniLdqVTdDaO0eIoLaAwKT+d6w+5GeTk7Vbg==
  dependencies:
    chalk "^3.0.0"
    error-stack-parser "^2.0.6"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

"@soda/get-current-script@^1.0.0":
  version "1.0.2"
  resolved "https://registry.nlark.com/@soda/get-current-script/download/@soda/get-current-script-1.0.2.tgz"
  integrity sha1-pTUV2yXYA4N0OBtzryC7Ty5QjYc=

"@types/archiver@^5.1.1":
  version "5.1.1"
  resolved "https://registry.npmmirror.com/@types/archiver/-/archiver-5.1.1.tgz"
  integrity sha512-heuaCk0YH5m274NOLSi66H1zX6GtZoMsdE6TYFcpFFjBjg0FoU4i4/M/a/kNlgNg26Xk3g364mNOYe1JaiEPOQ==
  dependencies:
    "@types/glob" "*"

"@types/body-parser@*":
  version "1.19.2"
  resolved "https://registry.npmmirror.com/@types/body-parser/download/@types/body-parser-1.19.2.tgz?cache=0&sync_timestamp=1637264988188&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fbody-parser%2Fdownload%2F%40types%2Fbody-parser-1.19.2.tgz"
  integrity sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/connect-history-api-fallback@*":
  version "1.3.5"
  resolved "https://registry.npmmirror.com/@types/connect-history-api-fallback/download/@types/connect-history-api-fallback-1.3.5.tgz"
  integrity sha1-0feooJ0O1aV67lrpwYq5uAMgXa4=
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.35"
  resolved "https://registry.npmmirror.com/@types/connect/download/@types/connect-3.4.35.tgz"
  integrity sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE=
  dependencies:
    "@types/node" "*"

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.18":
  version "4.17.27"
  resolved "https://registry.npmmirror.com/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.27.tgz"
  integrity sha512-e/sVallzUTPdyOTiqi8O8pMdBBphscvI6E4JYaKlja4Lm+zh7UFSSdW5VMkRbhDtmrONqOUHOXRguPsDckzxNA==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*":
  version "4.17.13"
  resolved "https://registry.npmmirror.com/@types/express/download/@types/express-4.17.13.tgz"
  integrity sha1-p24plXKJmbq1GjP6vOHXBaNwkDQ=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.18"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/glob@*", "@types/glob@^7.1.1":
  version "7.2.0"
  resolved "https://registry.npmmirror.com/@types/glob/download/@types/glob-7.2.0.tgz"
  integrity sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/http-proxy@^1.17.5":
  version "1.17.8"
  resolved "https://registry.npmmirror.com/@types/http-proxy/download/@types/http-proxy-1.17.8.tgz"
  integrity sha512-5kPLG5BKpWYkw/LVOGWpiq3nEVqxiN32rTgI53Sk12/xHFQ2rG3ehI9IO+O3W2QoKeyB92dJkoka8SUm6BX1pA==
  dependencies:
    "@types/node" "*"

"@types/json-schema@^7.0.4", "@types/json-schema@^7.0.5", "@types/json-schema@^7.0.7", "@types/json-schema@^7.0.8":
  version "7.0.9"
  resolved "https://registry.npmmirror.com/@types/json-schema/download/@types/json-schema-7.0.9.tgz"
  integrity sha1-l+3JA36gw4WFMgsolk3eOznkZg0=

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://registry.nlark.com/@types/json5/download/@types/json5-0.0.29.tgz"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/mime@^1":
  version "1.3.2"
  resolved "https://registry.npmmirror.com/@types/mime/download/@types/mime-1.3.2.tgz?cache=0&sync_timestamp=1637267582862&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fmime%2Fdownload%2F%40types%2Fmime-1.3.2.tgz"
  integrity sha1-k+Jb+e51/g/YC1lLxP6w6GIRG1o=

"@types/minimatch@*":
  version "3.0.5"
  resolved "https://registry.npmmirror.com/@types/minimatch/download/@types/minimatch-3.0.5.tgz?cache=0&sync_timestamp=1637267467935&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fminimatch%2Fdownload%2F%40types%2Fminimatch-3.0.5.tgz"
  integrity sha1-EAHMXmo3BLg8I2An538vWOoBD0A=

"@types/minimist@^1.2.0":
  version "1.2.2"
  resolved "https://registry.npmmirror.com/@types/minimist/download/@types/minimist-1.2.2.tgz?cache=0&sync_timestamp=1637267478331&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fminimist%2Fdownload%2F%40types%2Fminimist-1.2.2.tgz"
  integrity sha1-7nceK6Sz3Fs3KTXVSf2WF780W4w=

"@types/node@*":
  version "17.0.5"
  resolved "https://registry.npmmirror.com/@types/node/download/@types/node-17.0.5.tgz"
  integrity sha512-w3mrvNXLeDYV1GKTZorGJQivK6XLCoGwpnyJFbJVK/aTBQUxOCaa/GlFAAN3OTDFcb7h5tiFG+YXCO2By+riZw==

"@types/normalize-package-data@^2.4.0":
  version "2.4.1"
  resolved "https://registry.npmmirror.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.1.tgz?cache=0&sync_timestamp=1637268963320&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnormalize-package-data%2Fdownload%2F%40types%2Fnormalize-package-data-2.4.1.tgz"
  integrity sha1-0zV0eaD9/dWQf+Z+F+CoXJBuEwE=

"@types/parse-json@^4.0.0":
  version "4.0.0"
  resolved "https://registry.npmmirror.com/@types/parse-json/download/@types/parse-json-4.0.0.tgz?cache=0&sync_timestamp=1637269948744&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fparse-json%2Fdownload%2F%40types%2Fparse-json-4.0.0.tgz"
  integrity sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA=

"@types/q@^1.5.1":
  version "1.5.5"
  resolved "https://registry.npmmirror.com/@types/q/download/@types/q-1.5.5.tgz?cache=0&sync_timestamp=1637268455272&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fq%2Fdownload%2F%40types%2Fq-1.5.5.tgz"
  integrity sha1-daKo59irSyMEFFBdkjNdHctTpt8=

"@types/qs@*":
  version "6.9.7"
  resolved "https://registry.npmmirror.com/@types/qs/download/@types/qs-6.9.7.tgz?cache=0&sync_timestamp=1637268454704&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fqs%2Fdownload%2F%40types%2Fqs-6.9.7.tgz"
  integrity sha1-Y7t9Bn2xB8weRXwwO8JdUR/r9ss=

"@types/range-parser@*":
  version "1.2.4"
  resolved "https://registry.npmmirror.com/@types/range-parser/download/@types/range-parser-1.2.4.tgz?cache=0&sync_timestamp=1637271181079&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Frange-parser%2Fdownload%2F%40types%2Frange-parser-1.2.4.tgz"
  integrity sha1-zWZ7z90CUhOq+3ylkVqTJZCs3Nw=

"@types/serve-static@*":
  version "1.13.10"
  resolved "https://registry.npmmirror.com/@types/serve-static/download/@types/serve-static-1.13.10.tgz?cache=0&sync_timestamp=1637270855648&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fserve-static%2Fdownload%2F%40types%2Fserve-static-1.13.10.tgz"
  integrity sha1-9eDOh5fS18xevtpIpSyWxPpHqNk=
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/source-list-map@*":
  version "0.1.2"
  resolved "https://registry.npmmirror.com/@types/source-list-map/download/@types/source-list-map-0.1.2.tgz"
  integrity sha1-AHiDYGP/rxdBI0m7o2QIfgrALsk=

"@types/tapable@^1":
  version "1.0.8"
  resolved "https://registry.npmmirror.com/@types/tapable/download/@types/tapable-1.0.8.tgz?cache=0&sync_timestamp=1637271209655&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Ftapable%2Fdownload%2F%40types%2Ftapable-1.0.8.tgz"
  integrity sha1-uUpDkchWZse3Mpn9OtedT6pDUxA=

"@types/uglify-js@*":
  version "3.13.1"
  resolved "https://registry.npmmirror.com/@types/uglify-js/download/@types/uglify-js-3.13.1.tgz"
  integrity sha1-XoienoHpQkXHW2RQYA4cXqKHiuo=
  dependencies:
    source-map "^0.6.1"

"@types/webpack-dev-server@^3.11.0":
  version "3.11.6"
  resolved "https://registry.npmmirror.com/@types/webpack-dev-server/download/@types/webpack-dev-server-3.11.6.tgz"
  integrity sha1-2IiM/S8GMCA+E9PteDOk0RuKNNw=
  dependencies:
    "@types/connect-history-api-fallback" "*"
    "@types/express" "*"
    "@types/serve-static" "*"
    "@types/webpack" "^4"
    http-proxy-middleware "^1.0.0"

"@types/webpack-env@^1.15.2":
  version "1.16.3"
  resolved "https://registry.npmmirror.com/@types/webpack-env/download/@types/webpack-env-1.16.3.tgz"
  integrity sha1-t3YyenPlYbceeIHQzW00oUJNuGo=

"@types/webpack-sources@*":
  version "3.2.0"
  resolved "https://registry.npmmirror.com/@types/webpack-sources/download/@types/webpack-sources-3.2.0.tgz"
  integrity sha1-FtdZuglsKJA0smVT0t8b9FJI04s=
  dependencies:
    "@types/node" "*"
    "@types/source-list-map" "*"
    source-map "^0.7.3"

"@types/webpack@^4", "@types/webpack@^4.0.0":
  version "4.41.32"
  resolved "https://registry.npmmirror.com/@types/webpack/download/@types/webpack-4.41.32.tgz"
  integrity sha512-cb+0ioil/7oz5//7tZUSwbrSAN/NWHrQylz5cW8G0dWTcF/g+/dSdMlKVZspBYuMAN1+WnwHrkxiRrLcwd0Heg==
  dependencies:
    "@types/node" "*"
    "@types/tapable" "^1"
    "@types/uglify-js" "*"
    "@types/webpack-sources" "*"
    anymatch "^3.0.0"
    source-map "^0.6.0"

"@typescript-eslint/eslint-plugin@^4.18.0":
  version "4.33.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-4.33.0.tgz"
  integrity sha1-wk3HyAacdwa8QNmfb6h+3LIAUnY=
  dependencies:
    "@typescript-eslint/experimental-utils" "4.33.0"
    "@typescript-eslint/scope-manager" "4.33.0"
    debug "^4.3.1"
    functional-red-black-tree "^1.0.1"
    ignore "^5.1.8"
    regexpp "^3.1.0"
    semver "^7.3.5"
    tsutils "^3.21.0"

"@typescript-eslint/experimental-utils@4.33.0":
  version "4.33.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-4.33.0.tgz"
  integrity sha1-byp4akIJ+iIimJ6TgLUzGygQ9/0=
  dependencies:
    "@types/json-schema" "^7.0.7"
    "@typescript-eslint/scope-manager" "4.33.0"
    "@typescript-eslint/types" "4.33.0"
    "@typescript-eslint/typescript-estree" "4.33.0"
    eslint-scope "^5.1.1"
    eslint-utils "^3.0.0"

"@typescript-eslint/parser@^4.18.0":
  version "4.33.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/parser/download/@typescript-eslint/parser-4.33.0.tgz"
  integrity sha1-3+eXVw2WlOVgUo0Y7srYbIx0SJk=
  dependencies:
    "@typescript-eslint/scope-manager" "4.33.0"
    "@typescript-eslint/types" "4.33.0"
    "@typescript-eslint/typescript-estree" "4.33.0"
    debug "^4.3.1"

"@typescript-eslint/scope-manager@4.33.0":
  version "4.33.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-4.33.0.tgz"
  integrity sha1-045JKA2YPody4pEhz4xukiHygKM=
  dependencies:
    "@typescript-eslint/types" "4.33.0"
    "@typescript-eslint/visitor-keys" "4.33.0"

"@typescript-eslint/types@4.33.0":
  version "4.33.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/types/download/@typescript-eslint/types-4.33.0.tgz"
  integrity sha1-oeWQNqO1OuhDDO6/KpGdx/mvbXI=

"@typescript-eslint/typescript-estree@4.33.0":
  version "4.33.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-4.33.0.tgz"
  integrity sha1-DftRwpCPaMXAjYKu/q8WahfCRgk=
  dependencies:
    "@typescript-eslint/types" "4.33.0"
    "@typescript-eslint/visitor-keys" "4.33.0"
    debug "^4.3.1"
    globby "^11.0.3"
    is-glob "^4.0.1"
    semver "^7.3.5"
    tsutils "^3.21.0"

"@typescript-eslint/visitor-keys@4.33.0":
  version "4.33.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-4.33.0.tgz"
  integrity sha1-KiL3ekFgQom3oYZYbp7EjKku8d0=
  dependencies:
    "@typescript-eslint/types" "4.33.0"
    eslint-visitor-keys "^2.0.0"

"@vue/babel-helper-vue-jsx-merge-props@^1.2.1":
  version "1.2.1"
  resolved "https://registry.nlark.com/@vue/babel-helper-vue-jsx-merge-props/download/@vue/babel-helper-vue-jsx-merge-props-1.2.1.tgz"
  integrity sha1-MWJKelBfsU2h1YAjclpMXycOaoE=

"@vue/babel-helper-vue-transform-on@^1.0.2":
  version "1.0.2"
  resolved "https://registry.nlark.com/@vue/babel-helper-vue-transform-on/download/@vue/babel-helper-vue-transform-on-1.0.2.tgz"
  integrity sha1-m5xpHNBvyFUiGiR1w8yDHXdLx9w=

"@vue/babel-plugin-jsx@^1.0.3":
  version "1.1.1"
  resolved "https://registry.npmmirror.com/@vue/babel-plugin-jsx/download/@vue/babel-plugin-jsx-1.1.1.tgz?cache=0&sync_timestamp=1634464392976&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fbabel-plugin-jsx%2Fdownload%2F%40vue%2Fbabel-plugin-jsx-1.1.1.tgz"
  integrity sha1-DFusJ4gNI/iYlM0Daje1XvYd38E=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    "@vue/babel-helper-vue-transform-on" "^1.0.2"
    camelcase "^6.0.0"
    html-tags "^3.1.0"
    svg-tags "^1.0.0"

"@vue/babel-plugin-transform-vue-jsx@^1.2.1":
  version "1.2.1"
  resolved "https://registry.nlark.com/@vue/babel-plugin-transform-vue-jsx/download/@vue/babel-plugin-transform-vue-jsx-1.2.1.tgz"
  integrity sha1-ZGBGxlLC8CQnJ/NFGdkXsGQEHtc=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.2.1"
    html-tags "^2.0.0"
    lodash.kebabcase "^4.1.1"
    svg-tags "^1.0.0"

"@vue/babel-preset-app@^4.5.15":
  version "4.5.15"
  resolved "https://registry.npmmirror.com/@vue/babel-preset-app/download/@vue/babel-preset-app-4.5.15.tgz"
  integrity sha512-J+YttzvwRfV1BPczf8r3qCevznYk+jh531agVF+5EYlHF4Sgh/cGXTz9qkkiux3LQgvhEGXgmCteg1n38WuuKg==
  dependencies:
    "@babel/core" "^7.11.0"
    "@babel/helper-compilation-targets" "^7.9.6"
    "@babel/helper-module-imports" "^7.8.3"
    "@babel/plugin-proposal-class-properties" "^7.8.3"
    "@babel/plugin-proposal-decorators" "^7.8.3"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-jsx" "^7.8.3"
    "@babel/plugin-transform-runtime" "^7.11.0"
    "@babel/preset-env" "^7.11.0"
    "@babel/runtime" "^7.11.0"
    "@vue/babel-plugin-jsx" "^1.0.3"
    "@vue/babel-preset-jsx" "^1.2.4"
    babel-plugin-dynamic-import-node "^2.3.3"
    core-js "^3.6.5"
    core-js-compat "^3.6.5"
    semver "^6.1.0"

"@vue/babel-preset-jsx@^1.2.4":
  version "1.2.4"
  resolved "https://registry.nlark.com/@vue/babel-preset-jsx/download/@vue/babel-preset-jsx-1.2.4.tgz"
  integrity sha1-kv6nnbbxOwHoDToAmeKSS9y+Toc=
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.2.1"
    "@vue/babel-plugin-transform-vue-jsx" "^1.2.1"
    "@vue/babel-sugar-composition-api-inject-h" "^1.2.1"
    "@vue/babel-sugar-composition-api-render-instance" "^1.2.4"
    "@vue/babel-sugar-functional-vue" "^1.2.2"
    "@vue/babel-sugar-inject-h" "^1.2.2"
    "@vue/babel-sugar-v-model" "^1.2.3"
    "@vue/babel-sugar-v-on" "^1.2.3"

"@vue/babel-sugar-composition-api-inject-h@^1.2.1":
  version "1.2.1"
  resolved "https://registry.nlark.com/@vue/babel-sugar-composition-api-inject-h/download/@vue/babel-sugar-composition-api-inject-h-1.2.1.tgz"
  integrity sha1-BdbgxDJxDjdYKyvppgSbaJtvA+s=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-composition-api-render-instance@^1.2.4":
  version "1.2.4"
  resolved "https://registry.nlark.com/@vue/babel-sugar-composition-api-render-instance/download/@vue/babel-sugar-composition-api-render-instance-1.2.4.tgz"
  integrity sha1-5MvGmXw0T6wnF4WteikyXFHWjRk=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-functional-vue@^1.2.2":
  version "1.2.2"
  resolved "https://registry.nlark.com/@vue/babel-sugar-functional-vue/download/@vue/babel-sugar-functional-vue-1.2.2.tgz"
  integrity sha1-JnqayNeHyW7b8Dzj85LEnam9Jlg=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.2.2":
  version "1.2.2"
  resolved "https://registry.nlark.com/@vue/babel-sugar-inject-h/download/@vue/babel-sugar-inject-h-1.2.2.tgz"
  integrity sha1-1zjTyJM2fshJHcu2abAAkZKT46o=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.2.3":
  version "1.2.3"
  resolved "https://registry.nlark.com/@vue/babel-sugar-v-model/download/@vue/babel-sugar-v-model-1.2.3.tgz"
  integrity sha1-+h8pulHr8KoabDX6ZtU5vEWaGPI=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.2.1"
    "@vue/babel-plugin-transform-vue-jsx" "^1.2.1"
    camelcase "^5.0.0"
    html-tags "^2.0.0"
    svg-tags "^1.0.0"

"@vue/babel-sugar-v-on@^1.2.3":
  version "1.2.3"
  resolved "https://registry.nlark.com/@vue/babel-sugar-v-on/download/@vue/babel-sugar-v-on-1.2.3.tgz"
  integrity sha1-NCNnF4WGpp85LwS/ujICHQKROto=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.2.1"
    camelcase "^5.0.0"

"@vue/cli-overlay@^4.5.15":
  version "4.5.15"
  resolved "https://registry.npmmirror.com/@vue/cli-overlay/download/@vue/cli-overlay-4.5.15.tgz?cache=0&sync_timestamp=1637121070343&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fcli-overlay%2Fdownload%2F%40vue%2Fcli-overlay-4.5.15.tgz"
  integrity sha1-BwD9a605M21Bibo/99JeY46BjJw=

"@vue/cli-plugin-babel@~4.5.0":
  version "4.5.15"
  resolved "https://registry.npmmirror.com/@vue/cli-plugin-babel/download/@vue/cli-plugin-babel-4.5.15.tgz?cache=0&sync_timestamp=1637121137770&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fcli-plugin-babel%2Fdownload%2F%40vue%2Fcli-plugin-babel-4.5.15.tgz"
  integrity sha1-rk+y7VQlX+PYTfOB2raFCWQRee0=
  dependencies:
    "@babel/core" "^7.11.0"
    "@vue/babel-preset-app" "^4.5.15"
    "@vue/cli-shared-utils" "^4.5.15"
    babel-loader "^8.1.0"
    cache-loader "^4.1.0"
    thread-loader "^2.1.3"
    webpack "^4.0.0"

"@vue/cli-plugin-eslint@~4.5.0":
  version "4.5.15"
  resolved "https://registry.npmmirror.com/@vue/cli-plugin-eslint/download/@vue/cli-plugin-eslint-4.5.15.tgz?cache=0&sync_timestamp=1637130364100&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fcli-plugin-eslint%2Fdownload%2F%40vue%2Fcli-plugin-eslint-4.5.15.tgz"
  integrity sha1-V4GCSpQfNMJjNqZ7H2WEoGxqJP8=
  dependencies:
    "@vue/cli-shared-utils" "^4.5.15"
    eslint-loader "^2.2.1"
    globby "^9.2.0"
    inquirer "^7.1.0"
    webpack "^4.0.0"
    yorkie "^2.0.0"

"@vue/cli-plugin-router@^4.5.15", "@vue/cli-plugin-router@~4.5.0":
  version "4.5.15"
  resolved "https://registry.npmmirror.com/@vue/cli-plugin-router/download/@vue/cli-plugin-router-4.5.15.tgz?cache=0&sync_timestamp=1637121138678&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fcli-plugin-router%2Fdownload%2F%40vue%2Fcli-plugin-router-4.5.15.tgz"
  integrity sha1-HnXIyJ30LGlPFDufECjePPXWHh4=
  dependencies:
    "@vue/cli-shared-utils" "^4.5.15"

"@vue/cli-plugin-typescript@~4.5.0":
  version "4.5.15"
  resolved "https://registry.npmmirror.com/@vue/cli-plugin-typescript/download/@vue/cli-plugin-typescript-4.5.15.tgz?cache=0&sync_timestamp=1637130365188&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fcli-plugin-typescript%2Fdownload%2F%40vue%2Fcli-plugin-typescript-4.5.15.tgz"
  integrity sha1-FLoN3PO5TnMUj/hKyQT7xTRsFNE=
  dependencies:
    "@types/webpack-env" "^1.15.2"
    "@vue/cli-shared-utils" "^4.5.15"
    cache-loader "^4.1.0"
    fork-ts-checker-webpack-plugin "^3.1.1"
    globby "^9.2.0"
    thread-loader "^2.1.3"
    ts-loader "^6.2.2"
    tslint "^5.20.1"
    webpack "^4.0.0"
    yorkie "^2.0.0"
  optionalDependencies:
    fork-ts-checker-webpack-plugin-v5 "npm:fork-ts-checker-webpack-plugin@^5.0.11"

"@vue/cli-plugin-vuex@^4.5.15", "@vue/cli-plugin-vuex@~4.5.0":
  version "4.5.15"
  resolved "https://registry.npmmirror.com/@vue/cli-plugin-vuex/download/@vue/cli-plugin-vuex-4.5.15.tgz?cache=0&sync_timestamp=1637131562626&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fcli-plugin-vuex%2Fdownload%2F%40vue%2Fcli-plugin-vuex-4.5.15.tgz"
  integrity sha1-RmwfAnd9Av71Opu0mjbMOjvP7E4=

"@vue/cli-service@~4.5.0":
  version "4.5.15"
  resolved "https://registry.npmmirror.com/@vue/cli-service/download/@vue/cli-service-4.5.15.tgz"
  integrity sha1-DpoYbVFVACfQ5o6VBCB3600RW0U=
  dependencies:
    "@intervolga/optimize-cssnano-plugin" "^1.0.5"
    "@soda/friendly-errors-webpack-plugin" "^1.7.1"
    "@soda/get-current-script" "^1.0.0"
    "@types/minimist" "^1.2.0"
    "@types/webpack" "^4.0.0"
    "@types/webpack-dev-server" "^3.11.0"
    "@vue/cli-overlay" "^4.5.15"
    "@vue/cli-plugin-router" "^4.5.15"
    "@vue/cli-plugin-vuex" "^4.5.15"
    "@vue/cli-shared-utils" "^4.5.15"
    "@vue/component-compiler-utils" "^3.1.2"
    "@vue/preload-webpack-plugin" "^1.1.0"
    "@vue/web-component-wrapper" "^1.2.0"
    acorn "^7.4.0"
    acorn-walk "^7.1.1"
    address "^1.1.2"
    autoprefixer "^9.8.6"
    browserslist "^4.12.0"
    cache-loader "^4.1.0"
    case-sensitive-paths-webpack-plugin "^2.3.0"
    cli-highlight "^2.1.4"
    clipboardy "^2.3.0"
    cliui "^6.0.0"
    copy-webpack-plugin "^5.1.1"
    css-loader "^3.5.3"
    cssnano "^4.1.10"
    debug "^4.1.1"
    default-gateway "^5.0.5"
    dotenv "^8.2.0"
    dotenv-expand "^5.1.0"
    file-loader "^4.2.0"
    fs-extra "^7.0.1"
    globby "^9.2.0"
    hash-sum "^2.0.0"
    html-webpack-plugin "^3.2.0"
    launch-editor-middleware "^2.2.1"
    lodash.defaultsdeep "^4.6.1"
    lodash.mapvalues "^4.6.0"
    lodash.transform "^4.6.0"
    mini-css-extract-plugin "^0.9.0"
    minimist "^1.2.5"
    pnp-webpack-plugin "^1.6.4"
    portfinder "^1.0.26"
    postcss-loader "^3.0.0"
    ssri "^8.0.1"
    terser-webpack-plugin "^1.4.4"
    thread-loader "^2.1.3"
    url-loader "^2.2.0"
    vue-loader "^15.9.2"
    vue-style-loader "^4.1.2"
    webpack "^4.0.0"
    webpack-bundle-analyzer "^3.8.0"
    webpack-chain "^6.4.0"
    webpack-dev-server "^3.11.0"
    webpack-merge "^4.2.2"
  optionalDependencies:
    vue-loader-v16 "npm:vue-loader@^16.1.0"

"@vue/cli-shared-utils@^4.5.15":
  version "4.5.15"
  resolved "https://registry.npmmirror.com/@vue/cli-shared-utils/download/@vue/cli-shared-utils-4.5.15.tgz?cache=0&sync_timestamp=1637121137255&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fcli-shared-utils%2Fdownload%2F%40vue%2Fcli-shared-utils-4.5.15.tgz"
  integrity sha1-26OFgWXb40ZXVfJWpIkOaQhFMtY=
  dependencies:
    "@hapi/joi" "^15.0.1"
    chalk "^2.4.2"
    execa "^1.0.0"
    launch-editor "^2.2.1"
    lru-cache "^5.1.1"
    node-ipc "^9.1.1"
    open "^6.3.0"
    ora "^3.4.0"
    read-pkg "^5.1.1"
    request "^2.88.2"
    semver "^6.1.0"
    strip-ansi "^6.0.0"

"@vue/compiler-core@3.2.26":
  version "3.2.26"
  resolved "https://registry.npmmirror.com/@vue/compiler-core/download/@vue/compiler-core-3.2.26.tgz"
  integrity sha512-N5XNBobZbaASdzY9Lga2D9Lul5vdCIOXvUMd6ThcN8zgqQhPKfCV+wfAJNNJKQkSHudnYRO2gEB+lp0iN3g2Tw==
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/shared" "3.2.26"
    estree-walker "^2.0.2"
    source-map "^0.6.1"

"@vue/compiler-dom@3.2.26":
  version "3.2.26"
  resolved "https://registry.npmmirror.com/@vue/compiler-dom/download/@vue/compiler-dom-3.2.26.tgz"
  integrity sha512-smBfaOW6mQDxcT3p9TKT6mE22vjxjJL50GFVJiI0chXYGU/xzC05QRGrW3HHVuJrmLTLx5zBhsZ2dIATERbarg==
  dependencies:
    "@vue/compiler-core" "3.2.26"
    "@vue/shared" "3.2.26"

"@vue/compiler-sfc@3.2.26", "@vue/compiler-sfc@^3.0.0":
  version "3.2.26"
  resolved "https://registry.npmmirror.com/@vue/compiler-sfc/download/@vue/compiler-sfc-3.2.26.tgz"
  integrity sha512-ePpnfktV90UcLdsDQUh2JdiTuhV0Skv2iYXxfNMOK/F3Q+2BO0AulcVcfoksOpTJGmhhfosWfMyEaEf0UaWpIw==
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/compiler-core" "3.2.26"
    "@vue/compiler-dom" "3.2.26"
    "@vue/compiler-ssr" "3.2.26"
    "@vue/reactivity-transform" "3.2.26"
    "@vue/shared" "3.2.26"
    estree-walker "^2.0.2"
    magic-string "^0.25.7"
    postcss "^8.1.10"
    source-map "^0.6.1"

"@vue/compiler-ssr@3.2.26":
  version "3.2.26"
  resolved "https://registry.npmmirror.com/@vue/compiler-ssr/download/@vue/compiler-ssr-3.2.26.tgz"
  integrity sha512-2mywLX0ODc4Zn8qBoA2PDCsLEZfpUGZcyoFRLSOjyGGK6wDy2/5kyDOWtf0S0UvtoyVq95OTSGIALjZ4k2q/ag==
  dependencies:
    "@vue/compiler-dom" "3.2.26"
    "@vue/shared" "3.2.26"

"@vue/component-compiler-utils@^3.1.0", "@vue/component-compiler-utils@^3.1.2":
  version "3.3.0"
  resolved "https://registry.npmmirror.com/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.3.0.tgz"
  integrity sha1-+fX7U0ZLDDeyyNLz+/5E32D2Hck=
  dependencies:
    consolidate "^0.15.1"
    hash-sum "^1.0.2"
    lru-cache "^4.1.2"
    merge-source-map "^1.1.0"
    postcss "^7.0.36"
    postcss-selector-parser "^6.0.2"
    source-map "~0.6.1"
    vue-template-es2015-compiler "^1.9.0"
  optionalDependencies:
    prettier "^1.18.2 || ^2.0.0"

"@vue/devtools-api@^6.0.0-beta.11", "@vue/devtools-api@^6.0.0-beta.18":
  version "6.0.0-beta.21.1"
  resolved "https://registry.npmmirror.com/@vue/devtools-api/download/@vue/devtools-api-6.0.0-beta.21.1.tgz"
  integrity sha512-FqC4s3pm35qGVeXRGOjTsRzlkJjrBLriDS9YXbflHLsfA9FrcKzIyWnLXoNm+/7930E8rRakXuAc2QkC50swAw==

"@vue/eslint-config-standard@^5.1.2":
  version "5.1.2"
  resolved "https://registry.npmmirror.com/@vue/eslint-config-standard/download/@vue/eslint-config-standard-5.1.2.tgz"
  integrity sha1-xdVa+JSjriO2Wxr0pCV3esAXC0I=
  dependencies:
    eslint-config-standard "^14.1.0"
    eslint-import-resolver-node "^0.3.3"
    eslint-import-resolver-webpack "^0.12.1"

"@vue/eslint-config-typescript@^7.0.0":
  version "7.0.0"
  resolved "https://registry.npmmirror.com/@vue/eslint-config-typescript/download/@vue/eslint-config-typescript-7.0.0.tgz"
  integrity sha1-Igxwwu33olPnOSmFJfTUAbjvADg=
  dependencies:
    vue-eslint-parser "^7.0.0"

"@vue/preload-webpack-plugin@^1.1.0":
  version "1.1.2"
  resolved "https://registry.nlark.com/@vue/preload-webpack-plugin/download/@vue/preload-webpack-plugin-1.1.2.tgz"
  integrity sha1-zrkktOyzucQ4ccekKaAvhCPmIas=

"@vue/reactivity-transform@3.2.26":
  version "3.2.26"
  resolved "https://registry.npmmirror.com/@vue/reactivity-transform/download/@vue/reactivity-transform-3.2.26.tgz"
  integrity sha512-XKMyuCmzNA7nvFlYhdKwD78rcnmPb7q46uoR00zkX6yZrUmcCQ5OikiwUEVbvNhL5hBJuvbSO95jB5zkUon+eQ==
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/compiler-core" "3.2.26"
    "@vue/shared" "3.2.26"
    estree-walker "^2.0.2"
    magic-string "^0.25.7"

"@vue/reactivity@3.2.26":
  version "3.2.26"
  resolved "https://registry.npmmirror.com/@vue/reactivity/download/@vue/reactivity-3.2.26.tgz"
  integrity sha512-h38bxCZLW6oFJVDlCcAiUKFnXI8xP8d+eO0pcDxx+7dQfSPje2AO6M9S9QO6MrxQB7fGP0DH0dYQ8ksf6hrXKQ==
  dependencies:
    "@vue/shared" "3.2.26"

"@vue/runtime-core@3.2.26":
  version "3.2.26"
  resolved "https://registry.npmmirror.com/@vue/runtime-core/download/@vue/runtime-core-3.2.26.tgz"
  integrity sha512-BcYi7qZ9Nn+CJDJrHQ6Zsmxei2hDW0L6AB4vPvUQGBm2fZyC0GXd/4nVbyA2ubmuhctD5RbYY8L+5GUJszv9mQ==
  dependencies:
    "@vue/reactivity" "3.2.26"
    "@vue/shared" "3.2.26"

"@vue/runtime-dom@3.2.26":
  version "3.2.26"
  resolved "https://registry.npmmirror.com/@vue/runtime-dom/download/@vue/runtime-dom-3.2.26.tgz"
  integrity sha512-dY56UIiZI+gjc4e8JQBwAifljyexfVCkIAu/WX8snh8vSOt/gMSEGwPRcl2UpYpBYeyExV8WCbgvwWRNt9cHhQ==
  dependencies:
    "@vue/runtime-core" "3.2.26"
    "@vue/shared" "3.2.26"
    csstype "^2.6.8"

"@vue/server-renderer@3.2.26":
  version "3.2.26"
  resolved "https://registry.npmmirror.com/@vue/server-renderer/download/@vue/server-renderer-3.2.26.tgz"
  integrity sha512-Jp5SggDUvvUYSBIvYEhy76t4nr1vapY/FIFloWmQzn7UxqaHrrBpbxrqPcTrSgGrcaglj0VBp22BKJNre4aA1w==
  dependencies:
    "@vue/compiler-ssr" "3.2.26"
    "@vue/shared" "3.2.26"

"@vue/shared@3.2.26":
  version "3.2.26"
  resolved "https://registry.npmmirror.com/@vue/shared/download/@vue/shared-3.2.26.tgz"
  integrity sha512-vPV6Cq+NIWbH5pZu+V+2QHE9y1qfuTq49uNWw4f7FDEeZaDU2H2cx5jcUZOAKW7qTrUS4k6qZPbMy1x4N96nbA==

"@vue/web-component-wrapper@^1.2.0":
  version "1.3.0"
  resolved "https://registry.nlark.com/@vue/web-component-wrapper/download/@vue/web-component-wrapper-1.3.0.tgz"
  integrity sha1-trQKdiVCnSvXwigd26YB7QXcfxo=

"@vueuse/core@^7.3.0":
  version "7.4.3"
  resolved "https://registry.npmmirror.com/@vueuse/core/-/core-7.4.3.tgz"
  integrity sha512-OflPLlQ7eqaDJgKYw9DpRCbtfpkFkg1EqHFq7CFrbIEElB39wIfB9ccNypCar8e5KRDrAbOJLIOOvDZO6aC2Gg==
  dependencies:
    "@vueuse/shared" "7.4.3"
    vue-demi "*"

"@vueuse/shared@7.4.3":
  version "7.4.3"
  resolved "https://registry.npmmirror.com/@vueuse/shared/-/shared-7.4.3.tgz"
  integrity sha512-tNOCbpD3uZyzxNuw2GX5LTVkMB//IZ7GS452F7TwlY07aIoGjPgixi2Yfk/xSR4CcC9iU56zprZW33aEGj1O1Q==
  dependencies:
    vue-demi "*"

"@webassemblyjs/ast@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.9.0.tgz?cache=0&sync_timestamp=1625473420080&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fast%2Fdownload%2F%40webassemblyjs%2Fast-1.9.0.tgz"
  integrity sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=
  dependencies:
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"

"@webassemblyjs/floating-point-hex-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.9.0.tgz"
  integrity sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q=

"@webassemblyjs/helper-api-error@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.9.0.tgz?cache=0&sync_timestamp=1625473346773&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-api-error%2Fdownload%2F%40webassemblyjs%2Fhelper-api-error-1.9.0.tgz"
  integrity sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI=

"@webassemblyjs/helper-buffer@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.9.0.tgz"
  integrity sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA=

"@webassemblyjs/helper-code-frame@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.9.0.tgz?cache=0&sync_timestamp=1625473466896&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-code-frame%2Fdownload%2F%40webassemblyjs%2Fhelper-code-frame-1.9.0.tgz"
  integrity sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=
  dependencies:
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/helper-fsm@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.9.0.tgz"
  integrity sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg=

"@webassemblyjs/helper-module-context@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.9.0.tgz"
  integrity sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"

"@webassemblyjs/helper-wasm-bytecode@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.9.0.tgz"
  integrity sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A=

"@webassemblyjs/helper-wasm-section@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.9.0.tgz?cache=0&sync_timestamp=1625473466570&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-wasm-section%2Fdownload%2F%40webassemblyjs%2Fhelper-wasm-section-1.9.0.tgz"
  integrity sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"

"@webassemblyjs/ieee754@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.9.0.tgz"
  integrity sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.9.0.tgz"
  integrity sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.9.0.tgz"
  integrity sha1-BNM7Y2945qaBMifoJAL3Y3tiKas=

"@webassemblyjs/wasm-edit@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.9.0.tgz?cache=0&sync_timestamp=1625473422937&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwasm-edit%2Fdownload%2F%40webassemblyjs%2Fwasm-edit-1.9.0.tgz"
  integrity sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/helper-wasm-section" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-opt" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/wasm-gen@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.9.0.tgz?cache=0&sync_timestamp=1625473464969&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwasm-gen%2Fdownload%2F%40webassemblyjs%2Fwasm-gen-1.9.0.tgz"
  integrity sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wasm-opt@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.9.0.tgz?cache=0&sync_timestamp=1625473467198&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwasm-opt%2Fdownload%2F%40webassemblyjs%2Fwasm-opt-1.9.0.tgz"
  integrity sha1-IhEYHlsxMmRDzIES658LkChyGmE=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"

"@webassemblyjs/wasm-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.9.0.tgz?cache=0&sync_timestamp=1625473417746&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwasm-parser%2Fdownload%2F%40webassemblyjs%2Fwasm-parser-1.9.0.tgz"
  integrity sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wast-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.9.0.tgz?cache=0&sync_timestamp=1625473467847&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwast-parser%2Fdownload%2F%40webassemblyjs%2Fwast-parser-1.9.0.tgz"
  integrity sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/floating-point-hex-parser" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-code-frame" "1.9.0"
    "@webassemblyjs/helper-fsm" "1.9.0"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.9.0":
  version "1.9.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.9.0.tgz?cache=0&sync_timestamp=1625473465901&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwast-printer%2Fdownload%2F%40webassemblyjs%2Fwast-printer-1.9.0.tgz"
  integrity sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://registry.nlark.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://registry.nlark.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

abbrev@1:
  version "1.1.1"
  resolved "https://registry.nlark.com/abbrev/download/abbrev-1.1.1.tgz"
  integrity sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.7:
  version "1.3.7"
  resolved "https://registry.nlark.com/accepts/download/accepts-1.3.7.tgz"
  integrity sha1-UxvHJlF6OytB+FACHGzBXqq1B80=
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-jsx@^5.2.0, acorn-jsx@^5.3.1:
  version "5.3.2"
  resolved "https://registry.nlark.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz?cache=0&sync_timestamp=1625793705701&other_urls=https%3A%2F%2Fregistry.nlark.com%2Facorn-jsx%2Fdownload%2Facorn-jsx-5.3.2.tgz"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^7.1.1:
  version "7.2.0"
  resolved "https://registry.nlark.com/acorn-walk/download/acorn-walk-7.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Facorn-walk%2Fdownload%2Facorn-walk-7.2.0.tgz"
  integrity sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=

acorn@^6.4.1:
  version "6.4.2"
  resolved "https://registry.npmmirror.com/acorn/download/acorn-6.4.2.tgz"
  integrity sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=

acorn@^7.1.1, acorn@^7.4.0:
  version "7.4.1"
  resolved "https://registry.npmmirror.com/acorn/download/acorn-7.4.1.tgz"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

acorn@^8.7.0:
  version "8.7.0"
  resolved "https://registry.npmmirror.com/acorn/-/acorn-8.7.0.tgz"
  integrity sha512-V/LGr1APy+PXIwKebEWrkZPwoeoF+w1jiOBUmuxuiUIaOHtob8Qc9BTrYo7VuI5fR8tqsy+buA2WFooR5olqvQ==

address@^1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/address/download/address-1.1.2.tgz"
  integrity sha1-vxEWycdYxRt6kz0pa3LCIe2UKLY=

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/aggregate-error/download/aggregate-error-3.1.0.tgz"
  integrity sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv-errors@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/ajv-errors/download/ajv-errors-1.0.1.tgz"
  integrity sha1-81mGrOuRr63sQQL72FAUlQzvpk0=

ajv-keywords@^3.1.0, ajv-keywords@^3.4.1, ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://registry.npmmirror.com/ajv-keywords/download/ajv-keywords-3.5.2.tgz"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv@^6.1.0, ajv@^6.10.0, ajv@^6.10.2, ajv@^6.12.2, ajv@^6.12.3, ajv@^6.12.4, ajv@^6.12.5:
  version "6.12.6"
  resolved "https://registry.npmmirror.com/ajv/download/ajv-6.12.6.tgz"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

alphanum-sort@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/alphanum-sort/download/alphanum-sort-1.0.2.tgz"
  integrity sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://registry.nlark.com/amdefine/download/amdefine-1.0.1.tgz"
  integrity sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=

ansi-colors@^3.0.0:
  version "3.2.4"
  resolved "https://registry.nlark.com/ansi-colors/download/ansi-colors-3.2.4.tgz"
  integrity sha1-46PaS/uubIapwoViXeEkojQCb78=

ansi-escapes@^3.0.0:
  version "3.2.0"
  resolved "https://registry.nlark.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz"
  integrity sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "https://registry.nlark.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-html-community@0.0.8:
  version "0.0.8"
  resolved "https://registry.nlark.com/ansi-html-community/download/ansi-html-community-0.0.8.tgz"
  integrity sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-2.1.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-regex%2Fdownload%2Fansi-regex-2.1.1.tgz"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-3.0.0.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-regex%2Fdownload%2Fansi-regex-3.0.0.tgz"
  integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=

ansi-regex@^4.1.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-4.1.0.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-regex%2Fdownload%2Fansi-regex-4.1.0.tgz"
  integrity sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-5.0.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-regex%2Fdownload%2Fansi-regex-5.0.1.tgz"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-2.2.1.tgz"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-3.2.1.tgz"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

any-observable@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/any-observable/download/any-observable-0.3.0.tgz"
  integrity sha1-r5M0deWAamfQ198JDdXovvZdEZs=

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/any-promise/download/any-promise-1.3.0.tgz"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/anymatch/download/anymatch-2.0.0.tgz"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@^3.0.0, anymatch@~3.1.2:
  version "3.1.2"
  resolved "https://registry.nlark.com/anymatch/download/anymatch-3.1.2.tgz"
  integrity sha1-wFV8CWrzLxBhmPT04qODU343hxY=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

aproba@^1.0.3, aproba@^1.1.1:
  version "1.2.0"
  resolved "https://registry.nlark.com/aproba/download/aproba-1.2.0.tgz"
  integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=

arch@^2.1.1:
  version "2.2.0"
  resolved "https://registry.nlark.com/arch/download/arch-2.2.0.tgz"
  integrity sha1-G8R4GPMFdk8jqzMGsL/AhsWinRE=

archiver-utils@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/archiver-utils/-/archiver-utils-2.1.0.tgz"
  integrity sha512-bEL/yUb/fNNiNTuUz979Z0Yg5L+LzLxGJz8x79lYmR54fmTIb6ob/hNQgkQnIUDWIFjZVQwl9Xs356I6BAMHfw==
  dependencies:
    glob "^7.1.4"
    graceful-fs "^4.2.0"
    lazystream "^1.0.0"
    lodash.defaults "^4.2.0"
    lodash.difference "^4.5.0"
    lodash.flatten "^4.4.0"
    lodash.isplainobject "^4.0.6"
    lodash.union "^4.6.0"
    normalize-path "^3.0.0"
    readable-stream "^2.0.0"

archiver@^5.3.0:
  version "5.3.0"
  resolved "https://registry.npmmirror.com/archiver/-/archiver-5.3.0.tgz"
  integrity sha512-iUw+oDwK0fgNpvveEsdQ0Ase6IIKztBJU2U0E9MzszMfmVVUyv1QJhS2ITW9ZCqx8dktAxVAjWWkKehuZE8OPg==
  dependencies:
    archiver-utils "^2.1.0"
    async "^3.2.0"
    buffer-crc32 "^0.2.1"
    readable-stream "^3.6.0"
    readdir-glob "^1.0.0"
    tar-stream "^2.2.0"
    zip-stream "^4.1.0"

are-we-there-yet@~1.1.2:
  version "1.1.7"
  resolved "https://registry.nlark.com/are-we-there-yet/download/are-we-there-yet-1.1.7.tgz?cache=0&sync_timestamp=1630592923226&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fare-we-there-yet%2Fdownload%2Fare-we-there-yet-1.1.7.tgz"
  integrity sha1-sVR0qTKtq0/4pQ2a36fk6SbyEUY=
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.nlark.com/argparse/download/argparse-1.0.10.tgz"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/arr-diff/download/arr-diff-4.0.0.tgz"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/arr-flatten/download/arr-flatten-1.1.0.tgz"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/arr-union/download/arr-union-3.1.0.tgz"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "https://registry.nlark.com/array-find-index/download/array-find-index-1.0.2.tgz"
  integrity sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=

array-find@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/array-find/download/array-find-1.0.0.tgz"
  integrity sha1-bI4obRHtdoMn+OYuzuhzU8o+eLg=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/array-flatten/download/array-flatten-1.1.1.tgz"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-flatten@^2.1.0:
  version "2.1.2"
  resolved "https://registry.nlark.com/array-flatten/download/array-flatten-2.1.2.tgz"
  integrity sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=

array-includes@^3.1.4:
  version "3.1.4"
  resolved "https://registry.npmmirror.com/array-includes/download/array-includes-3.1.4.tgz"
  integrity sha1-9bSTFix2DzU5Yx8AW6K7Rqy0W6k=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"
    get-intrinsic "^1.1.1"
    is-string "^1.0.7"

array-union@^1.0.1, array-union@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/array-union/download/array-union-1.0.2.tgz"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/array-union/download/array-union-2.1.0.tgz"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://registry.nlark.com/array-uniq/download/array-uniq-1.0.3.tgz"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "https://registry.nlark.com/array-unique/download/array-unique-0.3.2.tgz"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

array.prototype.flat@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmmirror.com/array.prototype.flat/download/array.prototype.flat-1.2.5.tgz"
  integrity sha1-B+CXXYS7x8SM0YedYJ5oJZjTPhM=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.0"

arrify@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/arrify/-/arrify-2.0.1.tgz"
  integrity sha512-3duEwti880xqi4eAMN8AyR4a0ByT90zoYdLlevfrvU43vb0YZwZVfxOgxWrLXXXpyugL0hNZc9G6BiB5B3nUug==

asn1.js@^5.2.0:
  version "5.4.1"
  resolved "https://registry.nlark.com/asn1.js/download/asn1.js-5.4.1.tgz"
  integrity sha1-EamAuE67kXgc41sP3C7ilON4Pwc=
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    safer-buffer "^2.1.0"

asn1@~0.2.3:
  version "0.2.6"
  resolved "https://registry.npmmirror.com/asn1/download/asn1-0.2.6.tgz?cache=0&sync_timestamp=1635986760581&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fasn1%2Fdownload%2Fasn1-0.2.6.tgz"
  integrity sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/assert-plus/download/assert-plus-1.0.0.tgz"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assert@^1.1.1:
  version "1.5.0"
  resolved "https://registry.nlark.com/assert/download/assert-1.5.0.tgz"
  integrity sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs=
  dependencies:
    object-assign "^4.1.1"
    util "0.10.3"

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/assign-symbols/download/assign-symbols-1.0.0.tgz"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/astral-regex/download/astral-regex-1.0.0.tgz"
  integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=

async-each@^1.0.1:
  version "1.0.3"
  resolved "https://registry.nlark.com/async-each/download/async-each-1.0.3.tgz"
  integrity sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8=

async-foreach@^0.1.3:
  version "0.1.3"
  resolved "https://registry.nlark.com/async-foreach/download/async-foreach-0.1.3.tgz"
  integrity sha1-NhIfhFwFeBct5Bmpfb6x0W7DRUI=

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/async-limiter/download/async-limiter-1.0.1.tgz"
  integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=

async-validator@^4.0.7:
  version "4.0.7"
  resolved "https://registry.npmmirror.com/async-validator/-/async-validator-4.0.7.tgz"
  integrity sha512-Pj2IR7u8hmUEDOwB++su6baaRi+QvsgajuFB9j95foM1N2gy5HM4z60hfusIO0fBPG5uLAEl6yCJr1jNSVugEQ==

async@^2.6.2:
  version "2.6.3"
  resolved "https://registry.npmmirror.com/async/download/async-2.6.3.tgz"
  integrity sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=
  dependencies:
    lodash "^4.17.14"

async@^3.2.0:
  version "3.2.2"
  resolved "https://registry.npmmirror.com/async/-/async-3.2.2.tgz"
  integrity sha512-H0E+qZaDEfx/FY4t7iLRv1W2fFI6+pyCeTw1uN20AQPiwqwM6ojPxHxdLv4z8hi2DtnW9BOckSspLucW7pIE5g==

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.nlark.com/asynckit/download/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/at-least-node/download/at-least-node-1.0.0.tgz"
  integrity sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=

atob@^2.1.2:
  version "2.1.2"
  resolved "https://registry.nlark.com/atob/download/atob-2.1.2.tgz"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

autoprefixer@^9.8.6:
  version "9.8.8"
  resolved "https://registry.npmmirror.com/autoprefixer/download/autoprefixer-9.8.8.tgz"
  integrity sha512-eM9d/swFopRt5gdJ7jrpCwgvEMIayITpojhkkSMRsFHYuH5bkSQ4p/9qTEHtmNudUZh22Tehu7I6CxAW0IXTKA==
  dependencies:
    browserslist "^4.12.0"
    caniuse-lite "^1.0.30001109"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    picocolors "^0.2.1"
    postcss "^7.0.32"
    postcss-value-parser "^4.1.0"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "https://registry.nlark.com/aws-sign2/download/aws-sign2-0.7.0.tgz"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.11.0"
  resolved "https://registry.nlark.com/aws4/download/aws4-1.11.0.tgz"
  integrity sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk=

axios@^0.24.0:
  version "0.24.0"
  resolved "https://registry.npmmirror.com/axios/-/axios-0.24.0.tgz"
  integrity sha512-Q6cWsys88HoPgAaFAVUb0WpPk0O8iTeisR9IMqy9G8AbO4NlpVknrnQS03zzF9PGAWgO3cgletO3VjV/P7VztA==
  dependencies:
    follow-redirects "^1.14.4"

babel-code-frame@^6.22.0:
  version "6.26.0"
  resolved "https://registry.nlark.com/babel-code-frame/download/babel-code-frame-6.26.0.tgz"
  integrity sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

babel-loader@^8.1.0:
  version "8.2.3"
  resolved "https://registry.npmmirror.com/babel-loader/download/babel-loader-8.2.3.tgz?cache=0&sync_timestamp=1634769533620&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbabel-loader%2Fdownload%2Fbabel-loader-8.2.3.tgz"
  integrity sha1-iYa0Dxpkys/LS4QpMgCF72ixNC0=
  dependencies:
    find-cache-dir "^3.3.1"
    loader-utils "^1.4.0"
    make-dir "^3.1.0"
    schema-utils "^2.6.5"

babel-plugin-dynamic-import-node@^2.3.3:
  version "2.3.3"
  resolved "https://registry.nlark.com/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz?cache=0&sync_timestamp=1624608029217&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbabel-plugin-dynamic-import-node%2Fdownload%2Fbabel-plugin-dynamic-import-node-2.3.3.tgz"
  integrity sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=
  dependencies:
    object.assign "^4.1.0"

babel-plugin-polyfill-corejs2@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.3.0.tgz?cache=0&sync_timestamp=1636799770195&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbabel-plugin-polyfill-corejs2%2Fdownload%2Fbabel-plugin-polyfill-corejs2-0.3.0.tgz"
  integrity sha512-wMDoBJ6uG4u4PNFh72Ty6t3EgfA91puCuAwKIazbQlci+ENb/UU9A3xG5lutjUIiXCIn1CY5L15r9LimiJyrSA==
  dependencies:
    "@babel/compat-data" "^7.13.11"
    "@babel/helper-define-polyfill-provider" "^0.3.0"
    semver "^6.1.1"

babel-plugin-polyfill-corejs3@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmmirror.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.4.0.tgz?cache=0&sync_timestamp=1636799770316&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbabel-plugin-polyfill-corejs3%2Fdownload%2Fbabel-plugin-polyfill-corejs3-0.4.0.tgz"
  integrity sha512-YxFreYwUfglYKdLUGvIF2nJEsGwj+RhWSX/ije3D2vQPOXuyMLMtg/cCGMDpOA7Nd+MwlNdnGODbd2EwUZPlsw==
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.0"
    core-js-compat "^3.18.0"

babel-plugin-polyfill-regenerator@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.3.0.tgz?cache=0&sync_timestamp=1636799770450&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbabel-plugin-polyfill-regenerator%2Fdownload%2Fbabel-plugin-polyfill-regenerator-0.3.0.tgz"
  integrity sha512-dhAPTDLGoMW5/84wkgwiLRwMnio2i1fUe53EuvtKMv0pn2p3S8OCoV1xAzfJPl0KOX7IB89s2ib85vbYiea3jg==
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/balanced-match/download/balanced-match-1.0.2.tgz"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base64-js@^1.0.2, base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://registry.nlark.com/base64-js/download/base64-js-1.5.1.tgz?cache=0&sync_timestamp=1624607950711&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbase64-js%2Fdownload%2Fbase64-js-1.5.1.tgz"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

base@^0.11.1:
  version "0.11.2"
  resolved "https://registry.nlark.com/base/download/base-0.11.2.tgz"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

batch@0.6.1:
  version "0.6.1"
  resolved "https://registry.npmmirror.com/batch/download/batch-0.6.1.tgz"
  integrity sha512-x+VAiMRL6UPkx+kudNvxTl6hB2XNNCG2r+7wixVfIYwu/2HKRXimwQyaumLjMveWvT2Hkd/cAJw+QBMfJ/EKVw==

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

bfj@^6.1.1:
  version "6.1.2"
  resolved "https://registry.nlark.com/bfj/download/bfj-6.1.2.tgz"
  integrity sha1-MlyGGoIryzWKQceKM7jm4ght3n8=
  dependencies:
    bluebird "^3.5.5"
    check-types "^8.0.3"
    hoopy "^0.1.4"
    tryer "^1.0.1"

big.js@^3.1.3:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/big.js/download/big.js-3.2.0.tgz"
  integrity sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4=

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://registry.npmmirror.com/big.js/download/big.js-5.2.2.tgz"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "https://registry.nlark.com/binary-extensions/download/binary-extensions-1.13.1.tgz"
  integrity sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/binary-extensions/download/binary-extensions-2.2.0.tgz"
  integrity sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=

bindings@^1.5.0:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/bindings/-/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df"
  integrity sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==
  dependencies:
    file-uri-to-path "1.0.0"

bl@^4.0.3:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/bl/-/bl-4.1.0.tgz"
  integrity sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

block-stream@*:
  version "0.0.9"
  resolved "https://registry.nlark.com/block-stream/download/block-stream-0.0.9.tgz"
  integrity sha1-E+v+d4oDIFz+A3UUgeu0szAMEmo=
  dependencies:
    inherits "~2.0.0"

bluebird@^3.1.1, bluebird@^3.5.5:
  version "3.7.2"
  resolved "https://registry.nlark.com/bluebird/download/bluebird-3.7.2.tgz"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.11.9:
  version "4.12.0"
  resolved "https://registry.nlark.com/bn.js/download/bn.js-4.12.0.tgz"
  integrity sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=

bn.js@^5.0.0, bn.js@^5.1.1:
  version "5.2.0"
  resolved "https://registry.nlark.com/bn.js/download/bn.js-5.2.0.tgz"
  integrity sha1-NYhgZ0OWxpl3canQUfzBtX1K4AI=

body-parser@1.19.1:
  version "1.19.1"
  resolved "https://registry.npmmirror.com/body-parser/download/body-parser-1.19.1.tgz"
  integrity sha512-8ljfQi5eBk8EJfECMrgqNGWPEY5jWP+1IzkzkGdFFEwFQZZyaZ21UqdaHktgiMlH0xLHqIFtE/u2OYE5dOtViA==
  dependencies:
    bytes "3.1.1"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.2"
    http-errors "1.8.1"
    iconv-lite "0.4.24"
    on-finished "~2.3.0"
    qs "6.9.6"
    raw-body "2.4.2"
    type-is "~1.6.18"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "https://registry.nlark.com/bonjour/download/bonjour-3.5.0.tgz"
  integrity sha1-jokKGD2O6aI5OzhExpGkK897yfU=
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

boolbase@^1.0.0, boolbase@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/boolbase/download/boolbase-1.0.0.tgz"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.nlark.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.1, braces@^2.3.2:
  version "2.3.2"
  resolved "https://registry.nlark.com/braces/download/braces-2.3.2.tgz"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.1, braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/braces/download/braces-3.0.2.tgz"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

brorand@^1.0.1, brorand@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/brorand/download/brorand-1.1.0.tgz"
  integrity sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=

browserify-aes@^1.0.0, browserify-aes@^1.0.4:
  version "1.2.0"
  resolved "https://registry.nlark.com/browserify-aes/download/browserify-aes-1.2.0.tgz"
  integrity sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/browserify-cipher/download/browserify-cipher-1.0.1.tgz"
  integrity sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/browserify-des/download/browserify-des-1.0.2.tgz"
  integrity sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0, browserify-rsa@^4.0.1:
  version "4.1.0"
  resolved "https://registry.nlark.com/browserify-rsa/download/browserify-rsa-4.1.0.tgz"
  integrity sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0=
  dependencies:
    bn.js "^5.0.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.2.1"
  resolved "https://registry.nlark.com/browserify-sign/download/browserify-sign-4.2.1.tgz"
  integrity sha1-6vSt1G3VS+O7OzbAzxWrvrp5VsM=
  dependencies:
    bn.js "^5.1.1"
    browserify-rsa "^4.0.1"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    elliptic "^6.5.3"
    inherits "^2.0.4"
    parse-asn1 "^5.1.5"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/browserify-zlib/download/browserify-zlib-0.2.0.tgz"
  integrity sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=
  dependencies:
    pako "~1.0.5"

browserslist@^4.0.0, browserslist@^4.12.0, browserslist@^4.17.5, browserslist@^4.19.1:
  version "4.19.1"
  resolved "https://registry.npmmirror.com/browserslist/download/browserslist-4.19.1.tgz"
  integrity sha512-u2tbbG5PdKRTUoctO3NBD8FQ5HdPh1ZXPHzp1rwaa5jTc+RV9/+RlWiAIKmjRPQF+xbGM9Kklj5bZQFa2s/38A==
  dependencies:
    caniuse-lite "^1.0.30001286"
    electron-to-chromium "^1.4.17"
    escalade "^3.1.1"
    node-releases "^2.0.1"
    picocolors "^1.0.0"

buffer-crc32@^0.2.1, buffer-crc32@^0.2.13:
  version "0.2.13"
  resolved "https://registry.npmmirror.com/buffer-crc32/-/buffer-crc32-0.2.13.tgz"
  integrity "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI= sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ=="

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://registry.nlark.com/buffer-from/download/buffer-from-1.1.2.tgz?cache=0&sync_timestamp=1627578361955&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbuffer-from%2Fdownload%2Fbuffer-from-1.1.2.tgz"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "https://registry.nlark.com/buffer-indexof/download/buffer-indexof-1.1.1.tgz"
  integrity sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=

buffer-json@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/buffer-json/download/buffer-json-2.0.0.tgz"
  integrity sha1-9z4TseQvGW/i/WfQAcfXEH7dfCM=

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/buffer-xor/download/buffer-xor-1.0.3.tgz"
  integrity sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=

buffer@^4.3.0:
  version "4.9.2"
  resolved "https://registry.npmmirror.com/buffer/download/buffer-4.9.2.tgz"
  integrity sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

buffer@^5.5.0:
  version "5.7.1"
  resolved "https://registry.npmmirror.com/buffer/-/buffer-5.7.1.tgz"
  integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

builtin-modules@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/builtin-modules/download/builtin-modules-1.1.1.tgz"
  integrity sha1-Jw8HbFpywC9bZaR9+Uxf46J4iS8=

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz"
  integrity sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=

bytes@3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/bytes/download/bytes-3.0.0.tgz?cache=0&sync_timestamp=1637015083874&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbytes%2Fdownload%2Fbytes-3.0.0.tgz"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

bytes@3.1.1:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/bytes/download/bytes-3.1.1.tgz?cache=0&sync_timestamp=1637015083874&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbytes%2Fdownload%2Fbytes-3.1.1.tgz"
  integrity sha512-dWe4nWO/ruEOY7HkUJ5gFt1DCFV9zPRoJr8pV0/ASQermOZjtq8jMjOprC0Kd10GLN+l7xaUPvxzJFWtxGu8Fg==

cacache@^12.0.2, cacache@^12.0.3:
  version "12.0.4"
  resolved "https://registry.nlark.com/cacache/download/cacache-12.0.4.tgz?cache=0&sync_timestamp=1630000121314&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcacache%2Fdownload%2Fcacache-12.0.4.tgz"
  integrity sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=
  dependencies:
    bluebird "^3.5.5"
    chownr "^1.1.1"
    figgy-pudding "^3.5.1"
    glob "^7.1.4"
    graceful-fs "^4.1.15"
    infer-owner "^1.0.3"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.3"
    ssri "^6.0.1"
    unique-filename "^1.1.1"
    y18n "^4.0.0"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/cache-base/download/cache-base-1.0.1.tgz?cache=0&sync_timestamp=1636237308360&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcache-base%2Fdownload%2Fcache-base-1.0.1.tgz"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

cache-loader@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/cache-loader/download/cache-loader-4.1.0.tgz"
  integrity sha1-mUjK41OuwKH8ser9ojAIFuyFOH4=
  dependencies:
    buffer-json "^2.0.0"
    find-cache-dir "^3.0.0"
    loader-utils "^1.2.3"
    mkdirp "^0.5.1"
    neo-async "^2.6.1"
    schema-utils "^2.0.0"

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/call-bind/download/call-bind-1.0.2.tgz"
  integrity sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

call-me-maybe@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/call-me-maybe/download/call-me-maybe-1.0.1.tgz"
  integrity sha1-JtII6onje1y95gJQoV8DHBak1ms=

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/caller-callsite/download/caller-callsite-2.0.0.tgz"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/caller-path/download/caller-path-2.0.0.tgz?cache=0&sync_timestamp=1633674195219&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcaller-path%2Fdownload%2Fcaller-path-2.0.0.tgz"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/callsites/download/callsites-2.0.0.tgz?cache=0&sync_timestamp=1628464907898&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcallsites%2Fdownload%2Fcallsites-2.0.0.tgz"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/callsites/download/callsites-3.1.0.tgz?cache=0&sync_timestamp=1628464907898&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcallsites%2Fdownload%2Fcallsites-3.1.0.tgz"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@3.0.x:
  version "3.0.0"
  resolved "https://registry.nlark.com/camel-case/download/camel-case-3.0.0.tgz?cache=0&sync_timestamp=1624608083534&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcamel-case%2Fdownload%2Fcamel-case-3.0.0.tgz"
  integrity sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.1"

camelcase-keys@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/camelcase-keys/download/camelcase-keys-2.1.0.tgz?cache=0&sync_timestamp=1633332938539&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcamelcase-keys%2Fdownload%2Fcamelcase-keys-2.1.0.tgz"
  integrity sha1-MIvur/3ygRkFHvodkyITyRuPkuc=
  dependencies:
    camelcase "^2.0.0"
    map-obj "^1.0.0"

camelcase@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/camelcase/download/camelcase-2.1.1.tgz?cache=0&sync_timestamp=1636945198738&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcamelcase%2Fdownload%2Fcamelcase-2.1.1.tgz"
  integrity sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npmmirror.com/camelcase/download/camelcase-5.3.1.tgz?cache=0&sync_timestamp=1636945198738&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcamelcase%2Fdownload%2Fcamelcase-5.3.1.tgz"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

camelcase@^6.0.0:
  version "6.2.1"
  resolved "https://registry.npmmirror.com/camelcase/download/camelcase-6.2.1.tgz?cache=0&sync_timestamp=1636945198738&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcamelcase%2Fdownload%2Fcamelcase-6.2.1.tgz"
  integrity sha512-tVI4q5jjFV5CavAU8DXfza/TJcZutVKo/5Foskmsqcm0MsL91moHvwiGNnqaa2o6PF/7yT5ikDRcVcl8Rj6LCA==

caniuse-api@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/caniuse-api/download/caniuse-api-3.0.0.tgz"
  integrity sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30001109, caniuse-lite@^1.0.30001286:
  version "1.0.30001294"
  resolved "https://registry.npmmirror.com/caniuse-lite/download/caniuse-lite-1.0.30001294.tgz"
  integrity sha512-LiMlrs1nSKZ8qkNhpUf5KD0Al1KCBE3zaT7OLOwEkagXMEDij98SiOovn9wxVGQpklk9vVC/pUSqgYmkmKOS8g==

case-sensitive-paths-webpack-plugin@^2.3.0:
  version "2.4.0"
  resolved "https://registry.nlark.com/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.4.0.tgz?cache=0&sync_timestamp=1624607978538&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcase-sensitive-paths-webpack-plugin%2Fdownload%2Fcase-sensitive-paths-webpack-plugin-2.4.0.tgz"
  integrity sha1-22QGbGQi7tLgjMFLmGykN5bbxtQ=

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://registry.nlark.com/caseless/download/caseless-0.12.0.tgz"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

chalk@^1.0.0, chalk@^1.1.1, chalk@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/chalk/download/chalk-1.1.3.tgz"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0, chalk@^2.0.1, chalk@^2.1.0, chalk@^2.3.0, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/chalk/download/chalk-3.0.0.tgz"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0, chalk@^4.1.0:
  version "4.1.2"
  resolved "https://registry.npmmirror.com/chalk/download/chalk-4.1.2.tgz"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmmirror.com/chardet/download/chardet-0.7.0.tgz?cache=0&sync_timestamp=1634639163489&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchardet%2Fdownload%2Fchardet-0.7.0.tgz"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

check-types@^8.0.3:
  version "8.0.3"
  resolved "https://registry.nlark.com/check-types/download/check-types-8.0.3.tgz"
  integrity sha1-M1bMoZyIlUTy16le1JzlCKDs9VI=

"chokidar@>=3.0.0 <4.0.0", chokidar@^3.3.0, chokidar@^3.4.1:
  version "3.5.2"
  resolved "https://registry.npmmirror.com/chokidar/download/chokidar-3.5.2.tgz"
  integrity sha512-ekGhOnNVPgT77r4K/U3GDhu+FQ2S8TnK/s2KbIGXi0SZWuwkZ2QNyfWdZW+TVfn84DpEP7rLeCt2UI6bJ8GwbQ==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chokidar@^2.1.8:
  version "2.1.8"
  resolved "https://registry.npmmirror.com/chokidar/download/chokidar-2.1.8.tgz"
  integrity sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg==
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chownr@^1.1.1:
  version "1.1.4"
  resolved "https://registry.nlark.com/chownr/download/chownr-1.1.4.tgz"
  integrity sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=

chrome-trace-event@^1.0.2:
  version "1.0.3"
  resolved "https://registry.nlark.com/chrome-trace-event/download/chrome-trace-event-1.0.3.tgz"
  integrity sha1-EBXs7UdB4V0GZkqVfbv1DQQeJqw=

ci-info@^1.5.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/ci-info/download/ci-info-1.6.0.tgz"
  integrity sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "https://registry.nlark.com/cipher-base/download/cipher-base-1.0.4.tgz"
  integrity sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

class-utils@^0.3.5:
  version "0.3.6"
  resolved "https://registry.nlark.com/class-utils/download/class-utils-0.3.6.tgz"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

clean-css@4.2.x:
  version "4.2.4"
  resolved "https://registry.npmmirror.com/clean-css/download/clean-css-4.2.4.tgz?cache=0&sync_timestamp=1634992398113&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fclean-css%2Fdownload%2Fclean-css-4.2.4.tgz"
  integrity sha1-czv0brpOYHxokepXwkqYk1aDEXg=
  dependencies:
    source-map "~0.6.0"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/clean-stack/download/clean-stack-2.2.0.tgz"
  integrity sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=

cli-cursor@^2.0.0, cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/cli-cursor/download/cli-cursor-2.1.0.tgz?cache=0&sync_timestamp=1629748329424&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcli-cursor%2Fdownload%2Fcli-cursor-2.1.0.tgz"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/cli-cursor/download/cli-cursor-3.1.0.tgz?cache=0&sync_timestamp=1629748329424&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcli-cursor%2Fdownload%2Fcli-cursor-3.1.0.tgz"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-highlight@^2.1.4:
  version "2.1.11"
  resolved "https://registry.nlark.com/cli-highlight/download/cli-highlight-2.1.11.tgz"
  integrity sha1-SXNvpFLwqvT65YDjCssmgo0twb8=
  dependencies:
    chalk "^4.0.0"
    highlight.js "^10.7.1"
    mz "^2.4.0"
    parse5 "^5.1.1"
    parse5-htmlparser2-tree-adapter "^6.0.0"
    yargs "^16.0.0"

cli-spinners@^2.0.0:
  version "2.6.1"
  resolved "https://registry.npmmirror.com/cli-spinners/download/cli-spinners-2.6.1.tgz?cache=0&sync_timestamp=1633109759784&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcli-spinners%2Fdownload%2Fcli-spinners-2.6.1.tgz"
  integrity sha1-rclU6+KBw3pjGb+kAebdJIj/tw0=

cli-truncate@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/cli-truncate/download/cli-truncate-0.2.1.tgz?cache=0&sync_timestamp=1633786428528&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcli-truncate%2Fdownload%2Fcli-truncate-0.2.1.tgz"
  integrity sha1-nxXPuwcFAFNpIWxiasfQWrkN1XQ=
  dependencies:
    slice-ansi "0.0.4"
    string-width "^1.0.1"

cli-width@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/cli-width/download/cli-width-3.0.0.tgz"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

clipboardy@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/clipboardy/download/clipboardy-2.3.0.tgz?cache=0&sync_timestamp=1634141778549&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fclipboardy%2Fdownload%2Fclipboardy-2.3.0.tgz"
  integrity sha1-PCkDZQxo5GqRs4iYW8J3QofbopA=
  dependencies:
    arch "^2.1.1"
    execa "^1.0.0"
    is-wsl "^2.1.1"

cliui@^5.0.0:
  version "5.0.0"
  resolved "https://registry.nlark.com/cliui/download/cliui-5.0.0.tgz"
  integrity sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

cliui@^6.0.0:
  version "6.0.0"
  resolved "https://registry.nlark.com/cliui/download/cliui-6.0.0.tgz"
  integrity sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

cliui@^7.0.2:
  version "7.0.4"
  resolved "https://registry.nlark.com/cliui/download/cliui-7.0.4.tgz"
  integrity sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/clone-deep/download/clone-deep-4.0.1.tgz"
  integrity sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://registry.nlark.com/clone/download/clone-1.0.4.tgz"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

coa@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/coa/download/coa-2.0.2.tgz"
  integrity sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM=
  dependencies:
    "@types/q" "^1.5.1"
    chalk "^2.4.1"
    q "^1.1.2"

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/code-point-at/download/code-point-at-1.1.0.tgz"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/collection-visit/download/collection-visit-1.0.0.tgz"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0, color-convert@^1.9.3:
  version "1.9.3"
  resolved "https://registry.npmmirror.com/color-convert/download/color-convert-1.9.3.tgz"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/color-convert/download/color-convert-2.0.1.tgz"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3, color-name@^1.0.0:
  version "1.1.3"
  resolved "https://registry.nlark.com/color-name/download/color-name-1.1.3.tgz"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.nlark.com/color-name/download/color-name-1.1.4.tgz"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-string@^1.6.0:
  version "1.9.0"
  resolved "https://registry.npmmirror.com/color-string/download/color-string-1.9.0.tgz"
  integrity sha512-9Mrz2AQLefkH1UvASKj6v6hj/7eWgjnT/cVsR8CumieLoT+g900exWeNogqtweI8dxloXN9BDQTYro1oWu/5CQ==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^3.0.0:
  version "3.2.1"
  resolved "https://registry.npmmirror.com/color/download/color-3.2.1.tgz"
  integrity sha1-NUTcGYyvRJDD7MmnkLVP6f9F4WQ=
  dependencies:
    color-convert "^1.9.3"
    color-string "^1.6.0"

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "https://registry.nlark.com/combined-stream/download/combined-stream-1.0.8.tgz"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@2.17.x, commander@^2.12.1:
  version "2.17.1"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.17.1.tgz"
  integrity sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg==

commander@^2.18.0, commander@^2.20.0:
  version "2.20.3"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.20.3.tgz"
  integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==

commander@~2.19.0:
  version "2.19.0"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.19.0.tgz"
  integrity sha512-6tvAOO+D6OENvRAh524Dh9jcfKTYDQAqvqezbCW82xj5X0pSrcpxtvRKHLG0yBY6SD7PSDrJaj+0AiOcKVd1Xg==

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/commondir/download/commondir-1.0.1.tgz?cache=0&sync_timestamp=1624608091111&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcommondir%2Fdownload%2Fcommondir-1.0.1.tgz"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "https://registry.nlark.com/component-emitter/download/component-emitter-1.3.0.tgz"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

compress-commons@^4.1.0:
  version "4.1.1"
  resolved "https://registry.npmmirror.com/compress-commons/-/compress-commons-4.1.1.tgz"
  integrity sha512-QLdDLCKNV2dtoTorqgxngQCMA+gWXkM/Nwu7FpeBhk/RdkzimqC3jueb/FDmaZeXh+uby1jkBqE3xArsLBE5wQ==
  dependencies:
    buffer-crc32 "^0.2.13"
    crc32-stream "^4.0.2"
    normalize-path "^3.0.0"
    readable-stream "^3.6.0"

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://registry.nlark.com/compressible/download/compressible-2.0.18.tgz"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.4"
  resolved "https://registry.nlark.com/compression/download/compression-1.7.4.tgz?cache=0&sync_timestamp=1624607989121&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcompression%2Fdownload%2Fcompression-1.7.4.tgz"
  integrity sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.nlark.com/concat-map/download/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.0:
  version "1.6.2"
  resolved "https://registry.nlark.com/concat-stream/download/concat-stream-1.6.2.tgz"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

connect-history-api-fallback@^1.6.0:
  version "1.6.0"
  resolved "https://registry.nlark.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz"
  integrity sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=

console-browserify@^1.1.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/console-browserify/download/console-browserify-1.2.0.tgz"
  integrity sha1-ZwY871fOts9Jk6KrOlWECujEkzY=

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/console-control-strings/download/console-control-strings-1.1.0.tgz"
  integrity sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=

consolidate@^0.15.1:
  version "0.15.1"
  resolved "https://registry.nlark.com/consolidate/download/consolidate-0.15.1.tgz"
  integrity sha1-IasEMjXHGgfUXZqtmFk7DbpWurc=
  dependencies:
    bluebird "^3.1.1"

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/constants-browserify/download/constants-browserify-1.0.0.tgz"
  integrity sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=

content-disposition@0.5.4:
  version "0.5.4"
  resolved "https://registry.npmmirror.com/content-disposition/download/content-disposition-0.5.4.tgz"
  integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/content-type/download/content-type-1.0.4.tgz"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

convert-source-map@^1.7.0:
  version "1.8.0"
  resolved "https://registry.nlark.com/convert-source-map/download/convert-source-map-1.8.0.tgz?cache=0&sync_timestamp=1624608042394&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fconvert-source-map%2Fdownload%2Fconvert-source-map-1.8.0.tgz"
  integrity sha1-8zc8MtIbTXgN2ABFFGhPt5HKQ2k=
  dependencies:
    safe-buffer "~5.1.1"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.nlark.com/cookie-signature/download/cookie-signature-1.0.6.tgz"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.4.1:
  version "0.4.1"
  resolved "https://registry.nlark.com/cookie/download/cookie-0.4.1.tgz"
  integrity sha1-r9cT/ibr0hupXOth+agRblClN9E=

copy-concurrently@^1.0.0:
  version "1.0.5"
  resolved "https://registry.nlark.com/copy-concurrently/download/copy-concurrently-1.0.5.tgz"
  integrity sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=
  dependencies:
    aproba "^1.1.1"
    fs-write-stream-atomic "^1.0.8"
    iferr "^0.1.5"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "https://registry.nlark.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

copy-webpack-plugin@^5.1.1:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/copy-webpack-plugin/download/copy-webpack-plugin-5.1.2.tgz"
  integrity sha1-ioieHcr6bJHGzUvhrRWPHTgjuuI=
  dependencies:
    cacache "^12.0.3"
    find-cache-dir "^2.1.0"
    glob-parent "^3.1.0"
    globby "^7.1.1"
    is-glob "^4.0.1"
    loader-utils "^1.2.3"
    minimatch "^3.0.4"
    normalize-path "^3.0.0"
    p-limit "^2.2.1"
    schema-utils "^1.0.0"
    serialize-javascript "^4.0.0"
    webpack-log "^2.0.0"

core-js-compat@^3.18.0, core-js-compat@^3.19.1, core-js-compat@^3.6.5:
  version "3.20.1"
  resolved "https://registry.npmmirror.com/core-js-compat/download/core-js-compat-3.20.1.tgz"
  integrity sha512-AVhKZNpqMV3Jz8hU0YEXXE06qoxtQGsAqU0u1neUngz5IusDJRX/ZJ6t3i7mS7QxNyEONbCo14GprkBrxPlTZA==
  dependencies:
    browserslist "^4.19.1"
    semver "7.0.0"

core-js@^3.6.5:
  version "3.20.1"
  resolved "https://registry.npmmirror.com/core-js/download/core-js-3.20.1.tgz"
  integrity sha512-btdpStYFQScnNVQ5slVcr858KP0YWYjV16eGJQw8Gg7CWtu/2qNvIM3qVRIR3n1pK2R9NNOrTevbvAYxajwEjg==

core-util-is@1.0.2, core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/core-util-is/download/core-util-is-1.0.2.tgz"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cosmiconfig@^5.0.0, cosmiconfig@^5.2.1:
  version "5.2.1"
  resolved "https://registry.nlark.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

cosmiconfig@^6.0.0:
  version "6.0.0"
  resolved "https://registry.nlark.com/cosmiconfig/download/cosmiconfig-6.0.0.tgz"
  integrity sha1-2k/uhTxS9rHmk19BwaL8UL1KmYI=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.1.0"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.7.2"

cp-file@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmmirror.com/cp-file/-/cp-file-7.0.0.tgz"
  integrity sha512-0Cbj7gyvFVApzpK/uhCtQ/9kE9UnYpxMzaq5nQQC/Dh4iaj5fxp7iEFIullrYwzj8nf0qnsI1Qsx34hAeAebvw==
  dependencies:
    graceful-fs "^4.1.2"
    make-dir "^3.0.0"
    nested-error-stacks "^2.0.0"
    p-event "^4.1.0"

cpy@^8.1.2:
  version "8.1.2"
  resolved "https://registry.npmmirror.com/cpy/-/cpy-8.1.2.tgz"
  integrity sha512-dmC4mUesv0OYH2kNFEidtf/skUwv4zePmGeepjyyJ0qTo5+8KhA1o99oIAwVVLzQMAeDJml74d6wPPKb6EZUTg==
  dependencies:
    arrify "^2.0.1"
    cp-file "^7.0.0"
    globby "^9.2.0"
    has-glob "^1.0.0"
    junk "^3.1.0"
    nested-error-stacks "^2.1.0"
    p-all "^2.1.0"
    p-filter "^2.1.0"
    p-map "^3.0.0"

crc-32@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/crc-32/-/crc-32-1.2.0.tgz"
  integrity sha512-1uBwHxF+Y/4yF5G48fwnKq6QsIXheor3ZLPT80yGBV1oEUwpPojlEhQbWKVw1VwcTQyMGHK1/XMmTjmlsmTTGA==
  dependencies:
    exit-on-epipe "~1.0.1"
    printj "~1.1.0"

crc32-stream@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/crc32-stream/-/crc32-stream-4.0.2.tgz"
  integrity sha512-DxFZ/Hk473b/muq1VJ///PMNLj0ZMnzye9thBpmjpJKCc5eMgB95aK8zCGrGfQ90cWo561Te6HK9D+j4KPdM6w==
  dependencies:
    crc-32 "^1.2.0"
    readable-stream "^3.4.0"

create-ecdh@^4.0.0:
  version "4.0.4"
  resolved "https://registry.nlark.com/create-ecdh/download/create-ecdh-4.0.4.tgz"
  integrity sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.5.3"

create-hash@^1.1.0, create-hash@^1.1.2, create-hash@^1.2.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/create-hash/download/create-hash-1.2.0.tgz?cache=0&sync_timestamp=1624607951398&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcreate-hash%2Fdownload%2Fcreate-hash-1.2.0.tgz"
  integrity sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.4, create-hmac@^1.1.7:
  version "1.1.7"
  resolved "https://registry.nlark.com/create-hmac/download/create-hmac-1.1.7.tgz"
  integrity sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

cross-spawn@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-3.0.1.tgz"
  integrity sha1-ElYDfsufDF9549bvE14wdwGEuYI=
  dependencies:
    lru-cache "^4.0.1"
    which "^1.2.9"

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-5.1.0.tgz"
  integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-6.0.5.tgz"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0:
  version "7.0.3"
  resolved "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-7.0.3.tgz"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-browserify@^3.11.0:
  version "3.12.0"
  resolved "https://registry.nlark.com/crypto-browserify/download/crypto-browserify-3.12.0.tgz"
  integrity sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"
    randomfill "^1.0.3"

css-color-names@0.0.4, css-color-names@^0.0.4:
  version "0.0.4"
  resolved "https://registry.nlark.com/css-color-names/download/css-color-names-0.0.4.tgz"
  integrity sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=

css-declaration-sorter@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/css-declaration-sorter/download/css-declaration-sorter-4.0.1.tgz"
  integrity sha1-wZiUD2OnbX42wecQGLABchBUyyI=
  dependencies:
    postcss "^7.0.1"
    timsort "^0.3.0"

css-loader@^3.5.3:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/css-loader/download/css-loader-3.6.0.tgz?cache=0&sync_timestamp=1635967924209&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcss-loader%2Fdownload%2Fcss-loader-3.6.0.tgz"
  integrity sha1-Lkssfm4tJ/jI8o9hv/zS5ske9kU=
  dependencies:
    camelcase "^5.3.1"
    cssesc "^3.0.0"
    icss-utils "^4.1.1"
    loader-utils "^1.2.3"
    normalize-path "^3.0.0"
    postcss "^7.0.32"
    postcss-modules-extract-imports "^2.0.0"
    postcss-modules-local-by-default "^3.0.2"
    postcss-modules-scope "^2.2.0"
    postcss-modules-values "^3.0.0"
    postcss-value-parser "^4.1.0"
    schema-utils "^2.7.0"
    semver "^6.3.0"

css-select-base-adapter@^0.1.1:
  version "0.1.1"
  resolved "https://registry.nlark.com/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz"
  integrity sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc=

css-select@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/css-select/download/css-select-2.1.0.tgz"
  integrity sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8=
  dependencies:
    boolbase "^1.0.0"
    css-what "^3.2.1"
    domutils "^1.7.0"
    nth-check "^1.0.2"

css-select@^4.1.3:
  version "4.2.1"
  resolved "https://registry.npmmirror.com/css-select/download/css-select-4.2.1.tgz"
  integrity sha512-/aUslKhzkTNCQUB2qTX84lVmfia9NyjP3WpDGtj/WxhwBzWBYUV3DgUpurHTme8UTPcPlAD1DJ+b0nN/t50zDQ==
  dependencies:
    boolbase "^1.0.0"
    css-what "^5.1.0"
    domhandler "^4.3.0"
    domutils "^2.8.0"
    nth-check "^2.0.1"

css-tree@1.0.0-alpha.37:
  version "1.0.0-alpha.37"
  resolved "https://registry.npmmirror.com/css-tree/download/css-tree-1.0.0-alpha.37.tgz"
  integrity sha1-mL69YsTB2flg7DQM+fdSLjBwmiI=
  dependencies:
    mdn-data "2.0.4"
    source-map "^0.6.1"

css-tree@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/css-tree/download/css-tree-1.1.3.tgz"
  integrity sha1-60hw+2/XcHMn7JXC/yqwm16NuR0=
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css-what@^3.2.1:
  version "3.4.2"
  resolved "https://registry.npmmirror.com/css-what/download/css-what-3.4.2.tgz"
  integrity sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ=

css-what@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/css-what/download/css-what-5.1.0.tgz"
  integrity sha1-P3tweq32M7r2LCzrhXm1RbtA9/4=

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/cssesc/download/cssesc-3.0.0.tgz"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssnano-preset-default@^4.0.0, cssnano-preset-default@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmmirror.com/cssnano-preset-default/download/cssnano-preset-default-4.0.8.tgz"
  integrity sha1-kgYisfwelaNOiDggPxOXpQTy0/8=
  dependencies:
    css-declaration-sorter "^4.0.1"
    cssnano-util-raw-cache "^4.0.1"
    postcss "^7.0.0"
    postcss-calc "^7.0.1"
    postcss-colormin "^4.0.3"
    postcss-convert-values "^4.0.1"
    postcss-discard-comments "^4.0.2"
    postcss-discard-duplicates "^4.0.2"
    postcss-discard-empty "^4.0.1"
    postcss-discard-overridden "^4.0.1"
    postcss-merge-longhand "^4.0.11"
    postcss-merge-rules "^4.0.3"
    postcss-minify-font-values "^4.0.2"
    postcss-minify-gradients "^4.0.2"
    postcss-minify-params "^4.0.2"
    postcss-minify-selectors "^4.0.2"
    postcss-normalize-charset "^4.0.1"
    postcss-normalize-display-values "^4.0.2"
    postcss-normalize-positions "^4.0.2"
    postcss-normalize-repeat-style "^4.0.2"
    postcss-normalize-string "^4.0.2"
    postcss-normalize-timing-functions "^4.0.2"
    postcss-normalize-unicode "^4.0.1"
    postcss-normalize-url "^4.0.1"
    postcss-normalize-whitespace "^4.0.2"
    postcss-ordered-values "^4.1.2"
    postcss-reduce-initial "^4.0.3"
    postcss-reduce-transforms "^4.0.2"
    postcss-svgo "^4.0.3"
    postcss-unique-selectors "^4.0.1"

cssnano-util-get-arguments@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/cssnano-util-get-arguments/download/cssnano-util-get-arguments-4.0.0.tgz"
  integrity sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8=

cssnano-util-get-match@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/cssnano-util-get-match/download/cssnano-util-get-match-4.0.0.tgz"
  integrity sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0=

cssnano-util-raw-cache@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/cssnano-util-raw-cache/download/cssnano-util-raw-cache-4.0.1.tgz"
  integrity sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI=
  dependencies:
    postcss "^7.0.0"

cssnano-util-same-parent@^4.0.0:
  version "4.0.1"
  resolved "https://registry.nlark.com/cssnano-util-same-parent/download/cssnano-util-same-parent-4.0.1.tgz"
  integrity sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M=

cssnano@^4.0.0, cssnano@^4.1.10:
  version "4.1.11"
  resolved "https://registry.npmmirror.com/cssnano/download/cssnano-4.1.11.tgz"
  integrity sha1-x7X1uB2iacsf2YLLlgwSAJEMmpk=
  dependencies:
    cosmiconfig "^5.0.0"
    cssnano-preset-default "^4.0.8"
    is-resolvable "^1.0.0"
    postcss "^7.0.0"

csso@^4.0.2:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/csso/download/csso-4.2.0.tgz"
  integrity sha1-6jpWE0bo3J9UbW/r7dUBh884lSk=
  dependencies:
    css-tree "^1.1.2"

csstype@^2.6.8:
  version "2.6.19"
  resolved "https://registry.npmmirror.com/csstype/download/csstype-2.6.19.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcsstype%2Fdownload%2Fcsstype-2.6.19.tgz"
  integrity sha512-ZVxXaNy28/k3kJg0Fou5MiYpp88j7H9hLZp8PDC3jV0WFjfH5E9xHb56L0W59cPbKbcHXeP4qyT8PrHp8t6LcQ==

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "https://registry.nlark.com/currently-unhandled/download/currently-unhandled-0.4.1.tgz"
  integrity sha1-mI3zP+qxke95mmE2nddsF635V+o=
  dependencies:
    array-find-index "^1.0.1"

cyclist@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/cyclist/download/cyclist-1.0.1.tgz"
  integrity sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk=

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://registry.nlark.com/dashdash/download/dashdash-1.14.1.tgz"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

date-fns@^1.27.2:
  version "1.30.1"
  resolved "https://registry.npmmirror.com/date-fns/download/date-fns-1.30.1.tgz"
  integrity sha1-LnG/CxGRU9u0zE6I2epaz7UNwFw=

dayjs@^1.10.7:
  version "1.10.7"
  resolved "https://registry.npmmirror.com/dayjs/-/dayjs-1.10.7.tgz"
  integrity sha512-P6twpd70BcPK34K26uJ1KT3wlhpuOAPoMwJzpsIWUxHZ7wpmbdZL/hQqBDfz7hGurYSa5PhzdhDHtt319hL3ig==

debug@2.6.9, debug@^2.2.0, debug@^2.3.3, debug@^2.6.9:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@^3.1.1, debug@^3.2.6, debug@^3.2.7:
  version "3.2.7"
  resolved "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz"
  integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
  dependencies:
    ms "^2.1.1"

debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2:
  version "4.3.3"
  resolved "https://registry.npmmirror.com/debug/download/debug-4.3.3.tgz"
  integrity sha512-/zxw5+vh1Tfv+4Qn7a5nsbcJKPaSvCDhojn6FEl9vupwK2VCSDtEiEtqr8DFtzYFOdz63LBkxec7DYuc2jon6Q==
  dependencies:
    ms "2.1.2"

decamelize@^1.1.2, decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/decamelize/download/decamelize-1.2.0.tgz?cache=0&sync_timestamp=1633055760479&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdecamelize%2Fdownload%2Fdecamelize-1.2.0.tgz"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

dedent@^0.7.0:
  version "0.7.0"
  resolved "https://registry.nlark.com/dedent/download/dedent-0.7.0.tgz"
  integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=

deep-equal@^1.0.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/deep-equal/download/deep-equal-1.1.1.tgz"
  integrity sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o=
  dependencies:
    is-arguments "^1.0.4"
    is-date-object "^1.0.1"
    is-regex "^1.0.4"
    object-is "^1.0.1"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.2.0"

deep-is@~0.1.3:
  version "0.1.4"
  resolved "https://registry.nlark.com/deep-is/download/deep-is-0.1.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdeep-is%2Fdownload%2Fdeep-is-0.1.4.tgz"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

deepmerge@^1.5.2:
  version "1.5.2"
  resolved "https://registry.nlark.com/deepmerge/download/deepmerge-1.5.2.tgz?cache=0&sync_timestamp=1624608044263&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdeepmerge%2Fdownload%2Fdeepmerge-1.5.2.tgz"
  integrity sha1-EEmdhohEza1P7ghC34x/bwyVp1M=

deepmerge@^4.2.2:
  version "4.2.2"
  resolved "https://registry.nlark.com/deepmerge/download/deepmerge-4.2.2.tgz?cache=0&sync_timestamp=1624608044263&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdeepmerge%2Fdownload%2Fdeepmerge-4.2.2.tgz"
  integrity sha1-RNLqNnm49NT/ujPwPYZfwee/SVU=

default-gateway@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/default-gateway/download/default-gateway-4.2.0.tgz"
  integrity sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs=
  dependencies:
    execa "^1.0.0"
    ip-regex "^2.1.0"

default-gateway@^5.0.5:
  version "5.0.5"
  resolved "https://registry.npmmirror.com/default-gateway/download/default-gateway-5.0.5.tgz"
  integrity sha1-T9a9XShV05s0zFpZUFSG6ar8mxA=
  dependencies:
    execa "^3.3.0"

defaults@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/defaults/download/defaults-1.0.3.tgz"
  integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
  dependencies:
    clone "^1.0.2"

define-properties@^1.1.2, define-properties@^1.1.3:
  version "1.1.3"
  resolved "https://registry.nlark.com/define-properties/download/define-properties-1.1.3.tgz"
  integrity sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "https://registry.nlark.com/define-property/download/define-property-0.2.5.tgz"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/define-property/download/define-property-1.0.0.tgz"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "https://registry.nlark.com/define-property/download/define-property-2.0.2.tgz"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

del@^4.1.1:
  version "4.1.1"
  resolved "https://registry.nlark.com/del/download/del-4.1.1.tgz"
  integrity sha1-no8RciLqRKMf86FWwEm5kFKp8LQ=
  dependencies:
    "@types/glob" "^7.1.1"
    globby "^6.1.0"
    is-path-cwd "^2.0.0"
    is-path-in-cwd "^2.0.0"
    p-map "^2.0.0"
    pify "^4.0.1"
    rimraf "^2.6.3"

del@^5.0.0:
  version "5.1.0"
  resolved "https://registry.nlark.com/del/download/del-5.1.0.tgz"
  integrity sha1-2Uh8lONnQQ5u/ykl7ljAyEp1s6c=
  dependencies:
    globby "^10.0.1"
    graceful-fs "^4.2.2"
    is-glob "^4.0.1"
    is-path-cwd "^2.2.0"
    is-path-inside "^3.0.1"
    p-map "^3.0.0"
    rimraf "^3.0.0"
    slash "^3.0.0"

del@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/del/-/del-6.0.0.tgz"
  integrity sha512-1shh9DQ23L16oXSZKB2JxpL7iMy2E0S9d517ptA1P8iw0alkPtQcrKH7ru31rYtKwF499HkTu+DRzq3TCKDFRQ==
  dependencies:
    globby "^11.0.1"
    graceful-fs "^4.2.4"
    is-glob "^4.0.1"
    is-path-cwd "^2.2.0"
    is-path-inside "^3.0.2"
    p-map "^4.0.0"
    rimraf "^3.0.2"
    slash "^3.0.0"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/delayed-stream/download/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/delegates/download/delegates-1.0.0.tgz"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/depd/download/depd-1.1.2.tgz"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

des.js@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/des.js/download/des.js-1.0.1.tgz"
  integrity sha1-U4IULhvcU/hdhtU+X0qn3rkeCEM=
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/destroy/download/destroy-1.0.4.tgz"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-node@^2.0.4:
  version "2.1.0"
  resolved "https://registry.nlark.com/detect-node/download/detect-node-2.1.0.tgz"
  integrity sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=

diff@^4.0.1:
  version "4.0.2"
  resolved "https://registry.nlark.com/diff/download/diff-4.0.2.tgz?cache=0&sync_timestamp=1624608104914&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdiff%2Fdownload%2Fdiff-4.0.2.tgz"
  integrity sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0=

diffie-hellman@^5.0.0:
  version "5.0.3"
  resolved "https://registry.nlark.com/diffie-hellman/download/diffie-hellman-5.0.3.tgz"
  integrity sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

dijkstrajs@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/dijkstrajs/-/dijkstrajs-1.0.2.tgz"
  integrity sha512-QV6PMaHTCNmKSeP6QoXhVTw9snc9VD8MulTT0Bd99Pacp4SS1cjcrYPgBPmibqKVtMJJfqC6XvOXgPMEEPH/fg==

dir-glob@^2.0.0, dir-glob@^2.2.2:
  version "2.2.2"
  resolved "https://registry.nlark.com/dir-glob/download/dir-glob-2.2.2.tgz"
  integrity sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=
  dependencies:
    path-type "^3.0.0"

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.nlark.com/dir-glob/download/dir-glob-3.0.1.tgz"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/dns-equal/download/dns-equal-1.0.0.tgz"
  integrity sha1-s55/HabrCnW6nBcySzR1PEfgZU0=

dns-packet@^1.3.1:
  version "1.3.4"
  resolved "https://registry.npmmirror.com/dns-packet/download/dns-packet-1.3.4.tgz"
  integrity sha1-40VQZYJKJQe6iGxVqJljuxB97G8=
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "https://registry.nlark.com/dns-txt/download/dns-txt-2.0.2.tgz"
  integrity sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=
  dependencies:
    buffer-indexof "^1.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/doctrine/download/doctrine-2.1.0.tgz"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/doctrine/download/doctrine-3.0.0.tgz"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-converter@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/dom-converter/download/dom-converter-0.2.0.tgz"
  integrity sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=
  dependencies:
    utila "~0.4"

dom-serializer@0:
  version "0.2.2"
  resolved "https://registry.nlark.com/dom-serializer/download/dom-serializer-0.2.2.tgz"
  integrity sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

dom-serializer@^1.0.1:
  version "1.3.2"
  resolved "https://registry.nlark.com/dom-serializer/download/dom-serializer-1.3.2.tgz"
  integrity sha1-YgZDfTLO767HFhgDIwx6ILwbTZE=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

domain-browser@^1.1.1:
  version "1.2.0"
  resolved "https://registry.nlark.com/domain-browser/download/domain-browser-1.2.0.tgz"
  integrity sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=

domelementtype@1:
  version "1.3.1"
  resolved "https://registry.nlark.com/domelementtype/download/domelementtype-1.3.1.tgz"
  integrity sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=

domelementtype@^2.0.1, domelementtype@^2.2.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/domelementtype/download/domelementtype-2.2.0.tgz"
  integrity sha1-mgtsJ4LtahxzI9QiZxg9+b2LHVc=

domhandler@^4.0.0, domhandler@^4.2.0, domhandler@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/domhandler/download/domhandler-4.3.0.tgz"
  integrity sha512-fC0aXNQXqKSFTr2wDNZDhsEYjCiYsDWl3D01kwt25hm1YIPyDGHvvi3rw+PLqHAl/m71MaiF7d5zvBr0p5UB2g==
  dependencies:
    domelementtype "^2.2.0"

domutils@^1.7.0:
  version "1.7.0"
  resolved "https://registry.nlark.com/domutils/download/domutils-1.7.0.tgz?cache=0&sync_timestamp=1630106606599&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdomutils%2Fdownload%2Fdomutils-1.7.0.tgz"
  integrity sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^2.5.2, domutils@^2.8.0:
  version "2.8.0"
  resolved "https://registry.nlark.com/domutils/download/domutils-2.8.0.tgz?cache=0&sync_timestamp=1630106606599&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdomutils%2Fdownload%2Fdomutils-2.8.0.tgz"
  integrity sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

dot-prop@^5.2.0:
  version "5.3.0"
  resolved "https://registry.nlark.com/dot-prop/download/dot-prop-5.3.0.tgz"
  integrity sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=
  dependencies:
    is-obj "^2.0.0"

dotenv-expand@^5.1.0:
  version "5.1.0"
  resolved "https://registry.nlark.com/dotenv-expand/download/dotenv-expand-5.1.0.tgz"
  integrity sha1-P7rwIL/XlIhAcuomsel5HUWmKfA=

dotenv@^8.2.0:
  version "8.6.0"
  resolved "https://registry.nlark.com/dotenv/download/dotenv-8.6.0.tgz"
  integrity sha1-Bhr2ZNGff02PxuT/m1hM4jety4s=

duplexer@^0.1.1:
  version "0.1.2"
  resolved "https://registry.nlark.com/duplexer/download/duplexer-0.1.2.tgz"
  integrity sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=

duplexify@^3.4.2, duplexify@^3.6.0:
  version "3.7.1"
  resolved "https://registry.nlark.com/duplexify/download/duplexify-3.7.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fduplexify%2Fdownload%2Fduplexify-3.7.1.tgz"
  integrity sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

easy-stack@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/easy-stack/download/easy-stack-1.0.1.tgz"
  integrity sha1-iv5CZGJpiMq7EfPHBMzQyDVBEGY=

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "https://registry.nlark.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

echarts@^5.2.2:
  version "5.2.2"
  resolved "https://registry.npmmirror.com/echarts/-/echarts-5.2.2.tgz"
  integrity sha512-yxuBfeIH5c+0FsoRP60w4De6omXhA06c7eUYBsC1ykB6Ys2yK5fSteIYWvkJ4xJVLQgCvAdO8C4mN6MLeJpBaw==
  dependencies:
    tslib "2.3.0"
    zrender "5.2.1"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/ee-first/download/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@^2.6.1:
  version "2.7.4"
  resolved "https://registry.npmmirror.com/ejs/download/ejs-2.7.4.tgz"
  integrity sha512-7vmuyh5+kuUyJKePhQfRQBhXV5Ce+RnaeeQArKu1EAMpL3WbgMt5WG6uQZpEVvYSSsxMXRKOewtDk9RaTKXRlA==

electron-to-chromium@^1.4.17:
  version "1.4.30"
  resolved "https://registry.npmmirror.com/electron-to-chromium/download/electron-to-chromium-1.4.30.tgz"
  integrity sha512-609z9sIMxDHg+TcR/VB3MXwH+uwtrYyeAwWc/orhnr90ixs6WVGSrt85CDLGUdNnLqCA7liv426V20EecjvflQ==

elegant-spinner@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/elegant-spinner/download/elegant-spinner-1.0.1.tgz?cache=0&sync_timestamp=1631023011636&other_urls=https%3A%2F%2Fregistry.nlark.com%2Felegant-spinner%2Fdownload%2Felegant-spinner-1.0.1.tgz"
  integrity sha1-2wQ1IcldfjA/2PNFvtwzSc+wcp4=

element-plus@^1.3.0-beta.1:
  version "1.3.0-beta.1"
  resolved "https://registry.npmmirror.com/element-plus/-/element-plus-1.3.0-beta.1.tgz"
  integrity sha512-q3vMaKElPpuSTeIF7kuDmMOE+N1YVCCIG3fshXpz6qgjnxPbgZumVM0qGfhr8DTu9JxRbBoDok49dqtX/BWn3w==
  dependencies:
    "@ctrl/tinycolor" "^3.4.0"
    "@element-plus/icons-vue" "^0.2.4"
    "@popperjs/core" "^2.10.2"
    "@vueuse/core" "^7.3.0"
    async-validator "^4.0.7"
    dayjs "^1.10.7"
    lodash "^4.17.21"
    memoize-one "^6.0.0"
    normalize-wheel-es "^1.1.1"

elliptic@^6.5.3:
  version "6.5.4"
  resolved "https://registry.nlark.com/elliptic/download/elliptic-6.5.4.tgz"
  integrity sha1-2jfOvTHnmhNn6UG1ku0fvr1Yq7s=
  dependencies:
    bn.js "^4.11.9"
    brorand "^1.1.0"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.1"
    inherits "^2.0.4"
    minimalistic-assert "^1.0.1"
    minimalistic-crypto-utils "^1.0.1"

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-7.0.3.tgz?cache=0&sync_timestamp=1632751408145&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Femoji-regex%2Fdownload%2Femoji-regex-7.0.3.tgz"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-8.0.0.tgz?cache=0&sync_timestamp=1632751408145&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Femoji-regex%2Fdownload%2Femoji-regex-8.0.0.tgz"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/emojis-list/download/emojis-list-2.1.0.tgz"
  integrity sha1-TapNnbAPmBmIDHn6RXrlsJof04k=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/emojis-list/download/emojis-list-3.0.0.tgz"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

encode-utf8@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/encode-utf8/-/encode-utf8-1.0.3.tgz"
  integrity sha512-ucAnuBEhUK4boH2HjVYG5Q2mQyPorvv0u/ocS+zhdw0S8AlHYY+GOFhP1Gio5z4icpP2ivFSvhtFjQi8+T9ppw==

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/encodeurl/download/encodeurl-1.0.2.tgz"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

end-of-stream@^1.0.0, end-of-stream@^1.1.0, end-of-stream@^1.4.1:
  version "1.4.4"
  resolved "https://registry.nlark.com/end-of-stream/download/end-of-stream-1.4.4.tgz?cache=0&sync_timestamp=1624607958717&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fend-of-stream%2Fdownload%2Fend-of-stream-1.4.4.tgz"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enhanced-resolve@^0.9.1:
  version "0.9.1"
  resolved "https://registry.nlark.com/enhanced-resolve/download/enhanced-resolve-0.9.1.tgz"
  integrity sha1-TW5omzcl+GCQknzMhs2fFjW4ni4=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.2.0"
    tapable "^0.1.8"

enhanced-resolve@^4.0.0, enhanced-resolve@^4.5.0:
  version "4.5.0"
  resolved "https://registry.nlark.com/enhanced-resolve/download/enhanced-resolve-4.5.0.tgz"
  integrity sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.5.0"
    tapable "^1.0.0"

entities@^2.0.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/entities/download/entities-2.2.0.tgz?cache=0&sync_timestamp=1628508126700&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fentities%2Fdownload%2Fentities-2.2.0.tgz"
  integrity sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=

errno@^0.1.3, errno@~0.1.7:
  version "0.1.8"
  resolved "https://registry.nlark.com/errno/download/errno-0.1.8.tgz"
  integrity sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=
  dependencies:
    prr "~1.0.1"

error-ex@^1.2.0, error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.nlark.com/error-ex/download/error-ex-1.3.2.tgz"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.6:
  version "2.0.6"
  resolved "https://registry.nlark.com/error-stack-parser/download/error-stack-parser-2.0.6.tgz"
  integrity sha1-WpmnB716TFinl5AtSNgoA+3mqtg=
  dependencies:
    stackframe "^1.1.1"

es-abstract@^1.19.0, es-abstract@^1.19.1:
  version "1.19.1"
  resolved "https://registry.npmmirror.com/es-abstract/download/es-abstract-1.19.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fes-abstract%2Fdownload%2Fes-abstract-1.19.1.tgz"
  integrity sha1-1IhXlodpFpWd547aoN9FZicRXsM=
  dependencies:
    call-bind "^1.0.2"
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    get-intrinsic "^1.1.1"
    get-symbol-description "^1.0.0"
    has "^1.0.3"
    has-symbols "^1.0.2"
    internal-slot "^1.0.3"
    is-callable "^1.2.4"
    is-negative-zero "^2.0.1"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.1"
    is-string "^1.0.7"
    is-weakref "^1.0.1"
    object-inspect "^1.11.0"
    object-keys "^1.1.1"
    object.assign "^4.1.2"
    string.prototype.trimend "^1.0.4"
    string.prototype.trimstart "^1.0.4"
    unbox-primitive "^1.0.1"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://registry.nlark.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://registry.nlark.com/escalade/download/escalade-3.1.1.tgz"
  integrity sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/escape-html/download/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.nlark.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

eslint-config-standard@^14.1.0:
  version "14.1.1"
  resolved "https://registry.nlark.com/eslint-config-standard/download/eslint-config-standard-14.1.1.tgz"
  integrity sha1-gwqOROeu995nRkl5rQa0BgJsVuo=

eslint-import-resolver-node@^0.3.3, eslint-import-resolver-node@^0.3.6:
  version "0.3.6"
  resolved "https://registry.nlark.com/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.6.tgz?cache=0&sync_timestamp=1629046496980&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-import-resolver-node%2Fdownload%2Feslint-import-resolver-node-0.3.6.tgz"
  integrity sha1-QEi5WDldqJZoJSAB29nsprg7rL0=
  dependencies:
    debug "^3.2.7"
    resolve "^1.20.0"

eslint-import-resolver-webpack@^0.12.1:
  version "0.12.2"
  resolved "https://registry.npmmirror.com/eslint-import-resolver-webpack/download/eslint-import-resolver-webpack-0.12.2.tgz?cache=0&sync_timestamp=1634713960031&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-import-resolver-webpack%2Fdownload%2Feslint-import-resolver-webpack-0.12.2.tgz"
  integrity sha1-dp6GzQx1KhU2wZhV69kKoUzjhO4=
  dependencies:
    array-find "^1.0.0"
    debug "^2.6.9"
    enhanced-resolve "^0.9.1"
    find-root "^1.1.0"
    has "^1.0.3"
    interpret "^1.2.0"
    lodash "^4.17.15"
    node-libs-browser "^1.0.0 || ^2.0.0"
    resolve "^1.13.1"
    semver "^5.7.1"

eslint-loader@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmmirror.com/eslint-loader/download/eslint-loader-2.2.1.tgz"
  integrity sha1-KLnBLaVAV68IReKmEScBova/gzc=
  dependencies:
    loader-fs-cache "^1.0.0"
    loader-utils "^1.0.2"
    object-assign "^4.0.1"
    object-hash "^1.1.4"
    rimraf "^2.6.1"

eslint-module-utils@^2.7.1:
  version "2.7.1"
  resolved "https://registry.npmmirror.com/eslint-module-utils/download/eslint-module-utils-2.7.1.tgz"
  integrity sha1-tDUAHJ+N1Kt/bQ78rkuWltTCS3w=
  dependencies:
    debug "^3.2.7"
    find-up "^2.1.0"
    pkg-dir "^2.0.0"

eslint-plugin-es@^3.0.0:
  version "3.0.1"
  resolved "https://registry.nlark.com/eslint-plugin-es/download/eslint-plugin-es-3.0.1.tgz"
  integrity sha1-dafN/czdwFiZNK7rOEF18iHFeJM=
  dependencies:
    eslint-utils "^2.0.0"
    regexpp "^3.0.0"

eslint-plugin-import@^2.20.2:
  version "2.25.3"
  resolved "https://registry.npmmirror.com/eslint-plugin-import/download/eslint-plugin-import-2.25.3.tgz"
  integrity sha512-RzAVbby+72IB3iOEL8clzPLzL3wpDrlwjsTBAQXgyp5SeTqqY+0bFubwuo+y/HLhNZcXV4XqTBO4LGsfyHIDXg==
  dependencies:
    array-includes "^3.1.4"
    array.prototype.flat "^1.2.5"
    debug "^2.6.9"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.6"
    eslint-module-utils "^2.7.1"
    has "^1.0.3"
    is-core-module "^2.8.0"
    is-glob "^4.0.3"
    minimatch "^3.0.4"
    object.values "^1.1.5"
    resolve "^1.20.0"
    tsconfig-paths "^3.11.0"

eslint-plugin-node@^11.1.0:
  version "11.1.0"
  resolved "https://registry.nlark.com/eslint-plugin-node/download/eslint-plugin-node-11.1.0.tgz"
  integrity sha1-yVVEQW7kraJnQKMEdO78VALcZx0=
  dependencies:
    eslint-plugin-es "^3.0.0"
    eslint-utils "^2.0.0"
    ignore "^5.1.1"
    minimatch "^3.0.4"
    resolve "^1.10.1"
    semver "^6.1.0"

eslint-plugin-promise@^4.2.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/eslint-plugin-promise/download/eslint-plugin-promise-4.3.1.tgz"
  integrity sha1-YUhd8qNZ4DFJ/a/AposOAwrSrEU=

eslint-plugin-standard@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/eslint-plugin-standard/download/eslint-plugin-standard-4.1.0.tgz"
  integrity sha1-DDvzpn6FP4u7xYD7SUX78W9Bt8U=

eslint-plugin-vue@^8.2.0:
  version "8.2.0"
  resolved "https://registry.npmmirror.com/eslint-plugin-vue/-/eslint-plugin-vue-8.2.0.tgz"
  integrity sha512-cLIdTuOAMXyHeQ4drYKcZfoyzdwdBpH279X8/N0DgmotEI9yFKb5O/cAgoie/CkQZCH/MOmh0xw/KEfS90zY2A==
  dependencies:
    eslint-utils "^3.0.0"
    natural-compare "^1.4.0"
    semver "^7.3.5"
    vue-eslint-parser "^8.0.1"

eslint-scope@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-4.0.3.tgz?cache=0&sync_timestamp=1637466963861&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-scope%2Fdownload%2Feslint-scope-4.0.3.tgz"
  integrity sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^5.0.0, eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-5.1.1.tgz?cache=0&sync_timestamp=1637466963861&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-scope%2Fdownload%2Feslint-scope-5.1.1.tgz"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-6.0.0.tgz"
  integrity sha512-uRDL9MWmQCkaFus8RF5K9/L/2fn+80yoW3jkD53l4shjCh26fCtvJGasxjUqP5OT87SYTxCVA3BwTUzuELx9kA==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-utils@^1.4.3:
  version "1.4.3"
  resolved "https://registry.nlark.com/eslint-utils/download/eslint-utils-1.4.3.tgz?cache=0&sync_timestamp=1624608042629&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-utils%2Fdownload%2Feslint-utils-1.4.3.tgz"
  integrity sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-utils@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/eslint-utils/download/eslint-utils-2.1.0.tgz?cache=0&sync_timestamp=1624608042629&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-utils%2Fdownload%2Feslint-utils-2.1.0.tgz"
  integrity sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-utils@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/eslint-utils/download/eslint-utils-3.0.0.tgz?cache=0&sync_timestamp=1624608042629&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint-utils%2Fdownload%2Feslint-utils-3.0.0.tgz"
  integrity sha1-iuuvrOc0W7M1WdsKHxOh0tSMNnI=
  dependencies:
    eslint-visitor-keys "^2.0.0"

eslint-visitor-keys@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz?cache=0&sync_timestamp=1636378420914&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-visitor-keys%2Fdownload%2Feslint-visitor-keys-1.3.0.tgz"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint-visitor-keys@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz?cache=0&sync_timestamp=1636378420914&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-visitor-keys%2Fdownload%2Feslint-visitor-keys-2.1.0.tgz"
  integrity sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=

eslint-visitor-keys@^3.0.0, eslint-visitor-keys@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.1.0.tgz"
  integrity sha512-yWJFpu4DtjsWKkt5GeNBBuZMlNcYVs6vRCLoCVEJrTjaSB6LC98gFipNK/erM2Heg/E8mIK+hXG/pJMLK+eRZA==

eslint@^6.7.2:
  version "6.8.0"
  resolved "https://registry.npmmirror.com/eslint/download/eslint-6.8.0.tgz"
  integrity sha512-K+Iayyo2LtyYhDSYwz5D5QdWw0hCacNzyq1Y821Xna2xSJj7cijoLLYmLxTQgcgZ9mC61nryMy9S7GRbYpI5Ig==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.10.0"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^3.0.0"
    eslint-scope "^5.0.0"
    eslint-utils "^1.4.3"
    eslint-visitor-keys "^1.1.0"
    espree "^6.1.2"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^5.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.0.0"
    globals "^12.1.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    inquirer "^7.0.0"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.14"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.3"
    progress "^2.0.0"
    regexpp "^2.0.1"
    semver "^6.1.2"
    strip-ansi "^5.2.0"
    strip-json-comments "^3.0.1"
    table "^5.2.3"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^6.1.2, espree@^6.2.1:
  version "6.2.1"
  resolved "https://registry.npmmirror.com/espree/download/espree-6.2.1.tgz"
  integrity sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o=
  dependencies:
    acorn "^7.1.1"
    acorn-jsx "^5.2.0"
    eslint-visitor-keys "^1.1.0"

espree@^9.0.0:
  version "9.3.0"
  resolved "https://registry.npmmirror.com/espree/-/espree-9.3.0.tgz"
  integrity sha512-d/5nCsb0JcqsSEeQzFZ8DH1RmxPcglRWh24EFTlUEmCKoehXGdpsx0RkHDubqUI8LSAIKMQp4r9SzQ3n+sm4HQ==
  dependencies:
    acorn "^8.7.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^3.1.0"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.nlark.com/esprima/download/esprima-4.0.1.tgz"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.0.1, esquery@^1.4.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/esquery/download/esquery-1.4.0.tgz"
  integrity sha1-IUj/w4uC6McFff7UhCWz5h8PJKU=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.1.0, esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/esrecurse/download/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/estraverse/download/estraverse-4.3.0.tgz?cache=0&sync_timestamp=1635237716974&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-4.3.0.tgz"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://registry.npmmirror.com/estraverse/download/estraverse-5.3.0.tgz?cache=0&sync_timestamp=1635237716974&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/estree-walker/download/estree-walker-2.0.2.tgz"
  integrity sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.nlark.com/esutils/download/esutils-2.0.3.tgz"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.nlark.com/etag/download/etag-1.8.1.tgz"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

event-pubsub@4.3.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/event-pubsub/download/event-pubsub-4.3.0.tgz"
  integrity sha1-9o2Ba8KfHsAsU53FjI3UDOcss24=

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "https://registry.nlark.com/eventemitter3/download/eventemitter3-4.0.7.tgz"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.0.0:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/events/download/events-3.3.0.tgz?cache=0&sync_timestamp=1636449286836&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fevents%2Fdownload%2Fevents-3.3.0.tgz"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

eventsource@^1.0.7:
  version "1.1.0"
  resolved "https://registry.nlark.com/eventsource/download/eventsource-1.1.0.tgz"
  integrity sha1-AOjKfJIQnpSw3fMtrGd9hBAoz68=
  dependencies:
    original "^1.0.0"

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz"
  integrity sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

execa@^0.8.0:
  version "0.8.0"
  resolved "https://registry.npmmirror.com/execa/download/execa-0.8.0.tgz?cache=0&sync_timestamp=1637147199964&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexeca%2Fdownload%2Fexeca-0.8.0.tgz"
  integrity sha1-2NdrvBtVIX7RkP1t1J08d07PyNo=
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/execa/download/execa-1.0.0.tgz?cache=0&sync_timestamp=1637147199964&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexeca%2Fdownload%2Fexeca-1.0.0.tgz"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^2.0.3:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/execa/download/execa-2.1.0.tgz?cache=0&sync_timestamp=1637147199964&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexeca%2Fdownload%2Fexeca-2.1.0.tgz"
  integrity sha1-5dPs2DfSpg7FDz2nj9OXZ3R7vpk=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^3.0.0"
    onetime "^5.1.0"
    p-finally "^2.0.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

execa@^3.3.0:
  version "3.4.0"
  resolved "https://registry.npmmirror.com/execa/download/execa-3.4.0.tgz?cache=0&sync_timestamp=1637147199964&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexeca%2Fdownload%2Fexeca-3.4.0.tgz"
  integrity sha1-wI7UVQ72XYWPrCaf/IVyRG8364k=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    p-finally "^2.0.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

exit-on-epipe@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/exit-on-epipe/-/exit-on-epipe-1.0.1.tgz"
  integrity sha512-h2z5mrROTxce56S+pnvAV890uu7ls7f1kEvVGJbw1OlFH3/mlJ5bkXu0KRyW94v37zzHPiUd55iLn3DA7TjWpw==

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "https://registry.nlark.com/expand-brackets/download/expand-brackets-2.1.4.tgz"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

express@^4.16.3, express@^4.17.1:
  version "4.17.2"
  resolved "https://registry.npmmirror.com/express/download/express-4.17.2.tgz"
  integrity sha512-oxlxJxcQlYwqPWKVJJtvQiwHgosH/LrLSPA+H4UxpyvSS6jC5aH+5MoHFM+KABgTOt0APue4w66Ha8jCUo9QGg==
  dependencies:
    accepts "~1.3.7"
    array-flatten "1.1.1"
    body-parser "1.19.1"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.4.1"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.2"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "~1.1.2"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.9.6"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.17.2"
    serve-static "1.14.2"
    setprototypeof "1.2.0"
    statuses "~1.5.0"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/extend-shallow/download/extend-shallow-2.0.1.tgz"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@~3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/extend/download/extend-3.0.2.tgz"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://registry.nlark.com/external-editor/download/external-editor-3.1.0.tgz"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.4:
  version "2.0.4"
  resolved "https://registry.nlark.com/extglob/download/extglob-2.0.4.tgz"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extsprintf@1.3.0, extsprintf@^1.2.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/extsprintf/download/extsprintf-1.3.0.tgz?cache=0&sync_timestamp=1635889863507&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fextsprintf%2Fdownload%2Fextsprintf-1.3.0.tgz"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "https://registry.nlark.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz?cache=0&sync_timestamp=1624607945641&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffast-deep-equal%2Fdownload%2Ffast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-glob@^2.2.6:
  version "2.2.7"
  resolved "https://registry.nlark.com/fast-glob/download/fast-glob-2.2.7.tgz?cache=0&sync_timestamp=1625772623128&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffast-glob%2Fdownload%2Ffast-glob-2.2.7.tgz"
  integrity sha1-aVOFfDr6R1//ku5gFdUtpwpM050=
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    glob-parent "^3.1.0"
    is-glob "^4.0.0"
    merge2 "^1.2.3"
    micromatch "^3.1.10"

fast-glob@^3.0.3, fast-glob@^3.1.1:
  version "3.2.7"
  resolved "https://registry.nlark.com/fast-glob/download/fast-glob-3.2.7.tgz?cache=0&sync_timestamp=1625772623128&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffast-glob%2Fdownload%2Ffast-glob-3.2.7.tgz"
  integrity sha1-/Wy3otfpqnp4RhEehaGW1rL3ZqE=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "https://registry.nlark.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastq@^1.6.0:
  version "1.13.0"
  resolved "https://registry.nlark.com/fastq/download/fastq-1.13.0.tgz?cache=0&sync_timestamp=1631609698424&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffastq%2Fdownload%2Ffastq-1.13.0.tgz"
  integrity sha1-YWdg+Ip1Jr38WWt8q4wYk4w2uYw=
  dependencies:
    reusify "^1.0.4"

faye-websocket@^0.11.3:
  version "0.11.4"
  resolved "https://registry.nlark.com/faye-websocket/download/faye-websocket-0.11.4.tgz"
  integrity sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=
  dependencies:
    websocket-driver ">=0.5.1"

figgy-pudding@^3.5.1:
  version "3.5.2"
  resolved "https://registry.nlark.com/figgy-pudding/download/figgy-pudding-3.5.2.tgz"
  integrity sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=

figures@^1.7.0:
  version "1.7.0"
  resolved "https://registry.nlark.com/figures/download/figures-1.7.0.tgz?cache=0&sync_timestamp=1625254221763&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffigures%2Fdownload%2Ffigures-1.7.0.tgz"
  integrity sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4=
  dependencies:
    escape-string-regexp "^1.0.5"
    object-assign "^4.1.0"

figures@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/figures/download/figures-2.0.0.tgz?cache=0&sync_timestamp=1625254221763&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffigures%2Fdownload%2Ffigures-2.0.0.tgz"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

figures@^3.0.0:
  version "3.2.0"
  resolved "https://registry.nlark.com/figures/download/figures-3.2.0.tgz?cache=0&sync_timestamp=1625254221763&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffigures%2Fdownload%2Ffigures-3.2.0.tgz"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^5.0.1:
  version "5.0.1"
  resolved "https://registry.nlark.com/file-entry-cache/download/file-entry-cache-5.0.1.tgz"
  integrity sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=
  dependencies:
    flat-cache "^2.0.1"

file-loader@^4.2.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/file-loader/download/file-loader-4.3.0.tgz?cache=0&sync_timestamp=1624607980029&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffile-loader%2Fdownload%2Ffile-loader-4.3.0.tgz"
  integrity sha1-eA8ED3KbPRgBnyBgX3I+hEuKWK8=
  dependencies:
    loader-utils "^1.2.3"
    schema-utils "^2.5.0"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

filemanager-webpack-plugin@^6.1.7:
  version "6.1.7"
  resolved "https://registry.npmmirror.com/filemanager-webpack-plugin/-/filemanager-webpack-plugin-6.1.7.tgz"
  integrity sha512-0hhPpmod5t0xy1hBSA9gXi0WlOHL3+x56IBt0b/VMhvbZ5/z6jakXvNOTuVmn4wOZwAORvAeH5qQ5Qs6NdPyiw==
  dependencies:
    "@types/archiver" "^5.1.1"
    archiver "^5.3.0"
    cpy "^8.1.2"
    del "^6.0.0"
    fs-extra "^10.0.0"
    is-glob "^4.0.1"
    schema-utils "^3.1.1"

filesize@^3.6.1:
  version "3.6.1"
  resolved "https://registry.npmmirror.com/filesize/download/filesize-3.6.1.tgz?cache=0&sync_timestamp=1635763993879&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffilesize%2Fdownload%2Ffilesize-3.6.1.tgz"
  integrity sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc=

fill-range@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/fill-range/download/fill-range-4.0.0.tgz"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.nlark.com/fill-range/download/fill-range-7.0.1.tgz"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/finalhandler/download/finalhandler-1.1.2.tgz"
  integrity sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-cache-dir@^0.1.1:
  version "0.1.1"
  resolved "https://registry.nlark.com/find-cache-dir/download/find-cache-dir-0.1.1.tgz"
  integrity sha1-yN765XyKUqinhPnjHFfHQumToLk=
  dependencies:
    commondir "^1.0.1"
    mkdirp "^0.5.1"
    pkg-dir "^1.0.0"

find-cache-dir@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz"
  integrity sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-cache-dir@^3.0.0, find-cache-dir@^3.3.1:
  version "3.3.2"
  resolved "https://registry.nlark.com/find-cache-dir/download/find-cache-dir-3.3.2.tgz"
  integrity sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-root@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/find-root/download/find-root-1.1.0.tgz"
  integrity sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ=

find-up@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-1.1.2.tgz?cache=0&sync_timestamp=1633618659233&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffind-up%2Fdownload%2Ffind-up-1.1.2.tgz"
  integrity sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-2.1.0.tgz?cache=0&sync_timestamp=1633618659233&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffind-up%2Fdownload%2Ffind-up-2.1.0.tgz"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-3.0.0.tgz?cache=0&sync_timestamp=1633618659233&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffind-up%2Fdownload%2Ffind-up-3.0.0.tgz"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/find-up/-/find-up-4.1.0.tgz"
  integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/flat-cache/download/flat-cache-2.0.1.tgz"
  integrity sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flatted@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/flatted/download/flatted-2.0.2.tgz?cache=0&sync_timestamp=1636473923308&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fflatted%2Fdownload%2Fflatted-2.0.2.tgz"
  integrity sha1-RXWyHivO50NKqb5mL0t7X5wrUTg=

flush-write-stream@^1.0.0:
  version "1.1.1"
  resolved "https://registry.nlark.com/flush-write-stream/download/flush-write-stream-1.1.1.tgz"
  integrity sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.3.6"

follow-redirects@^1.0.0, follow-redirects@^1.14.4:
  version "1.14.6"
  resolved "https://registry.npmmirror.com/follow-redirects/download/follow-redirects-1.14.6.tgz"
  integrity sha512-fhUl5EwSJbbl8AR+uYL2KQDxLkdSjZGR36xy46AO7cOMTrCMON6Sa28FmAnC2tRTDbd/Uuzz3aJBv7EBN7JH8A==

for-in@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/for-in/download/for-in-1.0.2.tgz"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://registry.nlark.com/forever-agent/download/forever-agent-0.6.1.tgz"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

"fork-ts-checker-webpack-plugin-v5@npm:fork-ts-checker-webpack-plugin@^5.0.11":
  version "5.2.1"
  resolved "https://registry.npmmirror.com/fork-ts-checker-webpack-plugin/download/fork-ts-checker-webpack-plugin-5.2.1.tgz"
  integrity sha1-eTJthpeXkG+osk4qvPlCH8gFRQ0=
  dependencies:
    "@babel/code-frame" "^7.8.3"
    "@types/json-schema" "^7.0.5"
    chalk "^4.1.0"
    cosmiconfig "^6.0.0"
    deepmerge "^4.2.2"
    fs-extra "^9.0.0"
    memfs "^3.1.2"
    minimatch "^3.0.4"
    schema-utils "2.7.0"
    semver "^7.3.2"
    tapable "^1.0.0"

fork-ts-checker-webpack-plugin@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/fork-ts-checker-webpack-plugin/download/fork-ts-checker-webpack-plugin-3.1.1.tgz"
  integrity sha1-oWQsDT5l9QwswXQunAqA9EH4axk=
  dependencies:
    babel-code-frame "^6.22.0"
    chalk "^2.4.1"
    chokidar "^3.3.0"
    micromatch "^3.1.10"
    minimatch "^3.0.4"
    semver "^5.6.0"
    tapable "^1.0.0"
    worker-rpc "^0.1.0"

form-data@~2.3.2:
  version "2.3.3"
  resolved "https://registry.nlark.com/form-data/download/form-data-2.3.3.tgz"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/forwarded/download/forwarded-0.2.0.tgz"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "https://registry.nlark.com/fragment-cache/download/fragment-cache-0.2.1.tgz"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.nlark.com/fresh/download/fresh-0.5.2.tgz"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

from2@^2.1.0:
  version "2.3.0"
  resolved "https://registry.nlark.com/from2/download/from2-2.3.0.tgz"
  integrity sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-constants@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/fs-constants/-/fs-constants-1.0.0.tgz"
  integrity sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==

fs-extra@^10.0.0:
  version "10.0.0"
  resolved "https://registry.npmmirror.com/fs-extra/-/fs-extra-10.0.0.tgz"
  integrity sha512-C5owb14u9eJwizKGdchcDUQeFtlSHHthBk8pbX9Vc1PFZrLombudjDnNns88aYslCyF6IY5SUw3Roz6xShcEIQ==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^7.0.1:
  version "7.0.1"
  resolved "https://registry.nlark.com/fs-extra/download/fs-extra-7.0.1.tgz"
  integrity sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-extra@^9.0.0:
  version "9.1.0"
  resolved "https://registry.nlark.com/fs-extra/download/fs-extra-9.1.0.tgz"
  integrity sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-monkey@1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/fs-monkey/download/fs-monkey-1.0.3.tgz"
  integrity sha1-rjrJLVO7Mo7+DpodlUH2rY1I4tM=

fs-write-stream-atomic@^1.0.8:
  version "1.0.10"
  resolved "https://registry.npmmirror.com/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz"
  integrity sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.2.7:
  version "1.2.13"
  resolved "https://registry.yarnpkg.com/fsevents/-/fsevents-1.2.13.tgz#f325cb0455592428bcf11b383370ef70e3bfcc38"
  integrity sha512-oWb1Z6mkHIskLzEJ/XWX0srkpkTQ7vaopMQkyaEIoq0fmtFVxOthb8cCxeT+p3ynTdkk/RZwbgG4brR5BeWECw==
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

fsevents@~2.3.2:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a"
  integrity sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==

fstream@^1.0.0, fstream@^1.0.12:
  version "1.0.12"
  resolved "https://registry.nlark.com/fstream/download/fstream-1.0.12.tgz"
  integrity sha1-Touo7i1Ivk99DeUFRVVI6uWTIEU=
  dependencies:
    graceful-fs "^4.1.2"
    inherits "~2.0.0"
    mkdirp ">=0.5 0"
    rimraf "2"

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/function-bind/download/function-bind-1.1.1.tgz"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

gauge@~2.7.3:
  version "2.7.4"
  resolved "https://registry.npmmirror.com/gauge/download/gauge-2.7.4.tgz"
  integrity sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

gaze@^1.0.0:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/gaze/download/gaze-1.1.3.tgz"
  integrity sha1-xEFzPhO5J6yMD/C0w7Az8ogSkko=
  dependencies:
    globule "^1.0.0"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.nlark.com/gensync/download/gensync-1.0.0-beta.2.tgz"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.1, get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://registry.nlark.com/get-caller-file/download/get-caller-file-2.0.5.tgz"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.0.2, get-intrinsic@^1.1.0, get-intrinsic@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/get-intrinsic/download/get-intrinsic-1.1.1.tgz"
  integrity sha1-FfWfN2+FXERpY5SPDSTNNje0q8Y=
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"

get-own-enumerable-property-symbols@^3.0.0:
  version "3.0.2"
  resolved "https://registry.nlark.com/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.2.tgz"
  integrity sha1-tf3nfyLL4185C04ImSLFC85u9mQ=

get-stdin@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/get-stdin/download/get-stdin-4.0.1.tgz"
  integrity sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4=

get-stream@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/get-stream/download/get-stream-3.0.0.tgz"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/get-stream/download/get-stream-4.1.0.tgz"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-stream@^5.0.0:
  version "5.2.0"
  resolved "https://registry.nlark.com/get-stream/download/get-stream-5.2.0.tgz"
  integrity sha1-SWaheV7lrOZecGxLe+txJX1uItM=
  dependencies:
    pump "^3.0.0"

get-symbol-description@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/get-symbol-description/download/get-symbol-description-1.0.0.tgz"
  integrity sha1-f9uByQAQH71WTdXxowr1qtweWNY=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "https://registry.nlark.com/get-value/download/get-value-2.0.6.tgz"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://registry.nlark.com/getpass/download/getpass-0.1.7.tgz"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/glob-parent/download/glob-parent-3.1.0.tgz?cache=0&sync_timestamp=1632954190616&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-3.1.0.tgz"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@^5.0.0, glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/glob-parent/download/glob-parent-5.1.2.tgz?cache=0&sync_timestamp=1632954190616&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz"
  integrity sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=

glob@^7.0.0, glob@^7.0.3, glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/glob/download/glob-7.2.0.tgz"
  integrity sha512-lmLf6gtyrPq8tTjSmrO94wBeQbFR3HbLHbuyD69wuyQkImp2hWqMGB47OX65FBkPffO641IP9jWa1z4ivqG26Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@~7.1.1:
  version "7.1.7"
  resolved "https://registry.npmmirror.com/glob/download/glob-7.1.7.tgz"
  integrity sha512-OvD9ENzPLbegENnYP5UUfJIirTg4+XwMWGaQfQTY0JenxNvvIKP3U3/tAQSPIu/lHxXYSZmpXlUHeqAIdKzBLQ==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npmmirror.com/globals/download/globals-11.12.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-11.12.0.tgz"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^12.1.0:
  version "12.4.0"
  resolved "https://registry.npmmirror.com/globals/download/globals-12.4.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-12.4.0.tgz"
  integrity sha1-oYgTV2pBsAokqX5/gVkYwuGZJfg=
  dependencies:
    type-fest "^0.8.1"

globby@^10.0.1:
  version "10.0.2"
  resolved "https://registry.nlark.com/globby/download/globby-10.0.2.tgz"
  integrity sha1-J3WT50WsqkZGw6tBEonsR6A5JUM=
  dependencies:
    "@types/glob" "^7.1.1"
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.0.3"
    glob "^7.1.3"
    ignore "^5.1.1"
    merge2 "^1.2.3"
    slash "^3.0.0"

globby@^11.0.1, globby@^11.0.3:
  version "11.0.4"
  resolved "https://registry.nlark.com/globby/download/globby-11.0.4.tgz"
  integrity sha1-LLr/d8Lypi5x6bKBOme5ejowAaU=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.1.1"
    ignore "^5.1.4"
    merge2 "^1.3.0"
    slash "^3.0.0"

globby@^6.1.0:
  version "6.1.0"
  resolved "https://registry.nlark.com/globby/download/globby-6.1.0.tgz"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globby@^7.1.1:
  version "7.1.1"
  resolved "https://registry.nlark.com/globby/download/globby-7.1.1.tgz"
  integrity sha1-+yzP+UAfhgCUXfral0QMypcrhoA=
  dependencies:
    array-union "^1.0.1"
    dir-glob "^2.0.0"
    glob "^7.1.2"
    ignore "^3.3.5"
    pify "^3.0.0"
    slash "^1.0.0"

globby@^9.2.0:
  version "9.2.0"
  resolved "https://registry.nlark.com/globby/download/globby-9.2.0.tgz"
  integrity sha1-/QKacGxwPSm90XD0tts6P3p8tj0=
  dependencies:
    "@types/glob" "^7.1.1"
    array-union "^1.0.2"
    dir-glob "^2.2.2"
    fast-glob "^2.2.6"
    glob "^7.1.3"
    ignore "^4.0.3"
    pify "^4.0.1"
    slash "^2.0.0"

globule@^1.0.0:
  version "1.3.3"
  resolved "https://registry.nlark.com/globule/download/globule-1.3.3.tgz"
  integrity sha1-gRkZ7qwatzROkF8uO+gKE0R5c8I=
  dependencies:
    glob "~7.1.1"
    lodash "~4.17.10"
    minimatch "~3.0.2"

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.2, graceful-fs@^4.2.4:
  version "4.2.8"
  resolved "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.8.tgz"
  integrity sha1-5BK40z9eAGWTy9PO5t+fLOu+gCo=

gzip-size@^5.0.0:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/gzip-size/download/gzip-size-5.1.1.tgz"
  integrity sha1-y5vuaS+HwGErIyhAqHOQTkwTUnQ=
  dependencies:
    duplexer "^0.1.1"
    pify "^4.0.1"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/handle-thing/download/handle-thing-2.0.1.tgz"
  integrity sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=

har-schema@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/har-schema/download/har-schema-2.0.0.tgz"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "https://registry.npmmirror.com/har-validator/download/har-validator-5.1.5.tgz"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/has-ansi/download/has-ansi-2.0.0.tgz?cache=0&sync_timestamp=1631558652943&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhas-ansi%2Fdownload%2Fhas-ansi-2.0.0.tgz"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-bigints@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/has-bigints/download/has-bigints-1.0.1.tgz"
  integrity sha1-ZP5qywIGc+O3jbA1pa9pqp0HsRM=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/has-flag/download/has-flag-3.0.0.tgz?cache=0&sync_timestamp=1626716578584&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhas-flag%2Fdownload%2Fhas-flag-3.0.0.tgz"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/has-flag/download/has-flag-4.0.0.tgz?cache=0&sync_timestamp=1626716578584&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhas-flag%2Fdownload%2Fhas-flag-4.0.0.tgz"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-glob@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/has-glob/-/has-glob-1.0.0.tgz"
  integrity "sha1-mqqe7b/7G6OZCnsAEPtnjuAIEgc= sha512-D+8A457fBShSEI3tFCj65PAbT++5sKiFtdCdOam0gnfBgw9D277OERk+HM9qYJXmdVLZ/znez10SqHN0BBQ50g=="
  dependencies:
    is-glob "^3.0.0"

has-symbols@^1.0.1, has-symbols@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/has-symbols/download/has-symbols-1.0.2.tgz"
  integrity sha1-Fl0wcMADCXUqEjakeTMeOsVvFCM=

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/has-tostringtag/download/has-tostringtag-1.0.0.tgz"
  integrity sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=
  dependencies:
    has-symbols "^1.0.2"

has-unicode@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/has-unicode/download/has-unicode-2.0.1.tgz"
  integrity sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=

has-value@^0.3.1:
  version "0.3.1"
  resolved "https://registry.nlark.com/has-value/download/has-value-0.3.1.tgz"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/has-value/download/has-value-1.0.0.tgz"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "https://registry.nlark.com/has-values/download/has-values-0.1.4.tgz"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/has-values/download/has-values-1.0.0.tgz"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.0, has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/has/download/has-1.0.3.tgz"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

hash-base@^3.0.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/hash-base/download/hash-base-3.1.0.tgz"
  integrity sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=
  dependencies:
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

hash-sum@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/hash-sum/download/hash-sum-1.0.2.tgz"
  integrity sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ=

hash-sum@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/hash-sum/download/hash-sum-2.0.0.tgz"
  integrity sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo=

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "https://registry.nlark.com/hash.js/download/hash.js-1.1.7.tgz"
  integrity sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

he@1.2.x:
  version "1.2.0"
  resolved "https://registry.nlark.com/he/download/he-1.2.0.tgz"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

hex-color-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/hex-color-regex/download/hex-color-regex-1.1.0.tgz"
  integrity sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4=

highlight.js@^10.7.1:
  version "10.7.3"
  resolved "https://registry.npmmirror.com/highlight.js/download/highlight.js-10.7.3.tgz"
  integrity sha512-tzcUFauisWKNHaRkN4Wjl/ZA07gENAjFl3J/c480dprkGTg5EQstgaNFqBfUqCq54kZRIEcreTsAgF/m2quD7A==

hmac-drbg@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/hmac-drbg/download/hmac-drbg-1.0.1.tgz"
  integrity sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

hoopy@^0.1.4:
  version "0.1.4"
  resolved "https://registry.nlark.com/hoopy/download/hoopy-0.1.4.tgz"
  integrity sha1-YJIH1mEQADOpqUAq096mdzgcGx0=

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "https://registry.npmmirror.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://registry.nlark.com/hpack.js/download/hpack.js-2.1.6.tgz"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

hsl-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/hsl-regex/download/hsl-regex-1.0.0.tgz"
  integrity sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4=

hsla-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/hsla-regex/download/hsla-regex-1.0.0.tgz"
  integrity sha1-wc56MWjIxmFAM6S194d/OyJfnDg=

html-entities@^1.3.1:
  version "1.4.0"
  resolved "https://registry.nlark.com/html-entities/download/html-entities-1.4.0.tgz"
  integrity sha1-z70bAdKvr5rcobEK59/6uYxx0tw=

html-minifier@^3.2.3:
  version "3.5.21"
  resolved "https://registry.nlark.com/html-minifier/download/html-minifier-3.5.21.tgz"
  integrity sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw=
  dependencies:
    camel-case "3.0.x"
    clean-css "4.2.x"
    commander "2.17.x"
    he "1.2.x"
    param-case "2.1.x"
    relateurl "0.2.x"
    uglify-js "3.4.x"

html-tags@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/html-tags/download/html-tags-2.0.0.tgz"
  integrity sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos=

html-tags@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/html-tags/download/html-tags-3.1.0.tgz"
  integrity sha1-e15vfmZen7QfMAB+2eDUHpf7IUA=

html-webpack-plugin@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/html-webpack-plugin/download/html-webpack-plugin-3.2.0.tgz"
  integrity sha512-Br4ifmjQojUP4EmHnRBoUIYcZ9J7M4bTMcm7u6xoIAIuq2Nte4TzXX0533owvkQKQD1WeMTTTyD4Ni4QKxS0Bg==
  dependencies:
    html-minifier "^3.2.3"
    loader-utils "^0.2.16"
    lodash "^4.17.3"
    pretty-error "^2.0.2"
    tapable "^1.0.0"
    toposort "^1.0.0"
    util.promisify "1.0.0"

htmlparser2@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/htmlparser2/download/htmlparser2-6.1.0.tgz?cache=0&sync_timestamp=1636640945377&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhtmlparser2%2Fdownload%2Fhtmlparser2-6.1.0.tgz"
  integrity sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.0.0"
    domutils "^2.5.2"
    entities "^2.0.0"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://registry.nlark.com/http-deceiver/download/http-deceiver-1.2.7.tgz"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@1.8.1:
  version "1.8.1"
  resolved "https://registry.npmmirror.com/http-errors/download/http-errors-1.8.1.tgz"
  integrity sha512-Kpk9Sm7NmI+RHhnj6OIWDI1d6fIoFAtFt9RLaTMRlg/8w49juAStsrBgp0Dp4OdxdVbRIeKhtCUvoi/RuAhO4g==
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.1"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://registry.npmmirror.com/http-errors/download/http-errors-1.6.3.tgz"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-parser-js@>=0.5.1:
  version "0.5.5"
  resolved "https://registry.npmmirror.com/http-parser-js/download/http-parser-js-0.5.5.tgz"
  integrity sha512-x+JVEkO2PoM8qqpbPbOL3cqHPwerep7OwzK7Ay+sMQjKzaKCqWvjoXm5tqMP9tXWWTnTzAjIhXg+J99XYuPhPA==

http-proxy-middleware@0.19.1:
  version "0.19.1"
  resolved "https://registry.nlark.com/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz?cache=0&sync_timestamp=1625174336093&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhttp-proxy-middleware%2Fdownload%2Fhttp-proxy-middleware-0.19.1.tgz"
  integrity sha1-GDx9xKoUeRUDBkmMIQza+WCApDo=
  dependencies:
    http-proxy "^1.17.0"
    is-glob "^4.0.0"
    lodash "^4.17.11"
    micromatch "^3.1.10"

http-proxy-middleware@^1.0.0:
  version "1.3.1"
  resolved "https://registry.nlark.com/http-proxy-middleware/download/http-proxy-middleware-1.3.1.tgz?cache=0&sync_timestamp=1625174336093&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhttp-proxy-middleware%2Fdownload%2Fhttp-proxy-middleware-1.3.1.tgz"
  integrity sha1-Q3ANbZ7st0Gb8IahKND3IF2etmU=
  dependencies:
    "@types/http-proxy" "^1.17.5"
    http-proxy "^1.18.1"
    is-glob "^4.0.1"
    is-plain-obj "^3.0.0"
    micromatch "^4.0.2"

http-proxy@^1.17.0, http-proxy@^1.18.1:
  version "1.18.1"
  resolved "https://registry.nlark.com/http-proxy/download/http-proxy-1.18.1.tgz"
  integrity sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/http-signature/download/http-signature-1.2.0.tgz?cache=0&sync_timestamp=1637178703812&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-signature%2Fdownload%2Fhttp-signature-1.2.0.tgz"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/https-browserify/download/https-browserify-1.0.0.tgz"
  integrity sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=

human-signals@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/human-signals/download/human-signals-1.1.1.tgz"
  integrity sha1-xbHNFPUK6uCatsWf5jujOV/k36M=

iconv-lite@0.4.24, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "https://registry.nlark.com/iconv-lite/download/iconv-lite-0.4.24.tgz"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

icss-utils@^4.0.0, icss-utils@^4.1.1:
  version "4.1.1"
  resolved "https://registry.nlark.com/icss-utils/download/icss-utils-4.1.1.tgz"
  integrity sha1-IRcLU3ie4nRHwvR91oMIFAP5pGc=
  dependencies:
    postcss "^7.0.14"

ieee754@^1.1.13, ieee754@^1.1.4:
  version "1.2.1"
  resolved "https://registry.nlark.com/ieee754/download/ieee754-1.2.1.tgz"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

iferr@^0.1.5:
  version "0.1.5"
  resolved "https://registry.nlark.com/iferr/download/iferr-0.1.5.tgz"
  integrity sha1-xg7taebY/bazEEofy8ocGS3FtQE=

ignore@^3.3.5:
  version "3.3.10"
  resolved "https://registry.npmmirror.com/ignore/download/ignore-3.3.10.tgz"
  integrity sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM=

ignore@^4.0.3, ignore@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmmirror.com/ignore/download/ignore-4.0.6.tgz"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

ignore@^5.1.1, ignore@^5.1.4, ignore@^5.1.8:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/ignore/download/ignore-5.2.0.tgz"
  integrity sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==

immutable@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/immutable/-/immutable-4.0.0.tgz"
  integrity sha512-zIE9hX70qew5qTUjSS7wi1iwj/l7+m54KWU247nhM3v806UdGj1yDndXj+IOYxxtW9zyLI+xqFNZjTuDaLUqFw==

import-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/import-cwd/download/import-cwd-2.1.0.tgz"
  integrity sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=
  dependencies:
    import-from "^2.1.0"

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/import-fresh/download/import-fresh-2.0.0.tgz"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0, import-fresh@^3.1.0:
  version "3.3.0"
  resolved "https://registry.nlark.com/import-fresh/download/import-fresh-3.3.0.tgz"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-from@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/import-from/download/import-from-2.1.0.tgz"
  integrity sha1-M1238qev/VOqpHHUuAId7ja387E=
  dependencies:
    resolve-from "^3.0.0"

import-local@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/import-local/download/import-local-2.0.0.tgz?cache=0&sync_timestamp=1633327412941&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fimport-local%2Fdownload%2Fimport-local-2.0.0.tgz"
  integrity sha1-VQcL44pZk88Y72236WH1vuXFoJ0=
  dependencies:
    pkg-dir "^3.0.0"
    resolve-cwd "^2.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.nlark.com/imurmurhash/download/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

in-publish@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/in-publish/download/in-publish-2.0.1.tgz"
  integrity sha1-lIsaU1yAMFYc6lIvc/ePS+NX4Aw=

indent-string@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/indent-string/download/indent-string-2.1.0.tgz"
  integrity sha1-ji1INIdCEhtKghi3oTfppSBJ3IA=
  dependencies:
    repeating "^2.0.0"

indent-string@^3.0.0:
  version "3.2.0"
  resolved "https://registry.nlark.com/indent-string/download/indent-string-3.2.0.tgz"
  integrity sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/indent-string/download/indent-string-4.0.0.tgz"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

indexes-of@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/indexes-of/download/indexes-of-1.0.1.tgz"
  integrity sha1-8w9xbI4r00bHtn0985FVZqfAVgc=

infer-owner@^1.0.3:
  version "1.0.4"
  resolved "https://registry.nlark.com/infer-owner/download/infer-owner-1.0.4.tgz"
  integrity sha1-xM78qo5RBRwqQLos6KPScpWvlGc=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.nlark.com/inflight/download/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.0, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://registry.nlark.com/inherits/download/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/inherits/download/inherits-2.0.1.tgz"
  integrity sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=

inherits@2.0.3:
  version "2.0.3"
  resolved "https://registry.nlark.com/inherits/download/inherits-2.0.3.tgz"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

inquirer@^7.0.0, inquirer@^7.1.0:
  version "7.3.3"
  resolved "https://registry.npmmirror.com/inquirer/download/inquirer-7.3.3.tgz"
  integrity sha512-JG3eIAj5V9CwcGvuOmoo6LB9kbAYT8HXffUl6memuszlwDC/qvFAJw49XJ5NROSFNPxp3iQg1GqkFhaY/CR0IA==
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.19"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.6.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

internal-ip@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/internal-ip/download/internal-ip-4.3.0.tgz"
  integrity sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc=
  dependencies:
    default-gateway "^4.2.0"
    ipaddr.js "^1.9.0"

internal-slot@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/internal-slot/download/internal-slot-1.0.3.tgz"
  integrity sha1-c0fjB97uovqsKsYgXUvH00ln9Zw=
  dependencies:
    get-intrinsic "^1.1.0"
    has "^1.0.3"
    side-channel "^1.0.4"

interpret@^1.2.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/interpret/download/interpret-1.4.0.tgz?cache=0&sync_timestamp=1624607963441&other_urls=https%3A%2F%2Fregistry.nlark.com%2Finterpret%2Fdownload%2Finterpret-1.4.0.tgz"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

ip-regex@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/ip-regex/download/ip-regex-2.1.0.tgz?cache=0&sync_timestamp=1624607942179&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fip-regex%2Fdownload%2Fip-regex-2.1.0.tgz"
  integrity sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=

ip@^1.1.0, ip@^1.1.5:
  version "1.1.5"
  resolved "https://registry.nlark.com/ip/download/ip-1.1.5.tgz?cache=0&sync_timestamp=1624607989560&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fip%2Fdownload%2Fip-1.1.5.tgz"
  integrity sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=

ipaddr.js@1.9.1, ipaddr.js@^1.9.0:
  version "1.9.1"
  resolved "https://registry.nlark.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

is-absolute-url@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/is-absolute-url/download/is-absolute-url-2.1.0.tgz?cache=0&sync_timestamp=1628691826939&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-absolute-url%2Fdownload%2Fis-absolute-url-2.1.0.tgz"
  integrity sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=

is-absolute-url@^3.0.3:
  version "3.0.3"
  resolved "https://registry.nlark.com/is-absolute-url/download/is-absolute-url-3.0.3.tgz?cache=0&sync_timestamp=1628691826939&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-absolute-url%2Fdownload%2Fis-absolute-url-3.0.3.tgz"
  integrity sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "https://registry.nlark.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "https://registry.nlark.com/is-arguments/download/is-arguments-1.1.1.tgz"
  integrity sha1-FbP4j9oB8ql/7ITKdhpWDxI++ps=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.nlark.com/is-arrayish/download/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.nlark.com/is-arrayish/download/is-arrayish-0.3.2.tgz"
  integrity sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "https://registry.nlark.com/is-bigint/download/is-bigint-1.0.4.tgz?cache=0&sync_timestamp=1628747500062&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-bigint%2Fdownload%2Fis-bigint-1.0.4.tgz"
  integrity sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=
  dependencies:
    has-bigints "^1.0.1"

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/is-binary-path/download/is-binary-path-1.0.1.tgz"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/is-binary-path/download/is-binary-path-2.1.0.tgz"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "https://registry.nlark.com/is-boolean-object/download/is-boolean-object-1.1.2.tgz"
  integrity sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "https://registry.nlark.com/is-buffer/download/is-buffer-1.1.6.tgz"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-callable@^1.1.4, is-callable@^1.2.4:
  version "1.2.4"
  resolved "https://registry.nlark.com/is-callable/download/is-callable-1.2.4.tgz"
  integrity sha1-RzAdWN0CWUB4ZVR4U99tYf5HGUU=

is-ci@^1.0.10:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/is-ci/download/is-ci-1.2.1.tgz?cache=0&sync_timestamp=1635261090481&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-ci%2Fdownload%2Fis-ci-1.2.1.tgz"
  integrity sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=
  dependencies:
    ci-info "^1.5.0"

is-color-stop@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-color-stop/download/is-color-stop-1.1.0.tgz"
  integrity sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=
  dependencies:
    css-color-names "^0.0.4"
    hex-color-regex "^1.1.0"
    hsl-regex "^1.0.0"
    hsla-regex "^1.0.0"
    rgb-regex "^1.0.1"
    rgba-regex "^1.0.0"

is-core-module@^2.2.0, is-core-module@^2.8.0:
  version "2.8.0"
  resolved "https://registry.npmmirror.com/is-core-module/download/is-core-module-2.8.0.tgz?cache=0&sync_timestamp=1634237302390&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-core-module%2Fdownload%2Fis-core-module-2.8.0.tgz"
  integrity sha1-AyEzbD0JJeSX/Zf12VyxFKXM1Ug=
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "https://registry.nlark.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "https://registry.nlark.com/is-date-object/download/is-date-object-1.0.5.tgz"
  integrity sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=
  dependencies:
    has-tostringtag "^1.0.0"

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "https://registry.nlark.com/is-descriptor/download/is-descriptor-0.1.6.tgz"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/is-descriptor/download/is-descriptor-1.0.2.tgz"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "https://registry.nlark.com/is-directory/download/is-directory-0.3.1.tgz"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-docker@^2.0.0:
  version "2.2.1"
  resolved "https://registry.nlark.com/is-docker/download/is-docker-2.2.1.tgz?cache=0&sync_timestamp=1630451108035&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-docker%2Fdownload%2Fis-docker-2.2.1.tgz"
  integrity sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://registry.nlark.com/is-extendable/download/is-extendable-0.1.1.tgz"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/is-extendable/download/is-extendable-1.0.1.tgz"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/is-extglob/download/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finite@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/is-finite/download/is-finite-1.1.0.tgz"
  integrity sha1-kEE1x3+0LAZB1qobzbxNqo2ggvM=

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-glob@^3.0.0, is-glob@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/is-glob/download/is-glob-3.1.0.tgz?cache=0&sync_timestamp=1632934498977&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-glob%2Fdownload%2Fis-glob-3.1.0.tgz"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.3.tgz?cache=0&sync_timestamp=1632934498977&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-glob%2Fdownload%2Fis-glob-4.0.3.tgz"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-negative-zero@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/is-negative-zero/download/is-negative-zero-2.0.2.tgz"
  integrity sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==

is-number-object@^1.0.4:
  version "1.0.6"
  resolved "https://registry.nlark.com/is-number-object/download/is-number-object-1.0.6.tgz?cache=0&sync_timestamp=1628221744591&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-number-object%2Fdownload%2Fis-number-object-1.0.6.tgz"
  integrity sha1-anqvg4x/BoalC0VT9+VKlklOifA=
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/is-number/download/is-number-3.0.0.tgz"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.nlark.com/is-number/download/is-number-7.0.0.tgz"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-obj@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/is-obj/download/is-obj-1.0.1.tgz"
  integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=

is-obj@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/is-obj/download/is-obj-2.0.0.tgz"
  integrity sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=

is-observable@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-observable/download/is-observable-1.1.0.tgz?cache=0&sync_timestamp=1624608087574&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-observable%2Fdownload%2Fis-observable-1.1.0.tgz"
  integrity sha1-s+mGyPRN6VCGfKtUA/WjRlAFl14=
  dependencies:
    symbol-observable "^1.1.0"

is-path-cwd@^2.0.0, is-path-cwd@^2.2.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/is-path-cwd/download/is-path-cwd-2.2.0.tgz"
  integrity sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=

is-path-in-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/is-path-in-cwd/download/is-path-in-cwd-2.1.0.tgz"
  integrity sha1-v+Lcomxp85cmWkAJljYCk1oFOss=
  dependencies:
    is-path-inside "^2.1.0"

is-path-inside@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/is-path-inside/download/is-path-inside-2.1.0.tgz"
  integrity sha1-fJgQWH1lmkDSe8201WFuqwWUlLI=
  dependencies:
    path-is-inside "^1.0.2"

is-path-inside@^3.0.1, is-path-inside@^3.0.2:
  version "3.0.3"
  resolved "https://registry.nlark.com/is-path-inside/download/is-path-inside-3.0.3.tgz"
  integrity sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=

is-plain-obj@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-obj@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/is-plain-obj/download/is-plain-obj-3.0.0.tgz"
  integrity sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://registry.nlark.com/is-plain-object/download/is-plain-object-2.0.4.tgz?cache=0&sync_timestamp=1624608043754&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-plain-object%2Fdownload%2Fis-plain-object-2.0.4.tgz"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-promise@^2.1.0:
  version "2.2.2"
  resolved "https://registry.nlark.com/is-promise/download/is-promise-2.2.2.tgz"
  integrity sha1-OauVnMv5p3TPB597QMeib3YxNfE=

is-regex@^1.0.4, is-regex@^1.1.4:
  version "1.1.4"
  resolved "https://registry.nlark.com/is-regex/download/is-regex-1.1.4.tgz"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-regexp@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/is-regexp/download/is-regexp-1.0.0.tgz"
  integrity sha1-/S2INUXEa6xaYz57mgnof6LLUGk=

is-resolvable@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-resolvable/download/is-resolvable-1.1.0.tgz"
  integrity sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg=

is-shared-array-buffer@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.1.tgz"
  integrity sha1-l7DIX72stZycRG/mU7gs8rW3z+Y=

is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-stream/download/is-stream-1.1.0.tgz?cache=0&sync_timestamp=1628592752355&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-stream%2Fdownload%2Fis-stream-1.1.0.tgz"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/is-stream/download/is-stream-2.0.1.tgz?cache=0&sync_timestamp=1628592752355&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-stream%2Fdownload%2Fis-stream-2.0.1.tgz"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "https://registry.nlark.com/is-string/download/is-string-1.0.7.tgz?cache=0&sync_timestamp=1628213433356&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-string%2Fdownload%2Fis-string-1.0.7.tgz"
  integrity sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "https://registry.nlark.com/is-symbol/download/is-symbol-1.0.4.tgz"
  integrity sha1-ptrJO2NbBjymhyI23oiRClevE5w=
  dependencies:
    has-symbols "^1.0.2"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/is-typedarray/download/is-typedarray-1.0.0.tgz"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "https://registry.nlark.com/is-utf8/download/is-utf8-0.2.1.tgz"
  integrity sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=

is-weakref@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/is-weakref/download/is-weakref-1.0.2.tgz"
  integrity sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==
  dependencies:
    call-bind "^1.0.2"

is-windows@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/is-windows/download/is-windows-1.0.2.tgz"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-wsl/download/is-wsl-1.1.0.tgz"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

is-wsl@^2.1.1:
  version "2.2.0"
  resolved "https://registry.nlark.com/is-wsl/download/is-wsl-2.2.0.tgz"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/isarray/download/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/isexe/download/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/isobject/download/isobject-2.1.0.tgz"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "https://registry.nlark.com/isobject/download/isobject-3.0.1.tgz"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://registry.nlark.com/isstream/download/isstream-0.1.2.tgz"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

javascript-stringify@^2.0.1:
  version "2.1.0"
  resolved "https://registry.nlark.com/javascript-stringify/download/javascript-stringify-2.1.0.tgz"
  integrity sha1-J8dlOb4U2L0Sghmi1zGwkzeQTnk=

js-base64@^2.1.8:
  version "2.6.4"
  resolved "https://registry.npmmirror.com/js-base64/download/js-base64-2.6.4.tgz"
  integrity sha512-pZe//GGmwJndub7ZghVHz7vjb2LgC1m8B07Au3eYqeqv9emhESByMXxaEgkUkEqJe87oBbSniGYoQNIBklc7IQ==

js-message@1.0.7:
  version "1.0.7"
  resolved "https://registry.nlark.com/js-message/download/js-message-1.0.7.tgz"
  integrity sha1-+93QU8ekcCGHG7iyyVOXzBfCDkc=

js-queue@2.0.2:
  version "2.0.2"
  resolved "https://registry.nlark.com/js-queue/download/js-queue-2.0.2.tgz"
  integrity sha1-C+WQM4+QOzbHPTPDGIOoIUEs1II=
  dependencies:
    easy-stack "^1.0.1"

js-tokens@^3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/js-tokens/download/js-tokens-3.0.2.tgz"
  integrity sha1-mGbfOVECEw449/mWvOtlRDIJwls=

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/js-tokens/download/js-tokens-4.0.0.tgz"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://registry.npmmirror.com/js-yaml/download/js-yaml-3.14.1.tgz"
  integrity sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://registry.nlark.com/jsbn/download/jsbn-0.1.1.tgz"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://registry.nlark.com/jsesc/download/jsesc-2.5.2.tgz"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://registry.nlark.com/jsesc/download/jsesc-0.5.0.tgz"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-parse-better-errors@^1.0.1, json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry.nlark.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.nlark.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema@0.4.0:
  version "0.4.0"
  resolved "https://registry.npmmirror.com/json-schema/download/json-schema-0.4.0.tgz?cache=0&sync_timestamp=1636423476647&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjson-schema%2Fdownload%2Fjson-schema-0.4.0.tgz"
  integrity sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://registry.nlark.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json3@^3.3.3:
  version "3.3.3"
  resolved "https://registry.npmmirror.com/json3/download/json3-3.3.3.tgz"
  integrity sha1-f8EON1/FrkLEcFpcwKpvYr4wW4E=

json5@^0.5.0:
  version "0.5.1"
  resolved "https://registry.nlark.com/json5/download/json5-0.5.1.tgz?cache=0&sync_timestamp=1624607963605&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjson5%2Fdownload%2Fjson5-0.5.1.tgz"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/json5/download/json5-1.0.1.tgz?cache=0&sync_timestamp=1624607963605&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjson5%2Fdownload%2Fjson5-1.0.1.tgz"
  integrity sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2:
  version "2.2.0"
  resolved "https://registry.nlark.com/json5/download/json5-2.2.0.tgz?cache=0&sync_timestamp=1624607963605&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjson5%2Fdownload%2Fjson5-2.2.0.tgz"
  integrity sha1-Lf7+cgxrpSXZ69kJlQ8FFTFsiaM=
  dependencies:
    minimist "^1.2.5"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/jsonfile/download/jsonfile-4.0.0.tgz"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.nlark.com/jsonfile/download/jsonfile-6.1.0.tgz"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsprim@^1.2.2:
  version "1.4.2"
  resolved "https://registry.npmmirror.com/jsprim/download/jsprim-1.4.2.tgz"
  integrity sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.4.0"
    verror "1.10.0"

junk@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/junk/-/junk-3.1.0.tgz"
  integrity sha512-pBxcB3LFc8QVgdggvZWyeys+hnrNWg4OcZIU/1X59k5jQdLBlCsYGRQaz234SqoRLTCgMH00fY0xRJH+F9METQ==

killable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/killable/download/killable-1.0.1.tgz"
  integrity sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-3.2.2.tgz"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-4.0.0.tgz"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-5.1.0.tgz"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-6.0.3.tgz"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

launch-editor-middleware@^2.2.1:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/launch-editor-middleware/download/launch-editor-middleware-2.3.0.tgz"
  integrity sha512-GJR64trLdFFwCoL9DMn/d1SZX0OzTDPixu4mcfWTShQ4tIqCHCGvlg9fOEYQXyBlrSMQwylsJfUWncheShfV2w==
  dependencies:
    launch-editor "^2.3.0"

launch-editor@^2.2.1, launch-editor@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/launch-editor/download/launch-editor-2.3.0.tgz"
  integrity sha512-3QrsCXejlWYHjBPFXTyGNhPj4rrQdB+5+r5r3wArpLH201aR+nWUgw/zKKkTmilCfY/sv6u8qo98pNvtg8LUTA==
  dependencies:
    picocolors "^1.0.0"
    shell-quote "^1.6.1"

lazystream@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/lazystream/-/lazystream-1.0.1.tgz"
  integrity sha512-b94GiNHQNy6JNTrt5w6zNyffMrNkXZb3KTkCZJb2V1xaEGCk093vkZ2jk3tpaeP33/OiXC+WvK9AxUebnf5nbw==
  dependencies:
    readable-stream "^2.0.5"

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/levn/download/levn-0.3.0.tgz"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.npmmirror.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

lint-staged@^9.5.0:
  version "9.5.0"
  resolved "https://registry.npmmirror.com/lint-staged/download/lint-staged-9.5.0.tgz"
  integrity sha1-KQ7GBSUq9kbZt01zoPoRg2KwWjM=
  dependencies:
    chalk "^2.4.2"
    commander "^2.20.0"
    cosmiconfig "^5.2.1"
    debug "^4.1.1"
    dedent "^0.7.0"
    del "^5.0.0"
    execa "^2.0.3"
    listr "^0.14.3"
    log-symbols "^3.0.0"
    micromatch "^4.0.2"
    normalize-path "^3.0.0"
    please-upgrade-node "^3.1.1"
    string-argv "^0.3.0"
    stringify-object "^3.3.0"

listr-silent-renderer@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/listr-silent-renderer/download/listr-silent-renderer-1.1.1.tgz"
  integrity sha1-kktaN1cVN3C/Go4/v3S4u/P5JC4=

listr-update-renderer@^0.5.0:
  version "0.5.0"
  resolved "https://registry.nlark.com/listr-update-renderer/download/listr-update-renderer-0.5.0.tgz"
  integrity sha1-Tqg2hUinuK7LfgbYyVy0WuLt5qI=
  dependencies:
    chalk "^1.1.3"
    cli-truncate "^0.2.1"
    elegant-spinner "^1.0.1"
    figures "^1.7.0"
    indent-string "^3.0.0"
    log-symbols "^1.0.2"
    log-update "^2.3.0"
    strip-ansi "^3.0.1"

listr-verbose-renderer@^0.5.0:
  version "0.5.0"
  resolved "https://registry.nlark.com/listr-verbose-renderer/download/listr-verbose-renderer-0.5.0.tgz"
  integrity sha1-8RMhZ1NepMEmEQK58o2sfLoeA9s=
  dependencies:
    chalk "^2.4.1"
    cli-cursor "^2.1.0"
    date-fns "^1.27.2"
    figures "^2.0.0"

listr@^0.14.3:
  version "0.14.3"
  resolved "https://registry.nlark.com/listr/download/listr-0.14.3.tgz"
  integrity sha1-L+qQlgTkNL5GTFC926DUlpKPpYY=
  dependencies:
    "@samverschueren/stream-to-observable" "^0.3.0"
    is-observable "^1.1.0"
    is-promise "^2.1.0"
    is-stream "^1.1.0"
    listr-silent-renderer "^1.1.1"
    listr-update-renderer "^0.5.0"
    listr-verbose-renderer "^0.5.0"
    p-map "^2.0.0"
    rxjs "^6.3.3"

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/load-json-file/download/load-json-file-1.1.0.tgz?cache=0&sync_timestamp=1631508607226&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fload-json-file%2Fdownload%2Fload-json-file-1.1.0.tgz"
  integrity sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

loader-fs-cache@^1.0.0:
  version "1.0.3"
  resolved "https://registry.nlark.com/loader-fs-cache/download/loader-fs-cache-1.0.3.tgz"
  integrity sha1-8IZXZG1gcHi+LwoDL4vWndbyd9k=
  dependencies:
    find-cache-dir "^0.1.1"
    mkdirp "^0.5.1"

loader-runner@^2.3.1, loader-runner@^2.4.0:
  version "2.4.0"
  resolved "https://registry.nlark.com/loader-runner/download/loader-runner-2.4.0.tgz"
  integrity sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=

loader-utils@^0.2.16:
  version "0.2.17"
  resolved "https://registry.npmmirror.com/loader-utils/download/loader-utils-0.2.17.tgz"
  integrity sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@^1.0.2, loader-utils@^1.1.0, loader-utils@^1.2.3, loader-utils@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/loader-utils/download/loader-utils-1.4.0.tgz"
  integrity sha1-xXm140yzSxp07cbB+za/o3HVphM=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

loader-utils@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/loader-utils/download/loader-utils-2.0.2.tgz"
  integrity sha1-1uO0+4GHByGuTghoqxHdY4NowSk=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/locate-path/download/locate-path-2.0.0.tgz"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/locate-path/download/locate-path-3.0.0.tgz"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.nlark.com/locate-path/download/locate-path-5.0.0.tgz"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://registry.nlark.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.defaults@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/lodash.defaults/-/lodash.defaults-4.2.0.tgz"
  integrity "sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw= sha512-qjxPLHd3r5DnsdGacqOMU6pb/avJzdh9tFX2ymgoZE27BmjXrNy/y4LoaiTeAb+O3gL8AfpJGtqfX/ae2leYYQ=="

lodash.defaultsdeep@^4.6.1:
  version "4.6.1"
  resolved "https://registry.nlark.com/lodash.defaultsdeep/download/lodash.defaultsdeep-4.6.1.tgz"
  integrity sha1-US6b1yHSctlOPTpjZT+hdRZ0HKY=

lodash.difference@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmmirror.com/lodash.difference/-/lodash.difference-4.5.0.tgz"
  integrity "sha1-nMtOUF1Ia5FlE0V3KIWi3yf9AXw= sha512-dS2j+W26TQ7taQBGN8Lbbq04ssV3emRw4NY58WErlTO29pIqS0HmoT5aJ9+TUQ1N3G+JOZSji4eugsWwGp9yPA=="

lodash.flatten@^4.4.0:
  version "4.4.0"
  resolved "https://registry.npmmirror.com/lodash.flatten/-/lodash.flatten-4.4.0.tgz"
  integrity "sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8= sha512-C5N2Z3DgnnKr0LOpv/hKCgKdb7ZZwafIrsesve6lmzvZIRZRGaZ/l6Q8+2W7NaT+ZwO3fFlSCzCzrDCFdJfZ4g=="

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  integrity "sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs= sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA=="

lodash.kebabcase@^4.1.1:
  version "4.1.1"
  resolved "https://registry.nlark.com/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz"
  integrity sha1-hImxyw0p/4gZXM7KRI/21swpXDY=

lodash.mapvalues@^4.6.0:
  version "4.6.0"
  resolved "https://registry.nlark.com/lodash.mapvalues/download/lodash.mapvalues-4.6.0.tgz"
  integrity sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw=

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://registry.nlark.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.transform@^4.6.0:
  version "4.6.0"
  resolved "https://registry.nlark.com/lodash.transform/download/lodash.transform-4.6.0.tgz"
  integrity sha1-EjBkIvYzJK7YSD0/ODMrX2cFR6A=

lodash.union@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmmirror.com/lodash.union/-/lodash.union-4.6.0.tgz"
  integrity "sha1-SLtQiECfFvGCFmZkHETdGqrjzYg= sha512-c4pB2CdGrGdjMKYLA+XiRDO7Y0PRQbm/Gzg8qMj+QH+pFVAoTp5sBpO0odL3FjoPCGjK96p6qsP+yQoiLoOBcw=="

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://registry.nlark.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash@^4.0.0, lodash@^4.17.11, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.20, lodash@^4.17.21, lodash@^4.17.3, lodash@~4.17.10:
  version "4.17.21"
  resolved "https://registry.npmmirror.com/lodash/download/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

log-symbols@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/log-symbols/download/log-symbols-1.0.2.tgz"
  integrity sha1-N2/3tY6jCGoPCfrMdGF+ylAeGhg=
  dependencies:
    chalk "^1.0.0"

log-symbols@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/log-symbols/download/log-symbols-2.2.0.tgz"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

log-symbols@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/log-symbols/download/log-symbols-3.0.0.tgz"
  integrity sha1-86CFFqXeqJMzan3uFNGKHP2rd8Q=
  dependencies:
    chalk "^2.4.2"

log-update@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/log-update/download/log-update-2.3.0.tgz?cache=0&sync_timestamp=1634542359448&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Flog-update%2Fdownload%2Flog-update-2.3.0.tgz"
  integrity sha1-iDKP19HOeTiykoN0bwsbwSayRwg=
  dependencies:
    ansi-escapes "^3.0.0"
    cli-cursor "^2.0.0"
    wrap-ansi "^3.0.1"

loglevel@^1.6.8:
  version "1.8.0"
  resolved "https://registry.npmmirror.com/loglevel/download/loglevel-1.8.0.tgz"
  integrity sha512-G6A/nJLRgWOuuwdNuA6koovfEV1YpqqAG4pRUlFaz3jj2QNZ8M4vBqnVA+HBTmU/AMNUtlOsMmSpF6NyOjztbA==

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "https://registry.nlark.com/loud-rejection/download/loud-rejection-1.6.0.tgz"
  integrity sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

lower-case@^1.1.1:
  version "1.1.4"
  resolved "https://registry.nlark.com/lower-case/download/lower-case-1.1.4.tgz?cache=0&sync_timestamp=1624607698082&other_urls=https%3A%2F%2Fregistry.nlark.com%2Flower-case%2Fdownload%2Flower-case-1.1.4.tgz"
  integrity sha1-miyr0bno4K6ZOkv31YdcOcQujqw=

lru-cache@^4.0.1, lru-cache@^4.1.2:
  version "4.1.5"
  resolved "https://registry.nlark.com/lru-cache/download/lru-cache-4.1.5.tgz"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry.nlark.com/lru-cache/download/lru-cache-5.1.1.tgz"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.nlark.com/lru-cache/download/lru-cache-6.0.0.tgz"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

magic-string@^0.25.7:
  version "0.25.7"
  resolved "https://registry.nlark.com/magic-string/download/magic-string-0.25.7.tgz"
  integrity sha1-P0l9b9NMZpxnmNy4IfLvMfVEUFE=
  dependencies:
    sourcemap-codec "^1.4.4"

make-dir@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/make-dir/download/make-dir-2.1.0.tgz"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^3.0.0, make-dir@^3.0.2, make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/make-dir/download/make-dir-3.1.0.tgz"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "https://registry.nlark.com/map-cache/download/map-cache-0.2.2.tgz"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-obj@^1.0.0, map-obj@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/map-obj/download/map-obj-1.0.1.tgz"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/map-visit/download/map-visit-1.0.0.tgz"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

md5.js@^1.3.4:
  version "1.3.5"
  resolved "https://registry.nlark.com/md5.js/download/md5.js-1.3.5.tgz"
  integrity sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

mdn-data@2.0.14:
  version "2.0.14"
  resolved "https://registry.npmmirror.com/mdn-data/download/mdn-data-2.0.14.tgz"
  integrity sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA=

mdn-data@2.0.4:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/mdn-data/download/mdn-data-2.0.4.tgz"
  integrity sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs=

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/media-typer/download/media-typer-0.3.0.tgz"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memfs@^3.1.2:
  version "3.4.1"
  resolved "https://registry.npmmirror.com/memfs/download/memfs-3.4.1.tgz"
  integrity sha512-1c9VPVvW5P7I85c35zAdEr1TD5+F11IToIHIlrVIcflfnzPkJa0ZoYEoEdYDP8KgPFoSZ/opDrUsAoZWym3mtw==
  dependencies:
    fs-monkey "1.0.3"

memoize-one@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/memoize-one/-/memoize-one-6.0.0.tgz"
  integrity sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==

memory-fs@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/memory-fs/download/memory-fs-0.2.0.tgz"
  integrity sha1-8rslNovBIeORwlIN6Slpyu4KApA=

memory-fs@^0.4.1:
  version "0.4.1"
  resolved "https://registry.nlark.com/memory-fs/download/memory-fs-0.4.1.tgz"
  integrity sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

memory-fs@^0.5.0:
  version "0.5.0"
  resolved "https://registry.nlark.com/memory-fs/download/memory-fs-0.5.0.tgz"
  integrity sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

meow@^3.7.0:
  version "3.7.0"
  resolved "https://registry.npmmirror.com/meow/download/meow-3.7.0.tgz?cache=0&sync_timestamp=1637477490247&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmeow%2Fdownload%2Fmeow-3.7.0.tgz"
  integrity sha1-cstmi0JSKCkKu/qFaJJYcwioAfs=
  dependencies:
    camelcase-keys "^2.0.0"
    decamelize "^1.1.2"
    loud-rejection "^1.0.0"
    map-obj "^1.0.1"
    minimist "^1.1.3"
    normalize-package-data "^2.3.4"
    object-assign "^4.0.1"
    read-pkg-up "^1.0.1"
    redent "^1.0.0"
    trim-newlines "^1.0.0"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/merge-descriptors/download/merge-descriptors-1.0.1.tgz"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-source-map@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/merge-source-map/download/merge-source-map-1.1.0.tgz"
  integrity sha1-L93n5gIJOfcJBqaPLXrmheTIxkY=
  dependencies:
    source-map "^0.6.1"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/merge-stream/download/merge-stream-2.0.0.tgz"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.2.3, merge2@^1.3.0:
  version "1.4.1"
  resolved "https://registry.nlark.com/merge2/download/merge2-1.4.1.tgz"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/methods/download/methods-1.1.2.tgz"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

microevent.ts@~0.1.1:
  version "0.1.1"
  resolved "https://registry.nlark.com/microevent.ts/download/microevent.ts-0.1.1.tgz"
  integrity sha1-cLCbg/Q99RctAgWmMCW84Pc1f6A=

micromatch@^3.1.10, micromatch@^3.1.4:
  version "3.1.10"
  resolved "https://registry.nlark.com/micromatch/download/micromatch-3.1.10.tgz"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.0, micromatch@^4.0.2, micromatch@^4.0.4:
  version "4.0.4"
  resolved "https://registry.nlark.com/micromatch/download/micromatch-4.0.4.tgz"
  integrity sha1-iW1Rnf6dsl/OlM63pQCRm/iB6/k=
  dependencies:
    braces "^3.0.1"
    picomatch "^2.2.3"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "https://registry.nlark.com/miller-rabin/download/miller-rabin-4.0.1.tgz"
  integrity sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

mime-db@1.51.0, "mime-db@>= 1.43.0 < 2":
  version "1.51.0"
  resolved "https://registry.npmmirror.com/mime-db/download/mime-db-1.51.0.tgz?cache=0&sync_timestamp=1636426033377&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-db%2Fdownload%2Fmime-db-1.51.0.tgz"
  integrity sha512-5y8A56jg7XVQx2mbv1lu49NR4dokRnhZYTtL+KGfaa27uq4pSTXkwQkFJl4pkRMyNFz/EtYDSkiiEHx3F7UN6g==

mime-types@^2.1.12, mime-types@~2.1.17, mime-types@~2.1.19, mime-types@~2.1.24:
  version "2.1.34"
  resolved "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.34.tgz?cache=0&sync_timestamp=1636432373429&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-types%2Fdownload%2Fmime-types-2.1.34.tgz"
  integrity sha512-6cP692WwGIs9XXdOO4++N+7qjqv0rqxxVvJ3VHPh/Sc9mVZcQP+ZGhkKiTvWMQRr2tbHkJP/Yn7Y0npb3ZBs4A==
  dependencies:
    mime-db "1.51.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/mime/download/mime-1.6.0.tgz"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@^2.4.4:
  version "2.6.0"
  resolved "https://registry.npmmirror.com/mime/download/mime-2.6.0.tgz"
  integrity sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-1.2.0.tgz"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-2.1.0.tgz"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mini-css-extract-plugin@^0.9.0:
  version "0.9.0"
  resolved "https://registry.npmmirror.com/mini-css-extract-plugin/download/mini-css-extract-plugin-0.9.0.tgz?cache=0&sync_timestamp=1637170529830&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmini-css-extract-plugin%2Fdownload%2Fmini-css-extract-plugin-0.9.0.tgz"
  integrity sha1-R/LPB6oWWrNXM7H8l9TEbAVkM54=
  dependencies:
    loader-utils "^1.1.0"
    normalize-url "1.9.1"
    schema-utils "^1.0.0"
    webpack-sources "^1.1.0"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz"
  integrity sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=

minimatch@^3.0.4, minimatch@~3.0.2:
  version "3.0.4"
  resolved "https://registry.nlark.com/minimatch/download/minimatch-3.0.4.tgz?cache=0&sync_timestamp=1624607996146&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fminimatch%2Fdownload%2Fminimatch-3.0.4.tgz"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.1.3, minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.5"
  resolved "https://registry.nlark.com/minimist/download/minimist-1.2.5.tgz?cache=0&sync_timestamp=1624607886507&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fminimist%2Fdownload%2Fminimist-1.2.5.tgz"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

minipass@^3.1.1:
  version "3.1.6"
  resolved "https://registry.npmmirror.com/minipass/download/minipass-3.1.6.tgz"
  integrity sha512-rty5kpw9/z8SX9dmxblFA6edItUmwJgMeYDZRrwlIVN27i8gysGbznJwUggw2V/FVqFSDdWy040ZPS811DYAqQ==
  dependencies:
    yallist "^4.0.0"

mississippi@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/mississippi/download/mississippi-3.0.0.tgz"
  integrity sha1-6goykfl+C16HdrNj1fChLZTGcCI=
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^3.0.0"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "https://registry.nlark.com/mixin-deep/download/mixin-deep-1.3.2.tgz"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

"mkdirp@>=0.5 0", mkdirp@^0.5.0, mkdirp@^0.5.1, mkdirp@^0.5.3, mkdirp@^0.5.5, mkdirp@~0.5.1:
  version "0.5.5"
  resolved "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.5.tgz"
  integrity sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=
  dependencies:
    minimist "^1.2.5"

moment@^2.29.4:
  version "2.29.4"
  resolved "https://registry.yarnpkg.com/moment/-/moment-2.29.4.tgz#3dbe052889fe7c1b2ed966fcb3a77328964ef108"
  integrity sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==

move-concurrently@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/move-concurrently/download/move-concurrently-1.0.1.tgz"
  integrity sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=
  dependencies:
    aproba "^1.1.1"
    copy-concurrently "^1.0.0"
    fs-write-stream-atomic "^1.0.8"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.3"

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2, ms@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.1.2.tgz"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@2.1.3:
  version "2.1.3"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.1.3.tgz"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz"
  integrity sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "https://registry.npmmirror.com/multicast-dns/download/multicast-dns-6.2.3.tgz?cache=0&sync_timestamp=1633354862495&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmulticast-dns%2Fdownload%2Fmulticast-dns-6.2.3.tgz"
  integrity sha1-oOx72QVcQoL3kMPIL04o2zsxsik=
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

mute-stream@0.0.8:
  version "0.0.8"
  resolved "https://registry.nlark.com/mute-stream/download/mute-stream-0.0.8.tgz"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

mz@^2.4.0:
  version "2.7.0"
  resolved "https://registry.nlark.com/mz/download/mz-2.7.0.tgz"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nan@^2.12.1:
  version "2.17.0"
  resolved "https://registry.yarnpkg.com/nan/-/nan-2.17.0.tgz#c0150a2368a182f033e9aa5195ec76ea41a199cb"
  integrity sha512-2ZTgtl0nJsO0KQCjEpxcIr5D+Yv90plTitZt9JBfQvVJDS5seMl3FOvsh3+9CoYWXf/1l5OaZzzF6nDm4cagaQ==

nan@^2.13.2:
  version "2.15.0"
  resolved "https://registry.nlark.com/nan/download/nan-2.15.0.tgz?cache=0&sync_timestamp=1628093600180&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnan%2Fdownload%2Fnan-2.15.0.tgz"
  integrity sha1-PzSkc/8Y4VwbVia2KQO1rW5mX+4=

nanoid@^3.1.30:
  version "3.1.30"
  resolved "https://registry.npmmirror.com/nanoid/download/nanoid-3.1.30.tgz"
  integrity sha512-zJpuPDwOv8D2zq2WRoMe1HsfZthVewpel9CAvTfc/2mBD1uUT/agc5f7GHGWXlYkFvi1mVxe4IjvP2HNrop7nQ==

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "https://registry.nlark.com/nanomatch/download/nanomatch-1.2.13.tgz"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/natural-compare/download/natural-compare-1.4.0.tgz?cache=0&sync_timestamp=1624608011507&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnatural-compare%2Fdownload%2Fnatural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.2:
  version "0.6.2"
  resolved "https://registry.nlark.com/negotiator/download/negotiator-0.6.2.tgz"
  integrity sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=

neo-async@^2.5.0, neo-async@^2.6.0, neo-async@^2.6.1:
  version "2.6.2"
  resolved "https://registry.nlark.com/neo-async/download/neo-async-2.6.2.tgz"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

nested-error-stacks@^2.0.0, nested-error-stacks@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/nested-error-stacks/-/nested-error-stacks-2.1.0.tgz"
  integrity sha512-AO81vsIO1k1sM4Zrd6Hu7regmJN1NSiAja10gc4bX3F0wd+9rQmcuHQaHVQCYIEC8iFXnE+mavh23GOt7wBgug==

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://registry.nlark.com/nice-try/download/nice-try-1.0.5.tgz"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

no-case@^2.2.0:
  version "2.3.2"
  resolved "https://registry.nlark.com/no-case/download/no-case-2.3.2.tgz"
  integrity sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw=
  dependencies:
    lower-case "^1.1.1"

node-forge@^0.10.0:
  version "0.10.0"
  resolved "https://registry.nlark.com/node-forge/download/node-forge-0.10.0.tgz"
  integrity sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M=

node-gyp@^3.8.0:
  version "3.8.0"
  resolved "https://registry.npmmirror.com/node-gyp/download/node-gyp-3.8.0.tgz"
  integrity sha1-VAMEJhwzDoDQ1e3OJTpoyzlkIYw=
  dependencies:
    fstream "^1.0.0"
    glob "^7.0.3"
    graceful-fs "^4.1.2"
    mkdirp "^0.5.0"
    nopt "2 || 3"
    npmlog "0 || 1 || 2 || 3 || 4"
    osenv "0"
    request "^2.87.0"
    rimraf "2"
    semver "~5.3.0"
    tar "^2.0.0"
    which "1"

node-ipc@^9.1.1:
  version "9.2.1"
  resolved "https://registry.nlark.com/node-ipc/download/node-ipc-9.2.1.tgz"
  integrity sha1-sy9mEV+dbOhB3E7CAJ1qcz+Yu2s=
  dependencies:
    event-pubsub "4.3.0"
    js-message "1.0.7"
    js-queue "2.0.2"

"node-libs-browser@^1.0.0 || ^2.0.0", node-libs-browser@^2.2.1:
  version "2.2.1"
  resolved "https://registry.nlark.com/node-libs-browser/download/node-libs-browser-2.2.1.tgz"
  integrity sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.2.0"
    buffer "^4.3.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "^3.11.0"
    domain-browser "^1.1.1"
    events "^3.0.0"
    https-browserify "^1.0.0"
    os-browserify "^0.3.0"
    path-browserify "0.0.1"
    process "^0.11.10"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.3.3"
    stream-browserify "^2.0.1"
    stream-http "^2.7.2"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.11.0"
    vm-browserify "^1.0.1"

node-releases@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/node-releases/download/node-releases-2.0.1.tgz"
  integrity sha1-PR05XyBPHy8ppUNYuftnh2WtL8U=

node-sass@^4.12.0:
  version "4.14.1"
  resolved "https://registry.npmmirror.com/node-sass/download/node-sass-4.14.1.tgz"
  integrity sha512-sjCuOlvGyCJS40R8BscF5vhVlQjNN069NtQ1gSxyK1u9iqvn6tf7O1R4GNowVZfiZUCRt5MmMs1xd+4V/7Yr0g==
  dependencies:
    async-foreach "^0.1.3"
    chalk "^1.1.1"
    cross-spawn "^3.0.0"
    gaze "^1.0.0"
    get-stdin "^4.0.1"
    glob "^7.0.3"
    in-publish "^2.0.0"
    lodash "^4.17.15"
    meow "^3.7.0"
    mkdirp "^0.5.1"
    nan "^2.13.2"
    node-gyp "^3.8.0"
    npmlog "^4.0.0"
    request "^2.88.0"
    sass-graph "2.2.5"
    stdout-stream "^1.4.0"
    "true-case-path" "^1.0.2"

"nopt@2 || 3":
  version "3.0.6"
  resolved "https://registry.nlark.com/nopt/download/nopt-3.0.6.tgz?cache=0&sync_timestamp=1624607881839&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnopt%2Fdownload%2Fnopt-3.0.6.tgz"
  integrity sha1-xkZdvwirzU2zWTF/eaxopkayj/k=
  dependencies:
    abbrev "1"

normalize-package-data@^2.3.2, normalize-package-data@^2.3.4, normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "https://registry.nlark.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz?cache=0&sync_timestamp=1629301872905&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnormalize-package-data%2Fdownload%2Fnormalize-package-data-2.5.0.tgz"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/normalize-path/download/normalize-path-1.0.0.tgz"
  integrity sha1-MtDkcvkf80VwHBWoMRAY07CpA3k=

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/normalize-path/download/normalize-path-2.1.1.tgz"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/normalize-path/download/normalize-path-3.0.0.tgz"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.nlark.com/normalize-range/download/normalize-range-0.1.2.tgz"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize-url@1.9.1:
  version "1.9.1"
  resolved "https://registry.nlark.com/normalize-url/download/normalize-url-1.9.1.tgz"
  integrity sha1-LMDWazHqIwNkWENuNiDYWVTGbDw=
  dependencies:
    object-assign "^4.0.1"
    prepend-http "^1.0.0"
    query-string "^4.1.0"
    sort-keys "^1.0.0"

normalize-url@^3.0.0:
  version "3.3.0"
  resolved "https://registry.nlark.com/normalize-url/download/normalize-url-3.3.0.tgz"
  integrity sha1-suHE3E98bVd0PfczpPWXjRhlBVk=

normalize-wheel-es@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.1.1.tgz"
  integrity sha512-157VNH4CngrcsvF8xOVOe22cwniIR3nxSltdctvQeHZj8JttEeOXffK28jucWfWBXs0QNetAumjc1GiInnwX4w==

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-2.0.2.tgz?cache=0&sync_timestamp=1633420566316&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnpm-run-path%2Fdownload%2Fnpm-run-path-2.0.2.tgz"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-run-path@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-3.1.0.tgz?cache=0&sync_timestamp=1633420566316&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnpm-run-path%2Fdownload%2Fnpm-run-path-3.1.0.tgz"
  integrity sha1-f5G+MX9qRm7+08nymArYpO6LD6U=
  dependencies:
    path-key "^3.0.0"

npm-run-path@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-4.0.1.tgz?cache=0&sync_timestamp=1633420566316&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnpm-run-path%2Fdownload%2Fnpm-run-path-4.0.1.tgz"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

"npmlog@0 || 1 || 2 || 3 || 4", npmlog@^4.0.0:
  version "4.1.2"
  resolved "https://registry.npmmirror.com/npmlog/download/npmlog-4.1.2.tgz?cache=0&sync_timestamp=1637084993012&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnpmlog%2Fdownload%2Fnpmlog-4.1.2.tgz"
  integrity sha1-CKfyqL9zRgR3mp76StXMcXq7lUs=
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.3"
    set-blocking "~2.0.0"

nth-check@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/nth-check/download/nth-check-1.0.2.tgz"
  integrity sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=
  dependencies:
    boolbase "~1.0.0"

nth-check@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/nth-check/download/nth-check-2.0.1.tgz"
  integrity sha1-Lv4WL1w9oGoolZ+9PbddvuqfD8I=
  dependencies:
    boolbase "^1.0.0"

num2fraction@^1.2.2:
  version "1.2.2"
  resolved "https://registry.nlark.com/num2fraction/download/num2fraction-1.2.2.tgz"
  integrity sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/number-is-nan/download/number-is-nan-1.0.1.tgz"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "https://registry.nlark.com/oauth-sign/download/oauth-sign-0.9.0.tgz"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.nlark.com/object-assign/download/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "https://registry.nlark.com/object-copy/download/object-copy-0.1.0.tgz"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-hash@^1.1.4:
  version "1.3.1"
  resolved "https://registry.nlark.com/object-hash/download/object-hash-1.3.1.tgz"
  integrity sha1-/eRSCYqVHLFF8Dm7fUVUSd3BJt8=

object-inspect@^1.11.0, object-inspect@^1.9.0:
  version "1.12.0"
  resolved "https://registry.npmmirror.com/object-inspect/download/object-inspect-1.12.0.tgz"
  integrity sha512-Ho2z80bVIvJloH+YzRmpZVQe87+qASmBUKZDWgx9cu+KDrX2ZDH/3tMy+gXbZETVGs2M8YdxObOh7XAtim9Y0g==

object-is@^1.0.1:
  version "1.1.5"
  resolved "https://registry.nlark.com/object-is/download/object-is-1.1.5.tgz"
  integrity sha1-ud7qpfx/GEag+uzc7sE45XePU6w=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/object-keys/download/object-keys-1.1.1.tgz"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/object-visit/download/object-visit-1.0.1.tgz"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.0, object.assign@^4.1.2:
  version "4.1.2"
  resolved "https://registry.nlark.com/object.assign/download/object.assign-4.1.2.tgz"
  integrity sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    has-symbols "^1.0.1"
    object-keys "^1.1.1"

object.getownpropertydescriptors@^2.0.3:
  version "2.1.3"
  resolved "https://registry.npmmirror.com/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.3.tgz?cache=0&sync_timestamp=1633321702182&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fobject.getownpropertydescriptors%2Fdownload%2Fobject.getownpropertydescriptors-2.1.3.tgz"
  integrity sha1-siPPOOF/77l6Y8EMkd9yzLOG354=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/object.pick/download/object.pick-1.3.0.tgz"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.0, object.values@^1.1.5:
  version "1.1.5"
  resolved "https://registry.npmmirror.com/object.values/download/object.values-1.1.5.tgz?cache=0&sync_timestamp=1633327129170&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fobject.values%2Fdownload%2Fobject.values-1.1.5.tgz"
  integrity sha1-lZ9j486e8QhyAzMIITHkpFm3Fqw=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/obuf/download/obuf-1.1.2.tgz"
  integrity sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://registry.nlark.com/on-finished/download/on-finished-2.3.0.tgz"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/on-headers/download/on-headers-1.0.2.tgz"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/once/download/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/onetime/download/onetime-2.0.1.tgz"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0:
  version "5.1.2"
  resolved "https://registry.nlark.com/onetime/download/onetime-5.1.2.tgz"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^6.3.0:
  version "6.4.0"
  resolved "https://registry.npmmirror.com/open/download/open-6.4.0.tgz"
  integrity sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk=
  dependencies:
    is-wsl "^1.1.0"

opener@^1.5.1:
  version "1.5.2"
  resolved "https://registry.nlark.com/opener/download/opener-1.5.2.tgz"
  integrity sha1-XTfh81B3udysQwE3InGv3rKhNZg=

opn@^5.5.0:
  version "5.5.0"
  resolved "https://registry.npmmirror.com/opn/download/opn-5.5.0.tgz"
  integrity sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=
  dependencies:
    is-wsl "^1.1.0"

optionator@^0.8.3:
  version "0.8.3"
  resolved "https://registry.nlark.com/optionator/download/optionator-0.8.3.tgz"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

ora@^3.4.0:
  version "3.4.0"
  resolved "https://registry.npmmirror.com/ora/download/ora-3.4.0.tgz"
  integrity sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=
  dependencies:
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-spinners "^2.0.0"
    log-symbols "^2.2.0"
    strip-ansi "^5.2.0"
    wcwidth "^1.0.1"

original@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/original/download/original-1.0.2.tgz"
  integrity sha1-5EKmHP/hxf0gpl8yYcJmY7MD8l8=
  dependencies:
    url-parse "^1.4.3"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/os-browserify/download/os-browserify-0.3.0.tgz"
  integrity sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=

os-homedir@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/os-homedir/download/os-homedir-1.0.2.tgz"
  integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=

os-tmpdir@^1.0.0, os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

osenv@0:
  version "0.1.5"
  resolved "https://registry.nlark.com/osenv/download/osenv-0.1.5.tgz"
  integrity sha1-hc36+uso6Gd/QW4odZK18/SepBA=
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.0"

p-all@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/p-all/-/p-all-2.1.0.tgz"
  integrity sha512-HbZxz5FONzz/z2gJfk6bFca0BCiSRF8jU3yCsWOen/vR6lZjfPOu/e7L3uFzTW1i0H8TlC3vqQstEJPQL4/uLA==
  dependencies:
    p-map "^2.0.0"

p-event@^4.1.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/p-event/-/p-event-4.2.0.tgz"
  integrity sha512-KXatOjCRXXkSePPb1Nbi0p0m+gQAwdlbhi4wQKJPI1HsMQS9g+Sqp2o+QHziPr7eYJyOZet836KoHEVM1mwOrQ==
  dependencies:
    p-timeout "^3.1.0"

p-filter@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/p-filter/-/p-filter-2.1.0.tgz"
  integrity sha512-ZBxxZ5sL2HghephhpGAQdoskxplTwr7ICaehZwLIlfL6acuVgZPm8yBNuRAFBGEqtD/hmUeq9eqLg2ys9Xr/yw==
  dependencies:
    p-map "^2.0.0"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/p-finally/download/p-finally-1.0.0.tgz"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-finally@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/p-finally/download/p-finally-2.0.1.tgz"
  integrity sha1-vW/KqcVZoJa2gIBvTWV7Pw8kBWE=

p-limit@^1.1.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/p-limit/download/p-limit-1.3.0.tgz?cache=0&sync_timestamp=1628813055527&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fp-limit%2Fdownload%2Fp-limit-1.3.0.tgz"
  integrity sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=
  dependencies:
    p-try "^1.0.0"

p-limit@^2.0.0, p-limit@^2.2.0, p-limit@^2.2.1:
  version "2.3.0"
  resolved "https://registry.nlark.com/p-limit/download/p-limit-2.3.0.tgz?cache=0&sync_timestamp=1628813055527&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fp-limit%2Fdownload%2Fp-limit-2.3.0.tgz"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/p-locate/download/p-locate-2.0.0.tgz?cache=0&sync_timestamp=1629892708584&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fp-locate%2Fdownload%2Fp-locate-2.0.0.tgz"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/p-locate/download/p-locate-3.0.0.tgz?cache=0&sync_timestamp=1629892708584&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fp-locate%2Fdownload%2Fp-locate-3.0.0.tgz"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/p-locate/download/p-locate-4.1.0.tgz?cache=0&sync_timestamp=1629892708584&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fp-locate%2Fdownload%2Fp-locate-4.1.0.tgz"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-map@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/p-map/download/p-map-2.1.0.tgz?cache=0&sync_timestamp=1635931916150&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-map%2Fdownload%2Fp-map-2.1.0.tgz"
  integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=

p-map@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/p-map/download/p-map-3.0.0.tgz?cache=0&sync_timestamp=1635931916150&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-map%2Fdownload%2Fp-map-3.0.0.tgz"
  integrity sha1-1wTZr4orpoTiYA2aIVmD1BQal50=
  dependencies:
    aggregate-error "^3.0.0"

p-map@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/p-map/-/p-map-4.0.0.tgz"
  integrity sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==
  dependencies:
    aggregate-error "^3.0.0"

p-retry@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/p-retry/download/p-retry-3.0.1.tgz"
  integrity sha1-MWtMiJPiyNwc+okfQGxLQivr8yg=
  dependencies:
    retry "^0.12.0"

p-timeout@^3.1.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/p-timeout/-/p-timeout-3.2.0.tgz"
  integrity sha512-rhIwUycgwwKcP9yTOOFK/AKsAopjjCakVqLHePO3CC6Mir1Z99xT+R63jZxAT5lFZLa2inS5h+ZS2GvR99/FBg==
  dependencies:
    p-finally "^1.0.0"

p-try@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/p-try/download/p-try-1.0.0.tgz?cache=0&sync_timestamp=1633364462890&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-try%2Fdownload%2Fp-try-1.0.0.tgz"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/p-try/download/p-try-2.2.0.tgz?cache=0&sync_timestamp=1633364462890&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-try%2Fdownload%2Fp-try-2.2.0.tgz"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

pako@~1.0.5:
  version "1.0.11"
  resolved "https://registry.nlark.com/pako/download/pako-1.0.11.tgz?cache=0&sync_timestamp=1627560187062&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpako%2Fdownload%2Fpako-1.0.11.tgz"
  integrity sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=

parallel-transform@^1.1.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/parallel-transform/download/parallel-transform-1.2.0.tgz"
  integrity sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=
  dependencies:
    cyclist "^1.0.1"
    inherits "^2.0.3"
    readable-stream "^2.1.5"

param-case@2.1.x:
  version "2.1.1"
  resolved "https://registry.nlark.com/param-case/download/param-case-2.1.1.tgz?cache=0&sync_timestamp=1624607700998&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fparam-case%2Fdownload%2Fparam-case-2.1.1.tgz"
  integrity sha1-35T9jPZTHs915r75oIWPvHK+Ikc=
  dependencies:
    no-case "^2.2.0"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/parent-module/download/parent-module-1.0.1.tgz?cache=0&sync_timestamp=1633337513286&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fparent-module%2Fdownload%2Fparent-module-1.0.1.tgz"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-asn1@^5.0.0, parse-asn1@^5.1.5:
  version "5.1.6"
  resolved "https://registry.nlark.com/parse-asn1/download/parse-asn1-5.1.6.tgz"
  integrity sha1-OFCAo+wTy2KmLTlAnLPoiETNrtQ=
  dependencies:
    asn1.js "^5.2.0"
    browserify-aes "^1.0.0"
    evp_bytestokey "^1.0.0"
    pbkdf2 "^3.0.3"
    safe-buffer "^5.1.1"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/parse-json/download/parse-json-2.2.0.tgz"
  integrity sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=
  dependencies:
    error-ex "^1.2.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/parse-json/download/parse-json-4.0.0.tgz"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/parse-json/download/parse-json-5.2.0.tgz"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5-htmlparser2-tree-adapter@^6.0.0:
  version "6.0.1"
  resolved "https://registry.nlark.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-6.0.1.tgz"
  integrity sha1-LN+a2CMyEUA3DU2/XT6Sx8jdxuY=
  dependencies:
    parse5 "^6.0.1"

parse5@^5.1.1:
  version "5.1.1"
  resolved "https://registry.nlark.com/parse5/download/parse5-5.1.1.tgz"
  integrity sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=

parse5@^6.0.1:
  version "6.0.1"
  resolved "https://registry.nlark.com/parse5/download/parse5-6.0.1.tgz"
  integrity sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.nlark.com/parseurl/download/parseurl-1.3.3.tgz"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmmirror.com/pascalcase/download/pascalcase-0.1.1.tgz"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-browserify@0.0.1:
  version "0.0.1"
  resolved "https://registry.nlark.com/path-browserify/download/path-browserify-0.0.1.tgz"
  integrity sha1-5sTd1+06onxoogzE5Q4aTug7vEo=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/path-dirname/download/path-dirname-1.0.2.tgz"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/path-exists/download/path-exists-2.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpath-exists%2Fdownload%2Fpath-exists-2.1.0.tgz"
  integrity sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/path-exists/download/path-exists-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpath-exists%2Fdownload%2Fpath-exists-3.0.0.tgz"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/path-exists/download/path-exists-4.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpath-exists%2Fdownload%2Fpath-exists-4.0.0.tgz"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/path-is-inside/download/path-is-inside-1.0.2.tgz"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/path-key/download/path-key-2.0.1.tgz"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.nlark.com/path-key/download/path-key-3.1.1.tgz"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.6:
  version "1.0.7"
  resolved "https://registry.nlark.com/path-parse/download/path-parse-1.0.7.tgz"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.nlark.com/path-to-regexp/download/path-to-regexp-0.1.7.tgz?cache=0&sync_timestamp=1624608046390&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpath-to-regexp%2Fdownload%2Fpath-to-regexp-0.1.7.tgz"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-type@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/path-type/download/path-type-1.1.0.tgz"
  integrity sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

path-type@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/path-type/download/path-type-3.0.0.tgz"
  integrity sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=
  dependencies:
    pify "^3.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/path-type/download/path-type-4.0.0.tgz"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pbkdf2@^3.0.3:
  version "3.1.2"
  resolved "https://registry.nlark.com/pbkdf2/download/pbkdf2-3.1.2.tgz"
  integrity sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU=
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/performance-now/download/performance-now-2.1.0.tgz"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picocolors@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/picocolors/download/picocolors-0.2.1.tgz?cache=0&sync_timestamp=1634093378416&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpicocolors%2Fdownload%2Fpicocolors-0.2.1.tgz"
  integrity sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8=

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/picocolors/download/picocolors-1.0.0.tgz?cache=0&sync_timestamp=1634093378416&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpicocolors%2Fdownload%2Fpicocolors-1.0.0.tgz"
  integrity sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.3:
  version "2.3.0"
  resolved "https://registry.nlark.com/picomatch/download/picomatch-2.3.0.tgz"
  integrity sha1-8fBh3o9qS/AiiS4tEoI0+5gwKXI=

pify@^2.0.0:
  version "2.3.0"
  resolved "https://registry.nlark.com/pify/download/pify-2.3.0.tgz"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/pify/download/pify-3.0.0.tgz"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/pify/download/pify-4.0.1.tgz"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.nlark.com/pinkie/download/pinkie-2.0.4.tgz"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pkg-dir@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-1.0.0.tgz"
  integrity sha1-ektQio1bstYp1EcFb/TpyTFM89Q=
  dependencies:
    find-up "^1.0.0"

pkg-dir@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-2.0.0.tgz"
  integrity sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=
  dependencies:
    find-up "^2.1.0"

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-3.0.0.tgz"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

pkg-dir@^4.1.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-4.2.0.tgz"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

please-upgrade-node@^3.1.1:
  version "3.2.0"
  resolved "https://registry.nlark.com/please-upgrade-node/download/please-upgrade-node-3.2.0.tgz"
  integrity sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=
  dependencies:
    semver-compare "^1.0.0"

pngjs@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/pngjs/-/pngjs-5.0.0.tgz"
  integrity sha512-40QW5YalBNfQo5yRYmiw7Yz6TKKVr3h6970B2YE+3fQpsWcrbj1PzJgxeJ19DRQjhMbKPIuMY8rFaXc8moolVw==

pnp-webpack-plugin@^1.6.4:
  version "1.7.0"
  resolved "https://registry.nlark.com/pnp-webpack-plugin/download/pnp-webpack-plugin-1.7.0.tgz"
  integrity sha1-ZXQThPbYBW824iVajWf/wghm9ck=
  dependencies:
    ts-pnp "^1.1.6"

portfinder@^1.0.26:
  version "1.0.28"
  resolved "https://registry.nlark.com/portfinder/download/portfinder-1.0.28.tgz"
  integrity sha1-Z8RiKFK9U3TdHdkA93n1NGL6x3g=
  dependencies:
    async "^2.6.2"
    debug "^3.1.1"
    mkdirp "^0.5.5"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "https://registry.nlark.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

postcss-calc@^7.0.1:
  version "7.0.5"
  resolved "https://registry.nlark.com/postcss-calc/download/postcss-calc-7.0.5.tgz"
  integrity sha1-+KbpnxLmGcLrwjz2xIb9wVhgkz4=
  dependencies:
    postcss "^7.0.27"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.0.2"

postcss-colormin@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/postcss-colormin/download/postcss-colormin-4.0.3.tgz"
  integrity sha1-rgYLzpPteUrHEmTwgTLVUJVr04E=
  dependencies:
    browserslist "^4.0.0"
    color "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-convert-values@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/postcss-convert-values/download/postcss-convert-values-4.0.1.tgz?cache=0&sync_timestamp=1635857664165&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-convert-values%2Fdownload%2Fpostcss-convert-values-4.0.1.tgz"
  integrity sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-discard-comments@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-discard-comments/download/postcss-discard-comments-4.0.2.tgz"
  integrity sha1-H7q9LCRr/2qq15l7KwkY9NevQDM=
  dependencies:
    postcss "^7.0.0"

postcss-discard-duplicates@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-discard-duplicates/download/postcss-discard-duplicates-4.0.2.tgz"
  integrity sha1-P+EzzTyCKC5VD8myORdqkge3hOs=
  dependencies:
    postcss "^7.0.0"

postcss-discard-empty@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/postcss-discard-empty/download/postcss-discard-empty-4.0.1.tgz"
  integrity sha1-yMlR6fc+2UKAGUWERKAq2Qu592U=
  dependencies:
    postcss "^7.0.0"

postcss-discard-overridden@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/postcss-discard-overridden/download/postcss-discard-overridden-4.0.1.tgz"
  integrity sha1-ZSrvipZybwKfXj4AFG7npOdV/1c=
  dependencies:
    postcss "^7.0.0"

postcss-load-config@^2.0.0:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/postcss-load-config/download/postcss-load-config-2.1.2.tgz"
  integrity sha1-xepQTyxK7zPHNZo03jVzdyrXUCo=
  dependencies:
    cosmiconfig "^5.0.0"
    import-cwd "^2.0.0"

postcss-loader@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/postcss-loader/download/postcss-loader-3.0.0.tgz"
  integrity sha1-a5eUPkfHLYRfqeA/Jzdz1OjdbC0=
  dependencies:
    loader-utils "^1.1.0"
    postcss "^7.0.0"
    postcss-load-config "^2.0.0"
    schema-utils "^1.0.0"

postcss-merge-longhand@^4.0.11:
  version "4.0.11"
  resolved "https://registry.npmmirror.com/postcss-merge-longhand/download/postcss-merge-longhand-4.0.11.tgz?cache=0&sync_timestamp=1637084902220&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-merge-longhand%2Fdownload%2Fpostcss-merge-longhand-4.0.11.tgz"
  integrity sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ=
  dependencies:
    css-color-names "0.0.4"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    stylehacks "^4.0.0"

postcss-merge-rules@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/postcss-merge-rules/download/postcss-merge-rules-4.0.3.tgz?cache=0&sync_timestamp=1637085499834&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-merge-rules%2Fdownload%2Fpostcss-merge-rules-4.0.3.tgz"
  integrity sha1-NivqT/Wh+Y5AdacTxsslrv75plA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    cssnano-util-same-parent "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"
    vendors "^1.0.0"

postcss-minify-font-values@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-minify-font-values/download/postcss-minify-font-values-4.0.2.tgz"
  integrity sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-gradients@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/postcss-minify-gradients/download/postcss-minify-gradients-4.0.2.tgz?cache=0&sync_timestamp=1635856917388&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-minify-gradients%2Fdownload%2Fpostcss-minify-gradients-4.0.2.tgz"
  integrity sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    is-color-stop "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-params@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/postcss-minify-params/download/postcss-minify-params-4.0.2.tgz?cache=0&sync_timestamp=1637084901598&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-minify-params%2Fdownload%2Fpostcss-minify-params-4.0.2.tgz"
  integrity sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ=
  dependencies:
    alphanum-sort "^1.0.0"
    browserslist "^4.0.0"
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    uniqs "^2.0.0"

postcss-minify-selectors@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-minify-selectors/download/postcss-minify-selectors-4.0.2.tgz"
  integrity sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g=
  dependencies:
    alphanum-sort "^1.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

postcss-modules-extract-imports@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/postcss-modules-extract-imports/download/postcss-modules-extract-imports-2.0.0.tgz"
  integrity sha1-gYcZoa4doyX5gyRGsBE27rSTzX4=
  dependencies:
    postcss "^7.0.5"

postcss-modules-local-by-default@^3.0.2:
  version "3.0.3"
  resolved "https://registry.nlark.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-3.0.3.tgz"
  integrity sha1-uxTgzHgnnVBNvcv9fgyiiZP/u7A=
  dependencies:
    icss-utils "^4.1.1"
    postcss "^7.0.32"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^2.2.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/postcss-modules-scope/download/postcss-modules-scope-2.2.0.tgz"
  integrity sha1-OFyuATzHdD9afXYC0Qc6iequYu4=
  dependencies:
    postcss "^7.0.6"
    postcss-selector-parser "^6.0.0"

postcss-modules-values@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/postcss-modules-values/download/postcss-modules-values-3.0.0.tgz"
  integrity sha1-W1AA1uuuKbQlUwG0o6VFdEI+fxA=
  dependencies:
    icss-utils "^4.0.0"
    postcss "^7.0.6"

postcss-normalize-charset@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/postcss-normalize-charset/download/postcss-normalize-charset-4.0.1.tgz"
  integrity sha1-izWt067oOhNrBHHg1ZvlilAoXdQ=
  dependencies:
    postcss "^7.0.0"

postcss-normalize-display-values@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-display-values/download/postcss-normalize-display-values-4.0.2.tgz"
  integrity sha1-Db4EpM6QY9RmftK+R2u4MMglk1o=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-positions@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-positions/download/postcss-normalize-positions-4.0.2.tgz"
  integrity sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-repeat-style@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-4.0.2.tgz"
  integrity sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-string@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-string/download/postcss-normalize-string-4.0.2.tgz"
  integrity sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw=
  dependencies:
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-timing-functions@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-4.0.2.tgz"
  integrity sha1-jgCcoqOUnNr4rSPmtquZy159KNk=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-unicode@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/postcss-normalize-unicode/download/postcss-normalize-unicode-4.0.1.tgz"
  integrity sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs=
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-url@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/postcss-normalize-url/download/postcss-normalize-url-4.0.1.tgz"
  integrity sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE=
  dependencies:
    is-absolute-url "^2.0.0"
    normalize-url "^3.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-whitespace@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-normalize-whitespace/download/postcss-normalize-whitespace-4.0.2.tgz"
  integrity sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-ordered-values@^4.1.2:
  version "4.1.2"
  resolved "https://registry.nlark.com/postcss-ordered-values/download/postcss-ordered-values-4.1.2.tgz"
  integrity sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-reduce-initial@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/postcss-reduce-initial/download/postcss-reduce-initial-4.0.3.tgz"
  integrity sha1-f9QuvqXpyBRgljniwuhK4nC6SN8=
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"

postcss-reduce-transforms@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/postcss-reduce-transforms/download/postcss-reduce-transforms-4.0.2.tgz"
  integrity sha1-F++kBerMbge+NBSlyi0QdGgdTik=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-selector-parser@^3.0.0:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/postcss-selector-parser/download/postcss-selector-parser-3.1.2.tgz"
  integrity sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA=
  dependencies:
    dot-prop "^5.2.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^6.0.0, postcss-selector-parser@^6.0.2:
  version "6.0.8"
  resolved "https://registry.npmmirror.com/postcss-selector-parser/download/postcss-selector-parser-6.0.8.tgz"
  integrity sha512-D5PG53d209Z1Uhcc0qAZ5U3t5HagH3cxu+WLZ22jt3gLUpXM4eXXfiO14jiDWST3NNooX/E8wISfOhZ9eIjGTQ==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-svgo@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/postcss-svgo/download/postcss-svgo-4.0.3.tgz"
  integrity sha1-NDos26yVBdQWJD1Jb3JPOIlMlB4=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    svgo "^1.0.0"

postcss-unique-selectors@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/postcss-unique-selectors/download/postcss-unique-selectors-4.0.1.tgz?cache=0&sync_timestamp=1637084898160&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-unique-selectors%2Fdownload%2Fpostcss-unique-selectors-4.0.1.tgz"
  integrity sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w=
  dependencies:
    alphanum-sort "^1.0.0"
    postcss "^7.0.0"
    uniqs "^2.0.0"

postcss-value-parser@^3.0.0:
  version "3.3.1"
  resolved "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz"
  integrity sha1-n/giVH4okyE88cMO+lGsX9G6goE=

postcss-value-parser@^4.0.2, postcss-value-parser@^4.1.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@^7.0.0, postcss@^7.0.1, postcss@^7.0.14, postcss@^7.0.27, postcss@^7.0.32, postcss@^7.0.36, postcss@^7.0.5, postcss@^7.0.6:
  version "7.0.39"
  resolved "https://registry.npmmirror.com/postcss/download/postcss-7.0.39.tgz"
  integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

postcss@^8.1.10:
  version "8.4.5"
  resolved "https://registry.npmmirror.com/postcss/download/postcss-8.4.5.tgz"
  integrity sha512-jBDboWM8qpaqwkMwItqTQTiFikhs/67OYVvblFFTM7MrZjt6yMKd6r2kgXizEbTTljacm4NldIlZnhbjr84QYg==
  dependencies:
    nanoid "^3.1.30"
    picocolors "^1.0.0"
    source-map-js "^1.0.1"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/prelude-ls/download/prelude-ls-1.1.2.tgz"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prepend-http@^1.0.0:
  version "1.0.4"
  resolved "https://registry.nlark.com/prepend-http/download/prepend-http-1.0.4.tgz?cache=0&sync_timestamp=1628547378604&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fprepend-http%2Fdownload%2Fprepend-http-1.0.4.tgz"
  integrity sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=

"prettier@^1.18.2 || ^2.0.0":
  version "2.5.1"
  resolved "https://registry.npmmirror.com/prettier/download/prettier-2.5.1.tgz"
  integrity sha512-vBZcPRUR5MZJwoyi3ZoyQlc1rXeEck8KgeC9AwwOn+exuxLxq5toTRDTSaVrXHxelDMHy9zlicw8u66yxoSUFg==

pretty-error@^2.0.2:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/pretty-error/download/pretty-error-2.1.2.tgz?cache=0&sync_timestamp=1635212589197&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpretty-error%2Fdownload%2Fpretty-error-2.1.2.tgz"
  integrity sha1-von4LYGxyG7I/fvDhQRYgnJ/k7Y=
  dependencies:
    lodash "^4.17.20"
    renderkid "^2.0.4"

printj@~1.1.0:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/printj/-/printj-1.1.2.tgz"
  integrity sha512-zA2SmoLaxZyArQTOPj5LXecR+RagfPSU5Kw1qP+jkWeNlrq+eJZyY2oS68SU1Z/7/myXM4lo9716laOFAVStCQ==

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.10:
  version "0.11.10"
  resolved "https://registry.nlark.com/process/download/process-0.11.10.tgz"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

progress@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/progress/download/progress-2.0.3.tgz"
  integrity sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/promise-inflight/download/promise-inflight-1.0.1.tgz"
  integrity sha1-mEcocL8igTL8vdhoEputEsPAKeM=

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "https://registry.nlark.com/proxy-addr/download/proxy-addr-2.0.7.tgz"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

prr@~1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/prr/download/prr-1.0.1.tgz"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/pseudomap/download/pseudomap-1.0.2.tgz"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.28:
  version "1.8.0"
  resolved "https://registry.nlark.com/psl/download/psl-1.8.0.tgz"
  integrity sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ=

public-encrypt@^4.0.0:
  version "4.0.3"
  resolved "https://registry.nlark.com/public-encrypt/download/public-encrypt-4.0.3.tgz"
  integrity sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

pump@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/pump/download/pump-2.0.1.tgz?cache=0&sync_timestamp=1624607960506&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpump%2Fdownload%2Fpump-2.0.1.tgz"
  integrity sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/pump/download/pump-3.0.0.tgz?cache=0&sync_timestamp=1624607960506&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpump%2Fdownload%2Fpump-3.0.0.tgz"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.3:
  version "1.5.1"
  resolved "https://registry.nlark.com/pumpify/download/pumpify-1.5.1.tgz"
  integrity sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.nlark.com/punycode/download/punycode-1.3.2.tgz"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

punycode@^1.2.4:
  version "1.4.1"
  resolved "https://registry.nlark.com/punycode/download/punycode-1.4.1.tgz"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/punycode/download/punycode-2.1.1.tgz"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

q@^1.1.2:
  version "1.5.1"
  resolved "https://registry.nlark.com/q/download/q-1.5.1.tgz"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qrcode.vue@^3.3.3:
  version "3.3.3"
  resolved "https://registry.npmmirror.com/qrcode.vue/-/qrcode.vue-3.3.3.tgz"
  integrity sha512-OsD4tQjIbxg/K6D5ZkWjBdYI9eg9K2i8qeYILdEAX5mdAydSAxV7xKmmZSP/hA12olLqEMZ9ryqDQrwa9jEMgw==

qrcode@^1.5.0:
  version "1.5.0"
  resolved "https://registry.npmmirror.com/qrcode/-/qrcode-1.5.0.tgz"
  integrity sha512-9MgRpgVc+/+47dFvQeD6U2s0Z92EsKzcHogtum4QB+UNd025WOJSHvn/hjk9xmzj7Stj95CyUAs31mrjxliEsQ==
  dependencies:
    dijkstrajs "^1.0.1"
    encode-utf8 "^1.0.3"
    pngjs "^5.0.0"
    yargs "^15.3.1"

qs@6.9.6:
  version "6.9.6"
  resolved "https://registry.npmmirror.com/qs/download/qs-6.9.6.tgz"
  integrity sha512-TIRk4aqYLNoJUbd+g2lEdz5kLWIuTMRagAXxl78Q0RiVjAOugHmeKNGdd3cwo/ktpf9aL9epCfFqWDEKysUlLQ==

qs@~6.5.2:
  version "6.5.2"
  resolved "https://registry.npmmirror.com/qs/download/qs-6.5.2.tgz"
  integrity sha512-N5ZAX4/LxJmF+7wN74pUD6qAh9/wnvdQcjq9TZjevvXzSUo7bfmw91saqMjzGS2xq91/odN2dW/WOl7qQHNDGA==

query-string@^4.1.0:
  version "4.3.4"
  resolved "https://registry.nlark.com/query-string/download/query-string-4.3.4.tgz"
  integrity sha1-u7aTucqRXCMlFbIosaArYJBD2+s=
  dependencies:
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "https://registry.nlark.com/querystring-es3/download/querystring-es3-0.2.1.tgz"
  integrity sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/querystring/download/querystring-0.2.0.tgz"
  integrity sha512-X/xY82scca2tau62i9mDyU9K+I+djTMUsvwf7xnUX5GLvVzgJybOJf4Y6o9Zx3oJK/LSXg5tTZBjwzqVPaPO2g==

querystringify@^2.1.1:
  version "2.2.0"
  resolved "https://registry.nlark.com/querystringify/download/querystringify-2.2.0.tgz"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.nlark.com/queue-microtask/download/queue-microtask-1.2.3.tgz"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5, randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/randombytes/download/randombytes-2.1.0.tgz"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.3:
  version "1.0.4"
  resolved "https://registry.nlark.com/randomfill/download/randomfill-1.0.4.tgz"
  integrity sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.nlark.com/range-parser/download/range-parser-1.2.1.tgz"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.4.2:
  version "2.4.2"
  resolved "https://registry.npmmirror.com/raw-body/download/raw-body-2.4.2.tgz?cache=0&sync_timestamp=1637116782389&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fraw-body%2Fdownload%2Fraw-body-2.4.2.tgz"
  integrity sha512-RPMAFUJP19WIet/99ngh6Iv8fzAbqum4Li7AD6DtGaW2RpMB/11xDoalPiJMTbu6I3hkbMVkATvZrqb9EEqeeQ==
  dependencies:
    bytes "3.1.1"
    http-errors "1.8.1"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/read-pkg-up/download/read-pkg-up-1.0.1.tgz"
  integrity sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/read-pkg/download/read-pkg-1.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fread-pkg%2Fdownload%2Fread-pkg-1.1.0.tgz"
  integrity sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

read-pkg@^5.1.1:
  version "5.2.0"
  resolved "https://registry.nlark.com/read-pkg/download/read-pkg-5.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fread-pkg%2Fdownload%2Fread-pkg-5.2.0.tgz"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

"readable-stream@1 || 2", readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.0.5, readable-stream@^2.0.6, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.3.3, readable-stream@^2.3.6, readable-stream@~2.3.6:
  version "2.3.7"
  resolved "https://registry.nlark.com/readable-stream/download/readable-stream-2.3.7.tgz"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6, readable-stream@^3.1.1, readable-stream@^3.4.0, readable-stream@^3.6.0:
  version "3.6.0"
  resolved "https://registry.nlark.com/readable-stream/download/readable-stream-3.6.0.tgz"
  integrity sha1-M3u9o63AcGvT4CRCaihtS0sskZg=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdir-glob@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/readdir-glob/-/readdir-glob-1.1.1.tgz"
  integrity sha512-91/k1EzZwDx6HbERR+zucygRFfiPl2zkIYZtv3Jjr6Mn7SkKcVct8aVO+sSRiGMc6fLf72du3d92/uY63YPdEA==
  dependencies:
    minimatch "^3.0.4"

readdirp@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmmirror.com/readdirp/download/readdirp-2.2.1.tgz"
  integrity sha1-DodiKjMlqjPokihcr4tOhGUppSU=
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/readdirp/download/readdirp-3.6.0.tgz"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

redent@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/redent/download/redent-1.0.0.tgz"
  integrity sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94=
  dependencies:
    indent-string "^2.1.0"
    strip-indent "^1.0.1"

regenerate-unicode-properties@^9.0.0:
  version "9.0.0"
  resolved "https://registry.npmmirror.com/regenerate-unicode-properties/download/regenerate-unicode-properties-9.0.0.tgz"
  integrity sha1-VNCccRXh9T3CMUqXSzLBw0Tv4yY=
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "https://registry.nlark.com/regenerate/download/regenerate-1.4.2.tgz"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.13.4:
  version "0.13.9"
  resolved "https://registry.nlark.com/regenerator-runtime/download/regenerator-runtime-0.13.9.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fregenerator-runtime%2Fdownload%2Fregenerator-runtime-0.13.9.tgz"
  integrity sha1-iSV0Kpj/2QgUmI11Zq0wyjsmO1I=

regenerator-transform@^0.14.2:
  version "0.14.5"
  resolved "https://registry.nlark.com/regenerator-transform/download/regenerator-transform-0.14.5.tgz?cache=0&sync_timestamp=1627057533376&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fregenerator-transform%2Fdownload%2Fregenerator-transform-0.14.5.tgz"
  integrity sha1-yY2hVGg2ccnE3LFuznNlF+G3/rQ=
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/regex-not/download/regex-not-1.0.2.tgz"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.2.0:
  version "1.3.1"
  resolved "https://registry.nlark.com/regexp.prototype.flags/download/regexp.prototype.flags-1.3.1.tgz"
  integrity sha1-fvNSro0VnnWMDq3Kb4/LTu8HviY=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

regexpp@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/regexpp/download/regexpp-2.0.1.tgz"
  integrity sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=

regexpp@^3.0.0, regexpp@^3.1.0:
  version "3.2.0"
  resolved "https://registry.nlark.com/regexpp/download/regexpp-3.2.0.tgz"
  integrity sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=

regexpu-core@^4.7.1:
  version "4.8.0"
  resolved "https://registry.nlark.com/regexpu-core/download/regexpu-core-4.8.0.tgz?cache=0&sync_timestamp=1631619113277&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fregexpu-core%2Fdownload%2Fregexpu-core-4.8.0.tgz"
  integrity sha1-5WBbo2G2excYR4UBMnUC9EeamPA=
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^9.0.0"
    regjsgen "^0.5.2"
    regjsparser "^0.7.0"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.0.0"

regjsgen@^0.5.2:
  version "0.5.2"
  resolved "https://registry.npmmirror.com/regjsgen/download/regjsgen-0.5.2.tgz"
  integrity sha1-kv8pX7He7L9uzaslQ9IH6RqjNzM=

regjsparser@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmmirror.com/regjsparser/download/regjsparser-0.7.0.tgz"
  integrity sha1-prZntUyIXhi1JVTLSWDvcRh+mWg=
  dependencies:
    jsesc "~0.5.0"

relateurl@0.2.x:
  version "0.2.7"
  resolved "https://registry.nlark.com/relateurl/download/relateurl-0.2.7.tgz"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://registry.nlark.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

renderkid@^2.0.4:
  version "2.0.7"
  resolved "https://registry.npmmirror.com/renderkid/download/renderkid-2.0.7.tgz"
  integrity sha1-Rk8namvc7mBvShWZP5sp/HTKhgk=
  dependencies:
    css-select "^4.1.3"
    dom-converter "^0.2.0"
    htmlparser2 "^6.1.0"
    lodash "^4.17.21"
    strip-ansi "^3.0.1"

repeat-element@^1.1.2:
  version "1.1.4"
  resolved "https://registry.nlark.com/repeat-element/download/repeat-element-1.1.4.tgz"
  integrity sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://registry.nlark.com/repeat-string/download/repeat-string-1.6.1.tgz"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

repeating@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/repeating/download/repeating-2.0.1.tgz?cache=0&sync_timestamp=1624608101869&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frepeating%2Fdownload%2Frepeating-2.0.1.tgz"
  integrity sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=
  dependencies:
    is-finite "^1.0.0"

request@^2.87.0, request@^2.88.0, request@^2.88.2:
  version "2.88.2"
  resolved "https://registry.npmmirror.com/request/download/request-2.88.2.tgz"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/require-directory/download/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/require-main-filename/download/require-main-filename-2.0.0.tgz"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/requires-port/download/requires-port-1.0.0.tgz"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resolve-cwd@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/resolve-cwd/download/resolve-cwd-2.0.0.tgz"
  integrity sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=
  dependencies:
    resolve-from "^3.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/resolve-from/download/resolve-from-3.0.0.tgz?cache=0&sync_timestamp=1624607952279&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fresolve-from%2Fdownload%2Fresolve-from-3.0.0.tgz"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/resolve-from/download/resolve-from-4.0.0.tgz?cache=0&sync_timestamp=1624607952279&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fresolve-from%2Fdownload%2Fresolve-from-4.0.0.tgz"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/resolve-url/download/resolve-url-0.2.1.tgz"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.10.0, resolve@^1.10.1, resolve@^1.13.1, resolve@^1.14.2, resolve@^1.20.0, resolve@^1.3.2:
  version "1.20.0"
  resolved "https://registry.nlark.com/resolve/download/resolve-1.20.0.tgz"
  integrity sha1-YpoBP7P3B1XW8LeTXMHCxTeLGXU=
  dependencies:
    is-core-module "^2.2.0"
    path-parse "^1.0.6"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/restore-cursor/download/restore-cursor-2.0.0.tgz"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/restore-cursor/download/restore-cursor-3.1.0.tgz"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "https://registry.nlark.com/ret/download/ret-0.1.15.tgz"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

retry@^0.12.0:
  version "0.12.0"
  resolved "https://registry.nlark.com/retry/download/retry-0.12.0.tgz"
  integrity sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/reusify/download/reusify-1.0.4.tgz"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rgb-regex@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/rgb-regex/download/rgb-regex-1.0.1.tgz"
  integrity sha1-wODWiC3w4jviVKR16O3UGRX+rrE=

rgba-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/rgba-regex/download/rgba-regex-1.0.0.tgz"
  integrity sha1-QzdOLiyglosO8VI0YLfXMP8i7rM=

rimraf@2, rimraf@^2.5.4, rimraf@^2.6.1, rimraf@^2.6.3:
  version "2.7.1"
  resolved "https://registry.npmmirror.com/rimraf/download/rimraf-2.7.1.tgz"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@2.6.3:
  version "2.6.3"
  resolved "https://registry.npmmirror.com/rimraf/download/rimraf-2.6.3.tgz"
  integrity sha1-stEE/g2Psnz54KHNqCYt04M8bKs=
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/rimraf/download/rimraf-3.0.2.tgz"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "https://registry.nlark.com/ripemd160/download/ripemd160-2.0.2.tgz"
  integrity sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

run-async@^2.4.0:
  version "2.4.1"
  resolved "https://registry.nlark.com/run-async/download/run-async-2.4.1.tgz"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.nlark.com/run-parallel/download/run-parallel-1.2.0.tgz"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

run-queue@^1.0.0, run-queue@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/run-queue/download/run-queue-1.0.3.tgz"
  integrity sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=
  dependencies:
    aproba "^1.1.1"

rxjs@^6.3.3, rxjs@^6.6.0:
  version "6.6.7"
  resolved "https://registry.npmmirror.com/rxjs/download/rxjs-6.6.7.tgz"
  integrity sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=
  dependencies:
    tslib "^1.9.0"

safe-buffer@5.1.2, safe-buffer@>=5.1.0, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.nlark.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@5.2.1, safe-buffer@^5.2.0, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.nlark.com/safe-buffer/download/safe-buffer-5.2.1.tgz"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/safe-regex/download/safe-regex-1.1.0.tgz"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://registry.nlark.com/safer-buffer/download/safer-buffer-2.1.2.tgz"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sass-graph@2.2.5:
  version "2.2.5"
  resolved "https://registry.npmmirror.com/sass-graph/download/sass-graph-2.2.5.tgz"
  integrity sha1-qYHIdEa4MZ2W3OBnHkh4eb0kwug=
  dependencies:
    glob "^7.0.0"
    lodash "^4.0.0"
    scss-tokenizer "^0.2.3"
    yargs "^13.3.2"

sass-loader@^8.0.2:
  version "8.0.2"
  resolved "https://registry.npmmirror.com/sass-loader/download/sass-loader-8.0.2.tgz"
  integrity sha1-3r7NjDziQ8dkVPLoKQSCFQOACQ0=
  dependencies:
    clone-deep "^4.0.1"
    loader-utils "^1.2.3"
    neo-async "^2.6.1"
    schema-utils "^2.6.1"
    semver "^6.3.0"

sass@^1.45.2:
  version "1.45.2"
  resolved "https://registry.npmmirror.com/sass/-/sass-1.45.2.tgz"
  integrity sha512-cKfs+F9AMPAFlbbTXNsbGvg3y58nV0mXA3E94jqaySKcC8Kq3/8983zVKQ0TLMUrHw7hF9Tnd3Bz9z5Xgtrl9g==
  dependencies:
    chokidar ">=3.0.0 <4.0.0"
    immutable "^4.0.0"
    source-map-js ">=0.6.2 <2.0.0"

sax@~1.2.4:
  version "1.2.4"
  resolved "https://registry.nlark.com/sax/download/sax-1.2.4.tgz"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

schema-utils@2.7.0:
  version "2.7.0"
  resolved "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.7.0.tgz?cache=0&sync_timestamp=1637075967293&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-2.7.0.tgz"
  integrity sha1-FxUfdtjq5n+793lgwzxnatn078c=
  dependencies:
    "@types/json-schema" "^7.0.4"
    ajv "^6.12.2"
    ajv-keywords "^3.4.1"

schema-utils@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/schema-utils/download/schema-utils-1.0.0.tgz?cache=0&sync_timestamp=1637075967293&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-1.0.0.tgz"
  integrity sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=
  dependencies:
    ajv "^6.1.0"
    ajv-errors "^1.0.0"
    ajv-keywords "^3.1.0"

schema-utils@^2.0.0, schema-utils@^2.5.0, schema-utils@^2.6.1, schema-utils@^2.6.5, schema-utils@^2.7.0:
  version "2.7.1"
  resolved "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.7.1.tgz?cache=0&sync_timestamp=1637075967293&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-2.7.1.tgz"
  integrity sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

schema-utils@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.1.1.tgz"
  integrity sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw==
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

scss-tokenizer@^0.2.3:
  version "0.2.3"
  resolved "https://registry.nlark.com/scss-tokenizer/download/scss-tokenizer-0.2.3.tgz"
  integrity sha1-jrBtualyMzOCTT9VMGQRSYR85dE=
  dependencies:
    js-base64 "^2.1.8"
    source-map "^0.4.2"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/select-hose/download/select-hose-2.0.0.tgz"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

selfsigned@^1.10.8:
  version "1.10.11"
  resolved "https://registry.nlark.com/selfsigned/download/selfsigned-1.10.11.tgz"
  integrity sha1-JJKc2Qb+D0S20B+yOZmnOVN6y+k=
  dependencies:
    node-forge "^0.10.0"

semver-compare@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/semver-compare/download/semver-compare-1.0.0.tgz"
  integrity sha1-De4hahyUGrN+nvsXiPavxf9VN/w=

"semver@2 || 3 || 4 || 5", semver@^5.3.0, semver@^5.5.0, semver@^5.6.0, semver@^5.7.1:
  version "5.7.1"
  resolved "https://registry.nlark.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1624607961409&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@7.0.0:
  version "7.0.0"
  resolved "https://registry.nlark.com/semver/download/semver-7.0.0.tgz?cache=0&sync_timestamp=1624607961409&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-7.0.0.tgz"
  integrity sha1-XzyjV2HkfgWyBsba/yz4FPAxa44=

semver@^6.0.0, semver@^6.1.0, semver@^6.1.1, semver@^6.1.2, semver@^6.3.0:
  version "6.3.0"
  resolved "https://registry.nlark.com/semver/download/semver-6.3.0.tgz?cache=0&sync_timestamp=1624607961409&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-6.3.0.tgz"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@^7.3.2, semver@^7.3.5:
  version "7.3.5"
  resolved "https://registry.nlark.com/semver/download/semver-7.3.5.tgz?cache=0&sync_timestamp=1624607961409&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-7.3.5.tgz"
  integrity sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc=
  dependencies:
    lru-cache "^6.0.0"

semver@~5.3.0:
  version "5.3.0"
  resolved "https://registry.nlark.com/semver/download/semver-5.3.0.tgz?cache=0&sync_timestamp=1624607961409&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-5.3.0.tgz"
  integrity sha1-myzl094C0XxgEq0yaqa00M9U+U8=

send@0.17.2:
  version "0.17.2"
  resolved "https://registry.npmmirror.com/send/download/send-0.17.2.tgz"
  integrity sha512-UJYB6wFSJE3G00nEivR5rgWp8c2xXvJ3OPWPhmuteU0IKj8nKbG3DrjiOmLwpnHGYWAVwA69zmTm++YG0Hmwww==
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "1.8.1"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

serialize-javascript@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/serialize-javascript/download/serialize-javascript-4.0.0.tgz?cache=0&sync_timestamp=1624608021459&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fserialize-javascript%2Fdownload%2Fserialize-javascript-4.0.0.tgz"
  integrity sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://registry.nlark.com/serve-index/download/serve-index-1.9.1.tgz"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.14.2:
  version "1.14.2"
  resolved "https://registry.npmmirror.com/serve-static/download/serve-static-1.14.2.tgz"
  integrity sha512-+TMNA9AFxUEGuC0z2mevogSnn9MXKb4fa7ngeRMJaaGv8vTwnIEkKi+QGvPt33HSnf8pRS+WGM0EbMtCJLKMBQ==
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.2"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/set-blocking/download/set-blocking-2.0.0.tgz"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/set-value/download/set-value-2.0.1.tgz?cache=0&sync_timestamp=1631437838608&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fset-value%2Fdownload%2Fset-value-2.0.1.tgz"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/setimmediate/download/setimmediate-1.0.5.tgz"
  integrity sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/setprototypeof/download/setprototypeof-1.1.0.tgz"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/setprototypeof/download/setprototypeof-1.2.0.tgz"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "https://registry.nlark.com/sha.js/download/sha.js-2.4.11.tgz"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "https://registry.nlark.com/shallow-clone/download/shallow-clone-3.0.1.tgz"
  integrity sha1-jymBrZJTH1UDWwH7IwdppA4C76M=
  dependencies:
    kind-of "^6.0.2"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/shebang-command/download/shebang-command-1.2.0.tgz"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/shebang-command/download/shebang-command-2.0.0.tgz"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/shebang-regex/download/shebang-regex-1.0.0.tgz?cache=0&sync_timestamp=1628896660639&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fshebang-regex%2Fdownload%2Fshebang-regex-1.0.0.tgz"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/shebang-regex/download/shebang-regex-3.0.0.tgz?cache=0&sync_timestamp=1628896660639&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fshebang-regex%2Fdownload%2Fshebang-regex-3.0.0.tgz"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-quote@^1.6.1:
  version "1.7.3"
  resolved "https://registry.npmmirror.com/shell-quote/download/shell-quote-1.7.3.tgz"
  integrity sha1-qkDtrBcERbmkMeF7tiwLiBucQSM=

side-channel@^1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/side-channel/download/side-channel-1.0.4.tgz"
  integrity sha1-785cj9wQTudRslxY1CkAEfpeos8=
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.6"
  resolved "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.6.tgz"
  integrity sha512-sDl4qMFpijcGw22U5w63KmD3cZJfBuFlVNbVMKje2keoKML7X2UzWbc4XrmEbDwg0NXJc3yv4/ox7b+JWb57kQ==

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmmirror.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

slash@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/slash/download/slash-1.0.0.tgz"
  integrity sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=

slash@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/slash/download/slash-2.0.0.tgz"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/slash/download/slash-3.0.0.tgz"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@0.0.4:
  version "0.0.4"
  resolved "https://registry.nlark.com/slice-ansi/download/slice-ansi-0.0.4.tgz"
  integrity sha1-7b+JA/ZvfOL46v1s7tZeJkyDGzU=

slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/slice-ansi/download/slice-ansi-2.1.0.tgz"
  integrity sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "https://registry.nlark.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "https://registry.nlark.com/snapdragon/download/snapdragon-0.8.2.tgz"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

sockjs-client@^1.5.0:
  version "1.5.2"
  resolved "https://registry.nlark.com/sockjs-client/download/sockjs-client-1.5.2.tgz?cache=0&sync_timestamp=1629825451221&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsockjs-client%2Fdownload%2Fsockjs-client-1.5.2.tgz"
  integrity sha1-S8SMLanOR2nxnccjOWtQ9cEjMKM=
  dependencies:
    debug "^3.2.6"
    eventsource "^1.0.7"
    faye-websocket "^0.11.3"
    inherits "^2.0.4"
    json3 "^3.3.3"
    url-parse "^1.5.3"

sockjs@^0.3.21:
  version "0.3.24"
  resolved "https://registry.npmmirror.com/sockjs/download/sockjs-0.3.24.tgz"
  integrity sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^8.3.2"
    websocket-driver "^0.7.4"

sort-keys@^1.0.0:
  version "1.1.2"
  resolved "https://registry.nlark.com/sort-keys/download/sort-keys-1.1.2.tgz"
  integrity sha1-RBttTTRnmPG05J6JIK37oOVD+a0=
  dependencies:
    is-plain-obj "^1.0.0"

source-list-map@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/source-list-map/download/source-list-map-2.0.1.tgz"
  integrity sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=

"source-map-js@>=0.6.2 <2.0.0", source-map-js@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/source-map-js/download/source-map-js-1.0.1.tgz"
  integrity sha512-4+TN2b3tqOCd/kaGRJ/sTYA0tR0mdXx26ipdolxcwtJVqEnqNYvlCAt1q3ypy4QMlYus+Zh34RNtYLoq2oQ4IA==

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "https://registry.nlark.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@~0.5.12:
  version "0.5.21"
  resolved "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.5.21.tgz?cache=0&sync_timestamp=1637320256759&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.5.21.tgz"
  integrity sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.1"
  resolved "https://registry.nlark.com/source-map-url/download/source-map-url-0.4.1.tgz"
  integrity sha1-CvZmBadFpaL5HPG7+KevvCg97FY=

source-map@^0.4.2:
  version "0.4.4"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.4.4.tgz?cache=0&sync_timestamp=1624608014898&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsource-map%2Fdownload%2Fsource-map-0.4.4.tgz"
  integrity sha1-66T12pwNyZneaAMti092FzZSA2s=
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.5.0, source-map@^0.5.6:
  version "0.5.7"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.5.7.tgz?cache=0&sync_timestamp=1624608014898&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsource-map%2Fdownload%2Fsource-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.6.1.tgz?cache=0&sync_timestamp=1624608014898&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsource-map%2Fdownload%2Fsource-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.7.3:
  version "0.7.3"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.7.3.tgz?cache=0&sync_timestamp=1624608014898&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsource-map%2Fdownload%2Fsource-map-0.7.3.tgz"
  integrity sha1-UwL4FpAxc1ImVECS5kmB91F1A4M=

sourcemap-codec@^1.4.4:
  version "1.4.8"
  resolved "https://registry.nlark.com/sourcemap-codec/download/sourcemap-codec-1.4.8.tgz"
  integrity sha1-6oBL2UhXQC5pktBaOO8a41qatMQ=

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "https://registry.nlark.com/spdx-correct/download/spdx-correct-3.1.1.tgz"
  integrity sha1-3s6BrJweZxPl99G28X1Gj6U9iak=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "https://registry.nlark.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://registry.nlark.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.11"
  resolved "https://registry.npmmirror.com/spdx-license-ids/download/spdx-license-ids-3.0.11.tgz?cache=0&sync_timestamp=1636978442514&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fspdx-license-ids%2Fdownload%2Fspdx-license-ids-3.0.11.tgz"
  integrity sha512-Ctl2BrFiM0X3MANYgj3CkygxhRmr9mi6xhejbdO960nF6EDJApTYpn0BQnDKlnNBULKiCN1n3w9EBkHK8ZWg+g==

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/spdy-transport/download/spdy-transport-3.0.0.tgz"
  integrity sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "https://registry.nlark.com/spdy/download/spdy-4.0.2.tgz"
  integrity sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "https://registry.nlark.com/split-string/download/split-string-3.1.0.tgz"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.nlark.com/sprintf-js/download/sprintf-js-1.0.3.tgz"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sshpk@^1.7.0:
  version "1.16.1"
  resolved "https://registry.nlark.com/sshpk/download/sshpk-1.16.1.tgz"
  integrity sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssri@^6.0.1:
  version "6.0.2"
  resolved "https://registry.nlark.com/ssri/download/ssri-6.0.2.tgz"
  integrity sha1-FXk5E08gRk5zAd26PpD/qPdyisU=
  dependencies:
    figgy-pudding "^3.5.1"

ssri@^8.0.1:
  version "8.0.1"
  resolved "https://registry.nlark.com/ssri/download/ssri-8.0.1.tgz"
  integrity sha1-Y45OQ54v+9LNKJd21cpFfE9Roq8=
  dependencies:
    minipass "^3.1.1"

stable@^0.1.8:
  version "0.1.8"
  resolved "https://registry.npmmirror.com/stable/download/stable-0.1.8.tgz"
  integrity sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=

stackframe@^1.1.1:
  version "1.2.0"
  resolved "https://registry.nlark.com/stackframe/download/stackframe-1.2.0.tgz"
  integrity sha1-UkKUktY8YuuYmATBFVLj0i53kwM=

static-extend@^0.1.1:
  version "0.1.2"
  resolved "https://registry.nlark.com/static-extend/download/static-extend-0.1.2.tgz"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "https://registry.nlark.com/statuses/download/statuses-1.5.0.tgz"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

stdout-stream@^1.4.0:
  version "1.4.1"
  resolved "https://registry.nlark.com/stdout-stream/download/stdout-stream-1.4.1.tgz"
  integrity sha1-WsF0zdXNcmEEqgwLK9g4FdjVNd4=
  dependencies:
    readable-stream "^2.0.1"

stream-browserify@^2.0.1:
  version "2.0.2"
  resolved "https://registry.nlark.com/stream-browserify/download/stream-browserify-2.0.2.tgz"
  integrity sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-each@^1.1.0:
  version "1.2.3"
  resolved "https://registry.nlark.com/stream-each/download/stream-each-1.2.3.tgz"
  integrity sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=
  dependencies:
    end-of-stream "^1.1.0"
    stream-shift "^1.0.0"

stream-http@^2.7.2:
  version "2.8.3"
  resolved "https://registry.nlark.com/stream-http/download/stream-http-2.8.3.tgz"
  integrity sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-shift@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/stream-shift/download/stream-shift-1.0.1.tgz"
  integrity sha1-1wiCgVWasneEJCebCHfaPDktWj0=

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz"
  integrity sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=

string-argv@^0.3.0:
  version "0.3.1"
  resolved "https://registry.nlark.com/string-argv/download/string-argv-0.3.1.tgz"
  integrity sha1-leL77AQnrhkYSTX4FtdKqkxcGdo=

string-width@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/string-width/download/string-width-1.0.2.tgz?cache=0&sync_timestamp=1632421054789&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-1.0.2.tgz"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

"string-width@^1.0.2 || 2 || 3 || 4", string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz?cache=0&sync_timestamp=1632421054789&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/string-width/download/string-width-2.1.1.tgz?cache=0&sync_timestamp=1632421054789&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-2.1.1.tgz"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/string-width/download/string-width-3.1.0.tgz?cache=0&sync_timestamp=1632421054789&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-3.1.0.tgz"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string.prototype.trimend@^1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/string.prototype.trimend/download/string.prototype.trimend-1.0.4.tgz"
  integrity sha1-51rpDClCxjUEaGwYsoe0oLGkX4A=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

string.prototype.trimstart@^1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.4.tgz"
  integrity sha1-s2OZr0qymZtMnGSL16P7K7Jv7u0=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

string_decoder@^1.0.0, string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/string_decoder/download/string_decoder-1.1.1.tgz"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.nlark.com/string_decoder/download/string_decoder-1.3.0.tgz"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

stringify-object@^3.3.0:
  version "3.3.0"
  resolved "https://registry.nlark.com/stringify-object/download/stringify-object-3.3.0.tgz?cache=0&sync_timestamp=1629674005960&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstringify-object%2Fdownload%2Fstringify-object-3.3.0.tgz"
  integrity sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=
  dependencies:
    get-own-enumerable-property-symbols "^3.0.0"
    is-obj "^1.0.1"
    is-regexp "^1.0.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-3.0.1.tgz"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-4.0.0.tgz"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-5.2.0.tgz"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/strip-bom/download/strip-bom-2.0.0.tgz?cache=0&sync_timestamp=1624608094529&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-bom%2Fdownload%2Fstrip-bom-2.0.0.tgz"
  integrity sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=
  dependencies:
    is-utf8 "^0.2.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/strip-bom/download/strip-bom-3.0.0.tgz?cache=0&sync_timestamp=1624608094529&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-bom%2Fdownload%2Fstrip-bom-3.0.0.tgz"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/strip-eof/download/strip-eof-1.0.0.tgz"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-indent@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/strip-indent/download/strip-indent-1.0.1.tgz"
  integrity sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI=
  dependencies:
    get-stdin "^4.0.1"

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/strip-indent/download/strip-indent-2.0.0.tgz"
  integrity sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=

strip-json-comments@^3.0.1:
  version "3.1.1"
  resolved "https://registry.nlark.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

stylehacks@^4.0.0:
  version "4.0.3"
  resolved "https://registry.nlark.com/stylehacks/download/stylehacks-4.0.3.tgz"
  integrity sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU=
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-2.0.0.tgz"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-6.1.0.tgz"
  integrity sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-7.2.0.tgz"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/svg-tags/download/svg-tags-1.0.0.tgz"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

svgo@^1.0.0:
  version "1.3.2"
  resolved "https://registry.npmmirror.com/svgo/download/svgo-1.3.2.tgz"
  integrity sha512-yhy/sQYxR5BkC98CY7o31VGsg014AKLEPxdfhora76l36hD9Rdy5NZA/Ocn6yayNPgSamYdtX2rFJdcv07AYVw==
  dependencies:
    chalk "^2.4.1"
    coa "^2.0.2"
    css-select "^2.0.0"
    css-select-base-adapter "^0.1.1"
    css-tree "1.0.0-alpha.37"
    csso "^4.0.2"
    js-yaml "^3.13.1"
    mkdirp "~0.5.1"
    object.values "^1.1.0"
    sax "~1.2.4"
    stable "^0.1.8"
    unquote "~1.1.1"
    util.promisify "~1.0.0"

symbol-observable@^1.1.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/symbol-observable/download/symbol-observable-1.2.0.tgz"
  integrity sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ=

table@^5.2.3:
  version "5.4.6"
  resolved "https://registry.npmmirror.com/table/download/table-5.4.6.tgz"
  integrity sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

tapable@^0.1.8:
  version "0.1.10"
  resolved "https://registry.npmmirror.com/tapable/download/tapable-0.1.10.tgz"
  integrity sha512-jX8Et4hHg57mug1/079yitEKWGB3LCwoxByLsNim89LABq8NqgiX+6iYVOsq0vX8uJHkU+DZ5fnq95f800bEsQ==

tapable@^1.0.0, tapable@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/tapable/download/tapable-1.1.3.tgz"
  integrity sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA==

tar-stream@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/tar-stream/-/tar-stream-2.2.0.tgz"
  integrity sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==
  dependencies:
    bl "^4.0.3"
    end-of-stream "^1.4.1"
    fs-constants "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^3.1.1"

tar@^2.0.0:
  version "2.2.2"
  resolved "https://registry.nlark.com/tar/download/tar-2.2.2.tgz?cache=0&sync_timestamp=1629994977916&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftar%2Fdownload%2Ftar-2.2.2.tgz"
  integrity sha1-DKiEhWLHKZuLRG/2pNYM27I+3EA=
  dependencies:
    block-stream "*"
    fstream "^1.0.12"
    inherits "2"

terser-webpack-plugin@^1.4.3, terser-webpack-plugin@^1.4.4:
  version "1.4.5"
  resolved "https://registry.npmmirror.com/terser-webpack-plugin/download/terser-webpack-plugin-1.4.5.tgz"
  integrity sha1-oheu+uozDnNP+sthIOwfoxLWBAs=
  dependencies:
    cacache "^12.0.2"
    find-cache-dir "^2.1.0"
    is-wsl "^1.1.0"
    schema-utils "^1.0.0"
    serialize-javascript "^4.0.0"
    source-map "^0.6.1"
    terser "^4.1.2"
    webpack-sources "^1.4.0"
    worker-farm "^1.7.0"

terser@^4.1.2:
  version "4.8.0"
  resolved "https://registry.npmmirror.com/terser/download/terser-4.8.0.tgz?cache=0&sync_timestamp=1636988176283&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fterser%2Fdownload%2Fterser-4.8.0.tgz"
  integrity sha1-YwVjQ9fHC7KfOvZlhlpG/gOg3xc=
  dependencies:
    commander "^2.20.0"
    source-map "~0.6.1"
    source-map-support "~0.5.12"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/text-table/download/text-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://registry.nlark.com/thenify-all/download/thenify-all-1.6.0.tgz"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://registry.nlark.com/thenify/download/thenify-3.3.1.tgz"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

thread-loader@^2.1.3:
  version "2.1.3"
  resolved "https://registry.nlark.com/thread-loader/download/thread-loader-2.1.3.tgz"
  integrity sha1-y9LBOfwrLebp0o9iKGq3cMGsvdo=
  dependencies:
    loader-runner "^2.3.1"
    loader-utils "^1.1.0"
    neo-async "^2.6.0"

through2@^2.0.0:
  version "2.0.5"
  resolved "https://registry.nlark.com/through2/download/through2-2.0.5.tgz"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

through@^2.3.6:
  version "2.3.8"
  resolved "https://registry.nlark.com/through/download/through-2.3.8.tgz"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://registry.nlark.com/thunky/download/thunky-1.1.0.tgz"
  integrity sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=

timers-browserify@^2.0.4:
  version "2.0.12"
  resolved "https://registry.nlark.com/timers-browserify/download/timers-browserify-2.0.12.tgz"
  integrity sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4=
  dependencies:
    setimmediate "^1.0.4"

timsort@^0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/timsort/download/timsort-0.3.0.tgz"
  integrity sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.nlark.com/tmp/download/tmp-0.0.33.tgz"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz"
  integrity sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz?cache=0&sync_timestamp=1628418893613&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fto-fast-properties%2Fdownload%2Fto-fast-properties-2.0.0.tgz"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/to-object-path/download/to-object-path-0.3.0.tgz"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "https://registry.nlark.com/to-regex-range/download/to-regex-range-2.1.1.tgz"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.nlark.com/to-regex-range/download/to-regex-range-5.0.1.tgz"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/to-regex/download/to-regex-3.0.2.tgz"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/toidentifier/download/toidentifier-1.0.1.tgz?cache=0&sync_timestamp=1636938499270&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftoidentifier%2Fdownload%2Ftoidentifier-1.0.1.tgz"
  integrity sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==

toposort@^1.0.0:
  version "1.0.7"
  resolved "https://registry.nlark.com/toposort/download/toposort-1.0.7.tgz"
  integrity sha1-LmhELZ9k7HILjMieZEOsbKqVACk=

tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "https://registry.nlark.com/tough-cookie/download/tough-cookie-2.5.0.tgz"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

trim-newlines@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/trim-newlines/download/trim-newlines-1.0.0.tgz"
  integrity sha1-WIeWa7WCpFA6QetST301ARgVphM=

"true-case-path@^1.0.2":
  version "1.0.3"
  resolved "https://registry.nlark.com/true-case-path/download/true-case-path-1.0.3.tgz"
  integrity sha1-+BO1qMhrQNpZYGcisUTjIleZ9H0=
  dependencies:
    glob "^7.1.2"

tryer@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/tryer/download/tryer-1.0.1.tgz"
  integrity sha1-8shUBoALmw90yfdGW4HqrSQSUvg=

ts-loader@^6.2.2:
  version "6.2.2"
  resolved "https://registry.nlark.com/ts-loader/download/ts-loader-6.2.2.tgz?cache=0&sync_timestamp=1632169470976&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fts-loader%2Fdownload%2Fts-loader-6.2.2.tgz"
  integrity sha1-3/o4ebAaGh4KS4XiuEIdwN//HFg=
  dependencies:
    chalk "^2.3.0"
    enhanced-resolve "^4.0.0"
    loader-utils "^1.0.2"
    micromatch "^4.0.0"
    semver "^6.0.0"

ts-pnp@^1.1.6:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/ts-pnp/download/ts-pnp-1.2.0.tgz"
  integrity sha1-pQCtCEsHmPHDBxrzkeZZEshrypI=

tsconfig-paths@^3.11.0:
  version "3.12.0"
  resolved "https://registry.npmmirror.com/tsconfig-paths/download/tsconfig-paths-3.12.0.tgz?cache=0&sync_timestamp=1637404842509&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftsconfig-paths%2Fdownload%2Ftsconfig-paths-3.12.0.tgz"
  integrity sha512-e5adrnOYT6zqVnWqZu7i/BQ3BnhzvGbjEjejFXO20lKIKpwTaupkCPgEfv4GZK1IBciJUEhYs3J3p75FdaTFVg==
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.1"
    minimist "^1.2.0"
    strip-bom "^3.0.0"

tslib@2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/tslib/-/tslib-2.3.0.tgz"
  integrity sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==

tslib@^1.8.0, tslib@^1.8.1, tslib@^1.9.0:
  version "1.14.1"
  resolved "https://registry.nlark.com/tslib/download/tslib-1.14.1.tgz"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslint@^5.20.1:
  version "5.20.1"
  resolved "https://registry.npmmirror.com/tslint/download/tslint-5.20.1.tgz"
  integrity sha512-EcMxhzCFt8k+/UP5r8waCf/lzmeSyVlqxqMEDQE7rWYiQky8KpIBz1JAoYXfROHrPZ1XXd43q8yQnULOLiBRQg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    builtin-modules "^1.1.1"
    chalk "^2.3.0"
    commander "^2.12.1"
    diff "^4.0.1"
    glob "^7.1.1"
    js-yaml "^3.13.1"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    resolve "^1.3.2"
    semver "^5.3.0"
    tslib "^1.8.0"
    tsutils "^2.29.0"

tsutils@^2.29.0:
  version "2.29.0"
  resolved "https://registry.nlark.com/tsutils/download/tsutils-2.29.0.tgz"
  integrity sha1-MrSIUBRnrL7dS4VJhnOggSrKC5k=
  dependencies:
    tslib "^1.8.1"

tsutils@^3.21.0:
  version "3.21.0"
  resolved "https://registry.nlark.com/tsutils/download/tsutils-3.21.0.tgz"
  integrity sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=
  dependencies:
    tslib "^1.8.1"

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "https://registry.nlark.com/tty-browserify/download/tty-browserify-0.0.0.tgz"
  integrity sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://registry.nlark.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://registry.nlark.com/tweetnacl/download/tweetnacl-0.14.5.tgz?cache=0&sync_timestamp=1624607953624&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftweetnacl%2Fdownload%2Ftweetnacl-0.14.5.tgz"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://registry.nlark.com/type-check/download/type-check-0.3.2.tgz"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://registry.npmmirror.com/type-fest/download/type-fest-0.21.3.tgz"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npmmirror.com/type-fest/download/type-fest-0.6.0.tgz"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "https://registry.npmmirror.com/type-fest/download/type-fest-0.8.1.tgz"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.nlark.com/type-is/download/type-is-1.6.18.tgz"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://registry.nlark.com/typedarray/download/typedarray-0.0.6.tgz"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

typescript@~4.1.5:
  version "4.1.6"
  resolved "https://registry.npmmirror.com/typescript/download/typescript-4.1.6.tgz"
  integrity sha512-pxnwLxeb/Z5SP80JDRzVjh58KsM6jZHRAOtTpS7sXLS4ogXNKC9ANxHHZqLLeVHZN35jCtI4JdmLLbLiC1kBow==

uglify-js@3.4.x:
  version "3.4.10"
  resolved "https://registry.npmmirror.com/uglify-js/download/uglify-js-3.4.10.tgz"
  integrity sha1-mtlWPY6zrN+404WX0q8dgV9qdV8=
  dependencies:
    commander "~2.19.0"
    source-map "~0.6.1"

unbox-primitive@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/unbox-primitive/download/unbox-primitive-1.0.1.tgz"
  integrity sha1-CF4hViXsMWJXTciFmr7nilmxRHE=
  dependencies:
    function-bind "^1.1.1"
    has-bigints "^1.0.1"
    has-symbols "^1.0.2"
    which-boxed-primitive "^1.0.2"

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.0.tgz?cache=0&sync_timestamp=1631615391251&other_urls=https%3A%2F%2Fregistry.nlark.com%2Funicode-canonical-property-names-ecmascript%2Fdownload%2Funicode-canonical-property-names-ecmascript-2.0.0.tgz"
  integrity sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw=

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz?cache=0&sync_timestamp=1631618607567&other_urls=https%3A%2F%2Fregistry.nlark.com%2Funicode-match-property-ecmascript%2Fdownload%2Funicode-match-property-ecmascript-2.0.0.tgz"
  integrity sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.0.0.tgz?cache=0&sync_timestamp=1631618158421&other_urls=https%3A%2F%2Fregistry.nlark.com%2Funicode-match-property-value-ecmascript%2Fdownload%2Funicode-match-property-value-ecmascript-2.0.0.tgz"
  integrity sha1-GgGqVyR8FMVouJd1pUk4eIGJpxQ=

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Funicode-property-aliases-ecmascript%2Fdownload%2Funicode-property-aliases-ecmascript-2.0.0.tgz"
  integrity sha1-CjbLmlhcT2q9Ua0d7dsoXBZSl8g=

union-value@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/union-value/download/union-value-1.0.1.tgz"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

uniq@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/uniq/download/uniq-1.0.1.tgz"
  integrity sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=

uniqs@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/uniqs/download/uniqs-2.0.0.tgz"
  integrity sha1-/+3ks2slKQaW5uFl1KWe25mOawI=

unique-filename@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/unique-filename/download/unique-filename-1.1.1.tgz"
  integrity sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.2"
  resolved "https://registry.nlark.com/unique-slug/download/unique-slug-2.0.2.tgz"
  integrity sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=
  dependencies:
    imurmurhash "^0.1.4"

universalify@^0.1.0:
  version "0.1.2"
  resolved "https://registry.nlark.com/universalify/download/universalify-0.1.2.tgz"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

universalify@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/universalify/download/universalify-2.0.0.tgz"
  integrity sha1-daSYTv7cSwiXXFrrc/Uw0C3yVxc=

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/unpipe/download/unpipe-1.0.0.tgz"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unquote@~1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/unquote/download/unquote-1.1.1.tgz"
  integrity sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/unset-value/download/unset-value-1.0.0.tgz"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.1.1:
  version "1.2.0"
  resolved "https://registry.nlark.com/upath/download/upath-1.2.0.tgz"
  integrity sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=

upper-case@^1.1.1:
  version "1.1.3"
  resolved "https://registry.nlark.com/upper-case/download/upper-case-1.1.3.tgz"
  integrity sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.nlark.com/uri-js/download/uri-js-4.4.1.tgz"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/urix/download/urix-0.1.0.tgz"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-loader@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/url-loader/download/url-loader-2.3.0.tgz"
  integrity sha1-4OLvZY8APvuMpBsPP/v3a6uIZYs=
  dependencies:
    loader-utils "^1.2.3"
    mime "^2.4.4"
    schema-utils "^2.5.0"

url-parse@^1.4.3, url-parse@^1.5.3:
  version "1.5.4"
  resolved "https://registry.npmmirror.com/url-parse/download/url-parse-1.5.4.tgz"
  integrity sha512-ITeAByWWoqutFClc/lRZnFplgXgEZr3WJ6XngMM/N9DMIm4K8zXPCZ1Jdu0rERwO84w1WC5wkle2ubwTA4NTBg==
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url@^0.11.0:
  version "0.11.0"
  resolved "https://registry.npmmirror.com/url/download/url-0.11.0.tgz"
  integrity sha512-kbailJa29QrtXnxgq+DdCEGlbTeYM2eJUxsz6vjZavrCYPMIFHMKQmSKYAIuUK2i7hgPm28a8piX5NTUtM/LKQ==
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use@^3.1.0:
  version "3.1.1"
  resolved "https://registry.nlark.com/use/download/use-3.1.1.tgz"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.nlark.com/util-deprecate/download/util-deprecate-1.0.2.tgz?cache=0&sync_timestamp=1624607944834&other_urls=https%3A%2F%2Fregistry.nlark.com%2Futil-deprecate%2Fdownload%2Futil-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util.promisify@1.0.0, util.promisify@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/util.promisify/download/util.promisify-1.0.0.tgz"
  integrity sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA=
  dependencies:
    define-properties "^1.1.2"
    object.getownpropertydescriptors "^2.0.3"

util@0.10.3:
  version "0.10.3"
  resolved "https://registry.nlark.com/util/download/util-0.10.3.tgz"
  integrity sha1-evsa/lCAUkZInj23/g7TeTNqwPk=
  dependencies:
    inherits "2.0.1"

util@^0.11.0:
  version "0.11.1"
  resolved "https://registry.nlark.com/util/download/util-0.11.1.tgz"
  integrity sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=
  dependencies:
    inherits "2.0.3"

utila@~0.4:
  version "0.4.0"
  resolved "https://registry.nlark.com/utila/download/utila-0.4.0.tgz"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/utils-merge/download/utils-merge-1.0.1.tgz"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^3.3.2:
  version "3.4.0"
  resolved "https://registry.npmmirror.com/uuid/download/uuid-3.4.0.tgz"
  integrity sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://registry.npmmirror.com/uuid/download/uuid-8.3.2.tgz"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

v8-compile-cache@^2.0.3:
  version "2.3.0"
  resolved "https://registry.nlark.com/v8-compile-cache/download/v8-compile-cache-2.3.0.tgz"
  integrity sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4=

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://registry.nlark.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/vary/download/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vendors@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/vendors/download/vendors-1.0.4.tgz"
  integrity sha1-4rgApT56Kbk1BsPPQRANFsTErY4=

verror@1.10.0:
  version "1.10.0"
  resolved "https://registry.npmmirror.com/verror/download/verror-1.10.0.tgz"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vm-browserify@^1.0.1:
  version "1.1.2"
  resolved "https://registry.nlark.com/vm-browserify/download/vm-browserify-1.1.2.tgz"
  integrity sha1-eGQcSIuObKkadfUR56OzKobl3aA=

vue-demi@*:
  version "0.12.1"
  resolved "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.12.1.tgz"
  integrity sha512-QL3ny+wX8c6Xm1/EZylbgzdoDolye+VpCXRhI2hug9dJTP3OUJ3lmiKN3CsVV3mOJKwFi0nsstbgob0vG7aoIw==

vue-eslint-parser@^7.0.0:
  version "7.11.0"
  resolved "https://registry.npmmirror.com/vue-eslint-parser/download/vue-eslint-parser-7.11.0.tgz"
  integrity sha1-IUtd6pYQB/z/su5luJEjB2KNDa8=
  dependencies:
    debug "^4.1.1"
    eslint-scope "^5.1.1"
    eslint-visitor-keys "^1.1.0"
    espree "^6.2.1"
    esquery "^1.4.0"
    lodash "^4.17.21"
    semver "^6.3.0"

vue-eslint-parser@^8.0.1:
  version "8.0.1"
  resolved "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-8.0.1.tgz"
  integrity sha512-lhWjDXJhe3UZw2uu3ztX51SJAPGPey1Tff2RK3TyZURwbuI4vximQLzz4nQfCv8CZq4xx7uIiogHMMoSJPr33A==
  dependencies:
    debug "^4.3.2"
    eslint-scope "^6.0.0"
    eslint-visitor-keys "^3.0.0"
    espree "^9.0.0"
    esquery "^1.4.0"
    lodash "^4.17.21"
    semver "^7.3.5"

vue-hot-reload-api@^2.3.0:
  version "2.3.4"
  resolved "https://registry.nlark.com/vue-hot-reload-api/download/vue-hot-reload-api-2.3.4.tgz"
  integrity sha1-UylVzB6yCKPZkLOp+acFdGV+CPI=

"vue-loader-v16@npm:vue-loader@^16.1.0":
  version "16.8.3"
  resolved "https://registry.npmmirror.com/vue-loader/download/vue-loader-16.8.3.tgz"
  integrity sha512-7vKN45IxsKxe5GcVCbc2qFU5aWzyiLrYJyUuMz4BQLKctCj/fmCa0w6fGiiQ2cLFetNcek1ppGJQDCup0c1hpA==
  dependencies:
    chalk "^4.1.0"
    hash-sum "^2.0.0"
    loader-utils "^2.0.0"

vue-loader@^15.9.2:
  version "15.9.8"
  resolved "https://registry.npmmirror.com/vue-loader/download/vue-loader-15.9.8.tgz"
  integrity sha512-GwSkxPrihfLR69/dSV3+5CdMQ0D+jXg8Ma1S4nQXKJAznYFX14vHdc/NetQc34Dw+rBbIJyP7JOuVb9Fhprvog==
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    hash-sum "^1.0.2"
    loader-utils "^1.1.0"
    vue-hot-reload-api "^2.3.0"
    vue-style-loader "^4.1.0"

vue-router@^4.0.0-0:
  version "4.0.12"
  resolved "https://registry.npmmirror.com/vue-router/download/vue-router-4.0.12.tgz"
  integrity sha512-CPXvfqe+mZLB1kBWssssTiWg4EQERyqJZes7USiqfW9B5N2x+nHlnsM1D3b5CaJ6qgCvMmYJnz+G0iWjNCvXrg==
  dependencies:
    "@vue/devtools-api" "^6.0.0-beta.18"

vue-style-loader@^4.1.0, vue-style-loader@^4.1.2:
  version "4.1.3"
  resolved "https://registry.nlark.com/vue-style-loader/download/vue-style-loader-4.1.3.tgz"
  integrity sha1-bVWGOlH6dXqyTonZNxRlByqnvDU=
  dependencies:
    hash-sum "^1.0.2"
    loader-utils "^1.0.2"

vue-template-es2015-compiler@^1.9.0:
  version "1.9.1"
  resolved "https://registry.nlark.com/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz"
  integrity sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU=

vue@^3.0.0:
  version "3.2.26"
  resolved "https://registry.npmmirror.com/vue/download/vue-3.2.26.tgz"
  integrity sha512-KD4lULmskL5cCsEkfhERVRIOEDrfEL9CwAsLYpzptOGjaGFNWo3BQ9g8MAb7RaIO71rmVOziZ/uEN/rHwcUIhg==
  dependencies:
    "@vue/compiler-dom" "3.2.26"
    "@vue/compiler-sfc" "3.2.26"
    "@vue/runtime-dom" "3.2.26"
    "@vue/server-renderer" "3.2.26"
    "@vue/shared" "3.2.26"

vuex@^4.0.0-0:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/vuex/download/vuex-4.0.2.tgz"
  integrity sha512-M6r8uxELjZIK8kTKDGgZTYX/ahzblnzC4isU1tpmEuOIIKmV+TRdc+H4s8ds2NuZ7wpUTdGRzJRtoj+lI+pc0Q==
  dependencies:
    "@vue/devtools-api" "^6.0.0-beta.11"

watchpack-chokidar2@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/watchpack-chokidar2/download/watchpack-chokidar2-2.0.1.tgz"
  integrity sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc=
  dependencies:
    chokidar "^2.1.8"

watchpack@^1.7.4:
  version "1.7.5"
  resolved "https://registry.npmmirror.com/watchpack/download/watchpack-1.7.5.tgz"
  integrity sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM=
  dependencies:
    graceful-fs "^4.1.2"
    neo-async "^2.5.0"
  optionalDependencies:
    chokidar "^3.4.1"
    watchpack-chokidar2 "^2.0.1"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://registry.nlark.com/wbuf/download/wbuf-1.7.3.tgz"
  integrity sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=
  dependencies:
    minimalistic-assert "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/wcwidth/download/wcwidth-1.0.1.tgz"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webpack-bundle-analyzer@^3.8.0:
  version "3.9.0"
  resolved "https://registry.npmmirror.com/webpack-bundle-analyzer/download/webpack-bundle-analyzer-3.9.0.tgz?cache=0&sync_timestamp=1634019921368&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwebpack-bundle-analyzer%2Fdownload%2Fwebpack-bundle-analyzer-3.9.0.tgz"
  integrity sha1-9vlNsQj7V05BWtMT3kGicH0z7zw=
  dependencies:
    acorn "^7.1.1"
    acorn-walk "^7.1.1"
    bfj "^6.1.1"
    chalk "^2.4.1"
    commander "^2.18.0"
    ejs "^2.6.1"
    express "^4.16.3"
    filesize "^3.6.1"
    gzip-size "^5.0.0"
    lodash "^4.17.19"
    mkdirp "^0.5.1"
    opener "^1.5.1"
    ws "^6.0.0"

webpack-chain@^6.4.0:
  version "6.5.1"
  resolved "https://registry.nlark.com/webpack-chain/download/webpack-chain-6.5.1.tgz?cache=0&sync_timestamp=1624608012957&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwebpack-chain%2Fdownload%2Fwebpack-chain-6.5.1.tgz"
  integrity sha1-TycoTLu2N+PI+970Pu9YjU2GEgY=
  dependencies:
    deepmerge "^1.5.2"
    javascript-stringify "^2.0.1"

webpack-dev-middleware@^3.7.2:
  version "3.7.3"
  resolved "https://registry.npmmirror.com/webpack-dev-middleware/download/webpack-dev-middleware-3.7.3.tgz"
  integrity sha1-Bjk3KxQyYuK4SrldO5GnWXBhwsU=
  dependencies:
    memory-fs "^0.4.1"
    mime "^2.4.4"
    mkdirp "^0.5.1"
    range-parser "^1.2.1"
    webpack-log "^2.0.0"

webpack-dev-server@^3.11.0:
  version "3.11.3"
  resolved "https://registry.npmmirror.com/webpack-dev-server/download/webpack-dev-server-3.11.3.tgz"
  integrity sha512-3x31rjbEQWKMNzacUZRE6wXvUFuGpH7vr0lIEbYpMAG9BOxi0928QU1BBswOAP3kg3H1O4hiS+sq4YyAn6ANnA==
  dependencies:
    ansi-html-community "0.0.8"
    bonjour "^3.5.0"
    chokidar "^2.1.8"
    compression "^1.7.4"
    connect-history-api-fallback "^1.6.0"
    debug "^4.1.1"
    del "^4.1.1"
    express "^4.17.1"
    html-entities "^1.3.1"
    http-proxy-middleware "0.19.1"
    import-local "^2.0.0"
    internal-ip "^4.3.0"
    ip "^1.1.5"
    is-absolute-url "^3.0.3"
    killable "^1.0.1"
    loglevel "^1.6.8"
    opn "^5.5.0"
    p-retry "^3.0.1"
    portfinder "^1.0.26"
    schema-utils "^1.0.0"
    selfsigned "^1.10.8"
    semver "^6.3.0"
    serve-index "^1.9.1"
    sockjs "^0.3.21"
    sockjs-client "^1.5.0"
    spdy "^4.0.2"
    strip-ansi "^3.0.1"
    supports-color "^6.1.0"
    url "^0.11.0"
    webpack-dev-middleware "^3.7.2"
    webpack-log "^2.0.0"
    ws "^6.2.1"
    yargs "^13.3.2"

webpack-log@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/webpack-log/download/webpack-log-2.0.0.tgz"
  integrity sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8=
  dependencies:
    ansi-colors "^3.0.0"
    uuid "^3.3.2"

webpack-merge@^4.2.2:
  version "4.2.2"
  resolved "https://registry.nlark.com/webpack-merge/download/webpack-merge-4.2.2.tgz?cache=0&sync_timestamp=1624607912484&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwebpack-merge%2Fdownload%2Fwebpack-merge-4.2.2.tgz"
  integrity sha1-onxS6ng9E5iv0gh/VH17nS9DY00=
  dependencies:
    lodash "^4.17.15"

webpack-sources@^1.1.0, webpack-sources@^1.4.0, webpack-sources@^1.4.1:
  version "1.4.3"
  resolved "https://registry.npmmirror.com/webpack-sources/download/webpack-sources-1.4.3.tgz?cache=0&sync_timestamp=1636982763649&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwebpack-sources%2Fdownload%2Fwebpack-sources-1.4.3.tgz"
  integrity sha1-7t2OwLko+/HL/plOItLYkPMwqTM=
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack@^4.0.0:
  version "4.46.0"
  resolved "https://registry.npmmirror.com/webpack/download/webpack-4.46.0.tgz"
  integrity sha512-6jJuJjg8znb/xRItk7bkT0+Q7AHCYjjFnvKIWQPkNIOyRqoCGvkOs0ipeQzrqz4l5FtN5ZI/ukEHroeX/o1/5Q==
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    acorn "^6.4.1"
    ajv "^6.10.2"
    ajv-keywords "^3.4.1"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^4.5.0"
    eslint-scope "^4.0.3"
    json-parse-better-errors "^1.0.2"
    loader-runner "^2.4.0"
    loader-utils "^1.2.3"
    memory-fs "^0.4.1"
    micromatch "^3.1.10"
    mkdirp "^0.5.3"
    neo-async "^2.6.1"
    node-libs-browser "^2.2.1"
    schema-utils "^1.0.0"
    tapable "^1.1.3"
    terser-webpack-plugin "^1.4.3"
    watchpack "^1.7.4"
    webpack-sources "^1.4.1"

websocket-driver@>=0.5.1, websocket-driver@^0.7.4:
  version "0.7.4"
  resolved "https://registry.nlark.com/websocket-driver/download/websocket-driver-0.7.4.tgz"
  integrity sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://registry.nlark.com/websocket-extensions/download/websocket-extensions-0.1.4.tgz"
  integrity sha1-f4RzvIOd/YdgituV1+sHUhFXikI=

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz"
  integrity sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-module@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/which-module/download/which-module-2.0.0.tgz"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which@1, which@^1.2.9:
  version "1.3.1"
  resolved "https://registry.nlark.com/which/download/which-1.3.1.tgz"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.nlark.com/which/download/which-2.0.2.tgz"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.0:
  version "1.1.5"
  resolved "https://registry.npmmirror.com/wide-align/download/wide-align-1.1.5.tgz?cache=0&sync_timestamp=1634307502489&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwide-align%2Fdownload%2Fwide-align-1.1.5.tgz"
  integrity sha1-3x1MIGhUNp7PPJpImPGyP72dFdM=
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

word-wrap@~1.2.3:
  version "1.2.3"
  resolved "https://registry.nlark.com/word-wrap/download/word-wrap-1.2.3.tgz"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

worker-farm@^1.7.0:
  version "1.7.0"
  resolved "https://registry.nlark.com/worker-farm/download/worker-farm-1.7.0.tgz?cache=0&sync_timestamp=1624608024341&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fworker-farm%2Fdownload%2Fworker-farm-1.7.0.tgz"
  integrity sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=
  dependencies:
    errno "~0.1.7"

worker-rpc@^0.1.0:
  version "0.1.1"
  resolved "https://registry.nlark.com/worker-rpc/download/worker-rpc-0.1.1.tgz"
  integrity sha1-y1Zb1tcHGo8WZgaGBR6WmtMvVNU=
  dependencies:
    microevent.ts "~0.1.1"

wrap-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.nlark.com/wrap-ansi/download/wrap-ansi-3.0.1.tgz"
  integrity sha1-KIoE2H7aXChuBg3+jxNc6NAH+Lo=
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "https://registry.nlark.com/wrap-ansi/download/wrap-ansi-5.1.0.tgz"
  integrity sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://registry.nlark.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.nlark.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.nlark.com/wrappy/download/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write@1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/write/download/write-1.0.3.tgz"
  integrity sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=
  dependencies:
    mkdirp "^0.5.1"

ws@^6.0.0, ws@^6.2.1:
  version "6.2.2"
  resolved "https://registry.npmmirror.com/ws/download/ws-6.2.2.tgz"
  integrity sha512-zmhltoSR8u1cnDsD43TX59mzoMZsLKqUweyYBAIvTngR3shc0W6aOZylZmq/7hqyVxPdi+5Ud2QInblgyE72fw==
  dependencies:
    async-limiter "~1.0.0"

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "https://registry.nlark.com/xtend/download/xtend-4.0.2.tgz"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^4.0.0:
  version "4.0.3"
  resolved "https://registry.nlark.com/y18n/download/y18n-4.0.3.tgz"
  integrity sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://registry.nlark.com/y18n/download/y18n-5.0.8.tgz"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://registry.nlark.com/yallist/download/yallist-2.1.2.tgz?cache=0&sync_timestamp=1624607893982&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fyallist%2Fdownload%2Fyallist-2.1.2.tgz"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry.nlark.com/yallist/download/yallist-3.1.1.tgz?cache=0&sync_timestamp=1624607893982&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fyallist%2Fdownload%2Fyallist-3.1.1.tgz"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/yallist/download/yallist-4.0.0.tgz?cache=0&sync_timestamp=1624607893982&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fyallist%2Fdownload%2Fyallist-4.0.0.tgz"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yaml@^1.7.2:
  version "1.10.2"
  resolved "https://registry.npmmirror.com/yaml/download/yaml-1.10.2.tgz?cache=0&sync_timestamp=1636797191994&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyaml%2Fdownload%2Fyaml-1.10.2.tgz"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yargs-parser@^13.1.2:
  version "13.1.2"
  resolved "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-13.1.2.tgz?cache=0&sync_timestamp=1637031074828&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-13.1.2.tgz"
  integrity sha1-Ew8JcC667vJlDVTObj5XBvek+zg=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-18.1.3.tgz"
  integrity sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^20.2.2:
  version "20.2.9"
  resolved "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-20.2.9.tgz?cache=0&sync_timestamp=1637031074828&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-20.2.9.tgz"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs@^13.3.2:
  version "13.3.2"
  resolved "https://registry.npmmirror.com/yargs/download/yargs-13.3.2.tgz"
  integrity sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.2"

yargs@^15.3.1:
  version "15.4.1"
  resolved "https://registry.npmmirror.com/yargs/-/yargs-15.4.1.tgz"
  integrity sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yargs@^16.0.0:
  version "16.2.0"
  resolved "https://registry.npmmirror.com/yargs/download/yargs-16.2.0.tgz"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yorkie@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/yorkie/download/yorkie-2.0.0.tgz"
  integrity sha1-kkEZEtQ1IU4SxRwq4Qk+VLa7g9k=
  dependencies:
    execa "^0.8.0"
    is-ci "^1.0.10"
    normalize-path "^1.0.0"
    strip-indent "^2.0.0"

zip-stream@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/zip-stream/-/zip-stream-4.1.0.tgz"
  integrity sha512-zshzwQW7gG7hjpBlgeQP9RuyPGNxvJdzR8SUM3QhxCnLjWN2E7j3dOvpeDcQoETfHx0urRS7EtmVToql7YpU4A==
  dependencies:
    archiver-utils "^2.1.0"
    compress-commons "^4.1.0"
    readable-stream "^3.6.0"

zrender@5.2.1:
  version "5.2.1"
  resolved "https://registry.npmmirror.com/zrender/-/zrender-5.2.1.tgz"
  integrity sha512-M3bPGZuyLTNBC6LiNKXJwSCtglMp8XUEqEBG+2MdICDI3d1s500Y4P0CzldQGsqpRVB7fkvf3BKQQRxsEaTlsw==
  dependencies:
    tslib "2.3.0"
