OpenVPN 管理接口说明
----------------------------------

OpenVPN 管理接口允许通过 TCP 或 unix 域套接字从外部程序对 OpenVPN 进行管理控制。

该接口专门为希望以编程方式或远程控制 OpenVPN 守护进程的开发人员设计，可在 OpenVPN 作为客户端或服务器运行时使用。

管理接口使用客户端/服务器 TCP 连接或 unix 域套接字实现，OpenVPN 将在提供的 IP 地址和端口上监听传入的管理接口客户端连接。

管理协议目前是明文的，没有显式的安全层。因此，建议管理接口监听 unix 域套接字、localhost (127.0.0.1) 或本地 VPN 地址。可以通过 VPN 本身远程连接到管理接口，但在此模式下某些功能将受到限制，例如提供私钥密码的能力。

在 OpenVPN 配置文件中使用以下指令启用管理接口：

--management

有关此指令和相关指令的文档，请参阅手册页。

一旦 OpenVPN 启动并启用了管理层，您可以 telnet 到管理端口（确保使用理解"原始"模式的 telnet 客户端）。

连接到管理端口后，您可以使用 "help" 命令列出所有命令。

命令 -- bytecount
--------------------

bytecount 命令用于请求 OpenVPN 带宽使用的实时通知。

命令语法：

  bytecount n (其中 n > 0) -- 设置每 n 秒自动通知带宽使用情况
  bytecount 0 -- 关闭 bytecount 通知

如果 OpenVPN 作为客户端运行，bytecount 通知将如下所示：

  >BYTECOUNT:{BYTES_IN},{BYTES_OUT}

BYTES_IN 是从服务器接收的字节数，BYTES_OUT 是发送到服务器的字节数。

如果 OpenVPN 作为服务器运行，bytecount 通知将如下所示：

  >BYTECOUNT_CLI:{CID},{BYTES_IN},{BYTES_OUT}
 
CID 是客户端 ID，BYTES_IN 是从客户端接收的字节数，BYTES_OUT 是发送到客户端的字节数。

请注意，当在服务器上使用 bytecount 命令时，每个连接的客户端都会每 n 秒报告其带宽数据。

当客户端断开连接时，最终的带宽数据将放在 'bytes_received' 和 'bytes_sent' 环境变量中，包含在 >CLIENT:DISCONNECT 通知中。

命令 -- echo
---------------

echo 功能用于允许将 GUI 特定参数嵌入到 OpenVPN 配置文件中或从服务器推送到 OpenVPN 客户端。

命令示例：

  echo on      -- 开启 echo 消息的实时通知
  echo all     -- 打印当前 echo 历史列表
  echo off     -- 关闭 echo 消息的实时通知
  echo on all  -- 原子性地启用实时通知，并显示历史缓冲区中的任何消息

例如，假设您正在开发一个 OpenVPN GUI，并且您希望给 OpenVPN 服务器提供要求 GUI 忘记任何已保存密码的能力。

在 OpenVPN 服务器配置文件中，添加：

  push "echo forget-passwords"

当 OpenVPN 客户端从服务器接收其拉取的指令列表时，"echo forget-passwords" 指令将在列表中，它将导致管理接口将 "forget-passwords" 字符串保存在其 echo 参数列表中。

管理接口客户端可以使用 "echo all" 输出完整的回显参数列表，"echo on" 开启通过 ">ECHO:" 前缀的回显参数实时通知，或 "echo off" 关闭实时通知。

当 GUI 连接到 OpenVPN 管理套接字时，它可以发出 "echo all" 命令，这将产生如下输出：

  1101519562,forget-passwords
  END

本质上，echo 命令允许我们将参数从 OpenVPN 服务器传递到 OpenVPN 客户端，然后传递到管理接口客户端（如 GUI）。大整数是接收 echo 参数时的 unix 日期/时间。

如果管理接口客户端发出了命令 "echo on"，它将启用 echo 参数的实时通知。在这种情况下，我们的 "forget-passwords" 消息将如下输出：

  >ECHO:1101519562,forget-passwords

与 log 命令一样，echo 命令可以原子性地显示历史记录，同时激活实时更新：

  echo on all

echo 缓冲区的大小目前硬编码为 100 条消息。


一般来说，OpenVPN 核心根本不理解 echo 消息（因此协作的 GUI 和服务器可以使用此机制进行任意信息传输）。

话虽如此，社区维护的 OpenVPN Windows GUI 和 MacOS 的 Tunnelblick 之间已经商定了一些 echo 命令，这些命令的文档可以在 doc/gui-notes.txt 中找到。


命令 -- exit, quit
---------------------

关闭管理会话，并恢复在管理端口上监听来自其他客户端的连接。目前，OpenVPN 守护进程最多只能同时支持一个管理接口客户端。

命令 -- help
---------------

打印命令摘要。

命令 -- hold
---------------

hold 命令可用于操作保持标志，或从保持状态释放 OpenVPN。

如果在初始启动或重启时设置了保持标志，OpenVPN 将在初始化隧道之前休眠，直到管理接口接收到 "hold release" 命令。

OpenVPN 的 --management-hold 指令可用于在设置保持标志的情况下启动 OpenVPN。

保持标志设置是持久的，不会被重启重置。

OpenVPN 将通过向管理接口客户端发送实时通知来指示它处于保持状态，参数指示 OpenVPN 在没有 UI 的情况下会等待多长时间（受连接重试指数退避影响）。如果 UI 想要类似的行为，需要等待释放保持：

  >HOLD:Waiting for hold release:10

命令示例：

  hold         -- 显示当前保持标志，0=关闭，1=开启。
  hold on      -- 开启保持标志，以便将来的重启会保持。
  hold off     -- 关闭保持标志，以便将来的重启不会保持。
  hold release -- 离开保持状态并启动 OpenVPN，但不改变当前保持标志设置。

命令 -- kill
---------------

在服务器模式下，杀死特定的客户端实例。

命令示例：

  kill Test-Client -- 杀死具有通用名称 "Test-Client" 的客户端实例。
  kill tcp:*******:4000 -- 杀死具有源地址、端口和协议为 tcp:*******:4000 的客户端实例

  请注意，按地址杀死对 IPv6 连接的客户端尚不起作用，因此请依赖按 CN 或 CID 杀死。

使用 "status" 命令查看哪些客户端已连接。

命令 -- log
--------------

显示 OpenVPN 日志文件。管理接口只缓存日志文件的最近 n 行，其中 n 由 OpenVPN --management-log-cache 指令控制。

命令示例：

  log on     -- 启用日志消息的实时输出。
  log all    -- 显示当前缓存的日志文件历史记录。
  log on all -- 原子性地显示所有当前缓存的日志文件历史记录，然后启用新日志文件消息的实时通知。
  log off    -- 关闭日志消息的实时通知。
  log 20     -- 显示日志文件历史记录的最近 20 行。

实时通知格式：

实时日志消息以 ">LOG:" 前缀开头，后跟以下逗号分隔的字段：

  (a) unix 整数日期/时间，
  (b) 单个字符串中的零个或多个消息标志：
      I -- 信息性
      F -- 致命错误
      N -- 非致命错误
      W -- 警告
      D -- 调试，以及
  (c) 消息文本。

命令 -- mute
---------------

更改 OpenVPN --mute 参数。mute 参数用于静音相同消息类别的重复消息。

命令示例：

  mute 40 -- 将 mute 参数更改为 40
  mute    -- 显示当前 mute 设置

命令 -- net
--------------

（仅限 Windows）产生等效于 OpenVPN --show-net 指令的输出。输出包括 OpenVPN 对系统网络适配器列表和路由表的视图，基于 Windows IP 助手 API 返回的信息。

命令 -- pid
--------------

显示当前 OpenVPN 进程的进程 ID。

命令 -- password 和 username
--------------------------------

  password 命令用于向 OpenVPN 传递密码。

  如果 OpenVPN 使用 --management-query-passwords 指令运行，它将向管理接口查询 RSA 私钥密码和 --auth-user-pass 用户名/密码。

  当 OpenVPN 需要来自管理接口的密码时，它将产生实时 ">PASSWORD:" 消息。

  示例 1：

    >PASSWORD:Need 'Private Key' password

  OpenVPN 指示它需要 "Private Key" 类型的密码。

  管理接口客户端应如下响应：

    password "Private Key" foo

  示例 2：

    >PASSWORD:Need 'Auth' username/password

  OpenVPN 需要 --auth-user-pass 用户名和密码。管理接口客户端应响应：

    username "Auth" foo
    password "Auth" bar

  用户名/密码本身可以用引号括起来，特殊字符如双引号或反斜杠必须转义，例如，

    password "Private Key" "foo\"bar"

  转义规则与配置文件相同。有关更多信息，请参阅下面的"命令解析"部分。

  PASSWORD 实时消息类型也可用于指示密码或其他类型的身份验证失败：

  示例 3：私钥密码不正确，OpenVPN 正在退出：

    >PASSWORD:Verification Failed: 'Private Key'

  示例 4：--auth-user-pass 用户名/密码失败，如果 '--auth-retry none'（这是默认值）生效，OpenVPN 将以致命错误退出：

    >PASSWORD:Verification Failed: 'Auth'

  示例 5：--auth-user-pass 用户名/密码失败，服务器使用 client-deny 服务器端管理接口命令提供了自定义客户端原因文本字符串。

    >PASSWORD:Verification Failed: 'custom server-generated string'

  示例 6：如果服务器向客户端推送 --auth-token，OpenVPN 将产生实时 PASSWORD 消息：

    >PASSWORD:Auth-Token:foobar

  示例 7：静态挑战/响应：

    >PASSWORD:Need 'Auth' username/password SC:1,Please enter token PIN

  OpenVPN 需要 --auth-user-pass 用户名和密码以及对挑战的响应。应获取用户对 "Please enter token PIN" 的响应，并将其包含在管理接口客户端的响应中，与用户名和密码一起，按照下面挑战/响应协议部分中描述的格式。

  示例 8：动态挑战/响应：

    >PASSWORD:Verification Failed: ['CRV1:R,E:Om01u7Fh4LrGBS7uh0SWmzwabUiGiW6l:Y3Ix:Please enter token PIN']

  之前的 --auth-user-pass 用户名/密码失败或不完整，服务器提供了自定义客户端原因文本字符串，指示下次看到 "Need 'Auth' username/password" 消息时应进行动态挑战/响应。

  当下次看到没有静态挑战的 "Need 'Auth' username/password" 时，应获取用户对 "Please enter token PIN" 的响应，并将其包含在管理接口客户端的响应中，与用户名和密码一起，按照下面挑战/响应协议部分中描述的格式

有关示例 7 和 8 的更多详细信息，包括管理接口客户端应如何响应，请参阅下面的"挑战/响应协议"部分。
