# OpenVPN 管理接口说明

## 概述

OpenVPN 管理接口允许通过 TCP 或 unix 域套接字从外部程序对 OpenVPN 进行管理控制。

该接口专门为那些想以编程或远程方式控制 OpenVPN 守护进程的开发人员设计，可以在 OpenVPN 作为客户端或服务器运行时使用。

管理接口使用客户端/服务器 TCP 连接或 unix 域套接字实现，OpenVPN 将在提供的 IP 地址和端口上侦听传入的管理客户端连接。

管理协议目前是明文的，没有明确的安全层。因此，建议管理接口侦听 unix 域套接字、本地主机 (127.0.0.1) 或本地 VPN 地址。可以通过 VPN 本身远程连接到管理接口，但在此模式下，某些功能将受到限制，例如提供私钥密码的能力。

在 OpenVPN 配置文件中使用以下指令启用管理接口：

```
--management
```

有关此及相关指令的文档，请参阅 man 手册。

一旦启用了管理层，就可以使用 telnet 连接到管理端口 (确保使用的 telnet 客户端能够理解"原始"模式)。

连接到管理端口后，可以使用"help"命令列出所有命令。

## 命令

### COMMAND -- bytecount

bytecount 命令用于请求实时通知 OpenVPN 带宽使用情况。

命令语法：

```
bytecount n (where n > 0) -- 设置每 n 秒自动通知带宽使用情况
bytecount 0 -- 关闭 bytecount 通知
```

如果 OpenVPN 作为客户端运行，则 bytecount 通知如下所示：

```
>BYTECOUNT:{BYTES_IN},{BYTES_OUT}
```

BYTES_IN 是从服务器接收的字节数，BYTES_OUT 是发送到服务器的字节数。

如果 OpenVPN 作为服务器运行，则 bytecount 通知如下所示：

```
>BYTECOUNT_CLI:{CID},{BYTES_IN},{BYTES_OUT}
```

CID 是客户端 ID，BYTES_IN 是从客户端接收的字节数，BYTES_OUT 是发送到客户端的字节数。

请注意，当在服务器上使用 bytecount 命令时，每个连接的客户端将每 n 秒报告其带宽数字。

当客户端断开连接时，最终的带宽数字将放在"bytes_received"和"bytes_sent"环境变量中，如 >CLIENT:DISCONNECT 通知中所包含的那样。

### COMMAND -- echo

echo 功能用于允许将 GUI 特定参数嵌入到 OpenVPN 配置文件中，或从服务器推送到 OpenVPN 客户端。

命令示例：

```
echo on      -- 打开实时通知
echo all     -- 打印当前的 echo 历史列表
echo off     -- 关闭实时通知
echo on all  -- 原子地启用实时通知，以及显示历史缓冲区中的任何消息
```

例如，假设您正在开发一个 OpenVPN GUI，并且您希望 OpenVPN 服务器能够要求 GUI 忘记任何保存的密码。

在 OpenVPN 服务器配置文件中添加：

```
push "echo forget-passwords"
```

当 OpenVPN 客户端从服务器接收其拉取的指令列表时，"echo forget-passwords"指令将在列表中，并且它将导致管理接口将"forget-passwords"字符串保存在其 echo 参数列表中。

管理客户端可以使用"echo all"输出完整的回显参数列表，"echo on"通过">ECHO:"前缀打开回显参数的实时通知，或"echo off"关闭实时通知。

当 GUI 连接到 OpenVPN 管理套接字时，它可以发出"echo all"命令，这将产生如下输出：

```
1101519562,forget-passwords
END
```

本质上，echo 命令允许我们将参数从 OpenVPN 服务器传递到 OpenVPN 客户端，然后传递到管理客户端（例如 GUI）。大整数是接收回显参数时的 unix 日期/时间。

如果管理客户端发出了"echo on"命令，则会启用回显参数的实时通知。在这种情况下，我们的"forget-passwords"消息将如下所示：

```
>ECHO:1101519562,forget-passwords
```

与 log 命令一样，echo 命令可以在同时激活实时更新的同时原子地显示历史记录：

```
echo on all
```

回显缓冲区的大小当前为 100 条消息。

### COMMAND -- exit, quit

关闭 managment 会话，并继续侦听管理端口以接受来自其他客户端的连接。当前，OpenVPN 守护程序最多可以同时支持单个管理客户端。

### COMMAND -- help

打印命令摘要。

### COMMAND -- hold

hold 命令可用于操作 hold 标志，或从 hold 状态释放 OpenVPN。

如果在初始启动或重新启动时设置了 hold 标志，则 OpenVPN 将在初始化隧道之前休眠，直到管理接口接收到"hold release"命令。

OpenVPN 的 --management-hold 指令可用于启动设置了 hold 标志的 OpenVPN。

hold 标志设置是持久的，并且不会被重新启动重置。

OpenVPN 将通过向管理客户端发送实时通知来指示它处于保持状态：

```
>HOLD:Waiting for hold release
```

命令示例：

```
hold         -- 显示当前 hold 标志，0=关闭，1=打开。
hold on      -- 打开 hold 标志，以便将来的重新启动将保持。
hold off     -- 关闭 hold 标志，以便将来的重新启动不会保持。
hold release -- 退出保持状态并启动 OpenVPN，但不更改当前的 hold 标志设置。
```

### COMMAND -- kill

在服务器模式下，杀死特定的客户端实例。

命令示例：

```
kill Test-Client -- 杀死具有"Test-Client"通用名称的客户端实例。
kill 1.2.3.4:4000 -- 杀死具有源地址和端口为 1.2.3.4:4000 的客户端实例
```

使用"status"命令查看已连接的客户端。

### COMMAND -- log

显示 OpenVPN 日志文件。管理接口仅缓存日志文件的最近 n 行，其中 n 受 OpenVPN --management-log-cache 指令的控制。

命令示例：

```
log on     -- 启用日志消息的实时输出。
log all    -- 显示当前缓存的日志文件历史记录。
log on all -- 原子地显示当前缓存的所有日志文件历史记录，然后启用对新日志文件消息的实时通知。
log off    -- 关闭日志消息的实时通知。
log 20     -- 显示日志文件历史记录的最近 20 行。
```

实时通知格式：

实时日志消息以">LOG:"前缀开头，后跟以下逗号分隔的字段：

- unix 整数日期/时间
- 单个字符串中的零个或多个消息标志：
  - I -- 信息
  - F -- 致命错误
  - N -- 非致命错误
  - W -- 警告
  - D -- 调试
- 消息文本

### COMMAND -- mute

更改 OpenVPN --mute 参数。mute 参数用于消除相同消息类别的重复消息。

命令示例：

```
mute 40 -- 将 mute 参数更改为 40
mute    -- 显示当前的 mute 设置
```

### COMMAND -- net

（仅限 Windows）生成等效于 OpenVPN --show-net 指令的输出。输出包括 OpenVPN 对系统网络适配器列表和路由表的视图，该视图基于 Windows IP 帮助器 API 返回的信息。

### COMMAND -- pid

显示当前 OpenVPN 进程的进程 ID。

### COMMAND -- password and username

password 命令用于将密码传递给 OpenVPN。

如果使用 --management-query-passwords 指令运行 OpenVPN，则它将查询管理接口以获取 RSA 私钥密码和 --auth-user-pass 用户名/密码。

当 OpenVPN 需要来自管理接口的密码时，它将生成实时">PASSWORD:"消息。

示例 1：

```
>PASSWORD:Need 'Private Key' password
```

OpenVPN 表示它需要类型为"Private Key"的密码。

管理客户端应如下响应此查询：

```
password "Private Key" foo
```

示例 2：

```
>PASSWORD:Need 'Auth' username/password
```

OpenVPN 需要 --auth-user-pass 密码。管理客户端应该回应：

```
username "Auth" foo
password "Auth" bar
```

用户名/密码本身可以用引号括起来，必须转义特殊字符，例如双引号或反斜杠，例如：

```
password "Private Key" "foo\"bar"
```

转义规则与配置文件相同。有关更多信息，请参见下面的"命令解析"部分。

PASSWORD 实时消息类型也可用于指示密码或其他类型的身份验证失败：

示例 3：
私钥密码不正确，OpenVPN 正在退出：

```
>PASSWORD:Verification Failed: 'Private Key'
```

示例 4：
--auth-user-pass 用户名/密码失败，OpenVPN 正在退出：

```
>PASSWORD:Verification Failed: 'Auth'
```

示例 5：
--auth-user-pass 用户名/密码失败，并且服务器使用 client-deny 服务器端管理接口命令提供了自定义客户端原因文本字符串。

```
>PASSWORD:Verification Failed: 'custom server-generated string'
```

### COMMAND -- forget-passwords

forget-passwords 命令将导致守护程序在会话期间忘记输入的密码。

命令示例：

```
forget-passwords -- 忘记到目前为止输入的密码。
```

### COMMAND -- signal

signal 命令将向 OpenVPN 守护程序发送信号。信号可以是 SIGHUP、SIGTERM、SIGUSR1 或 SIGUSR2 中的一个。

命令示例：

```
signal SIGUSR1 -- 向守护程序发送 SIGUSR1 信号
```

### COMMAND -- state

显示当前的OpenVPN状态，显示状态历史记录，或启用实时通知状态更改。

这些是OpenVPN状态：

- CONNECTING    -- OpenVPN的初始状态。
- WAIT          -- （仅限客户端）等待服务器的初始响应。
- AUTH          -- （仅限客户端）与服务器进行身份验证。
- GET_CONFIG    -- （仅限客户端）从服务器下载配置选项。
- ASSIGN_IP     -- 将IP地址分配给虚拟网络接口。
- ADD_ROUTES    -- 将路由添加到系统。
- CONNECTED     -- 初始化序列已完成。
- RECONNECTING  -- 发生了重新启动。
- EXITING       -- 正在进行优雅的退出。

命令示例：

```
state        -- 打印当前 OpenVPN 状态。
state on     -- 启用状态更改的实时通知。
state off    -- 禁用状态更改的实时通知。
state all    -- 打印当前状态历史记录。
state 3      -- 打印最近的 3 个状态转换。
state on all -- 原子地显示状态历史记录，同时启用将来状态转换的实时状态通知。
```

输出格式由 4 个逗号分隔的参数组成：
- 整数unix日期/时间
- 状态名称
- 可选的描述字符串（主要用于 RECONNECTING 和 EXITING 以显示断开连接的原因）
- 可选的 TUN/TAP 本地 IP 地址（显示为 ASSIGN_IP 和 CONNECTED）
- 远程服务器的可选地址（OpenVPN 2.1 或更高版本）

实时状态通知将在其前面添加">STATE:"前缀。

### COMMAND -- status

以与 OpenVPN --status 指令生成的格式相同的格式显示当前的守护程序状态信息。

命令示例：

```
status   -- 使用默认状态格式版本显示状态信息。
status 3 -- 使用 --status-version 3 的格式显示状态信息。
```

### COMMAND -- username

请参阅上面的"password"部分。

### COMMAND -- verb

更改 OpenVPN --verb 参数。verb 参数控制输出详细程度，范围从 0（无输出）到 15（最大输出）。有关详细信息，请参阅 OpenVPN 手册页。
