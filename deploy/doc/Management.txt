OpenVPN Management Interface Notes
----------------------------------

The OpenVPN Management interface allows OpenVPN to be administratively controlled from an external program via a TCP or unix domain socket.
// OpenVPN 管理接口允许通过 TCP 或 unix 域套接字从外部程序对 OpenVPN 进行管理控制。

The interface has been specifically designed for developers who would like to programmatically or remotely control an OpenVPN daemon, and can be used when OpenVPN is running as a client or server.
// 该接口专门为那些想以编程或远程方式控制 OpenVPN 守护进程的开发人员设计，可以在 OpenVPN 作为客户端或服务器运行时使用。

The management interface is implemented using a client/server TCP connection or unix domain socket where OpenVPN will listen on a provided IP address and port for incoming management client connections.
// 管理接口使用客户端/服务器 TCP 连接或 unix 域套接字实现，OpenVPN 将在提供的 IP 地址和端口上侦听传入的管理客户端连接。

The management protocol is currently cleartext without an explicit security layer.
For this reason, it is recommended that the management interface either listen on a unix domain socket, localhost (127.0.0.1), or on the local VPN address.
It's possible to remotely connect to the management interface over the VPN itself, though some capabilities will be limited in this mode, such as the ability to provide private key passwords.
// 管理协议目前是明文的，没有明确的安全层。
// 因此，建议管理接口侦听 unix 域套接字、本地主机 (127.0.0.1) 或本地 VPN 地址。
// 可以通过 VPN 本身远程连接到管理接口，但在此模式下，某些功能将受到限制，例如提供私钥密码的能力。

The management interface is enabled in the OpenVPN configuration file using the following directive:
// 在 OpenVPN 配置文件中使用以下指令启用管理接口：

--management

See the man page for documentation on this and related directives.
// 有关此及相关指令的文档，请参阅 man 手册。

Once OpenVPN has started with the management layer enabled, you can telnet to the management port (make sure to use a telnet client which understands "raw" mode).
// 一旦启用了管理层，就可以使用 telnet 连接到管理端口 (确保使用的 telnet 客户端能够理解“原始”模式)。

Once connected to the management port, you can use the "help" command to list all commands.
// 连接到管理端口后，可以使用“help”命令列出所有命令。

COMMAND -- bytecount
--------------------

The bytecount command is used to request real-time notification of OpenVPN bandwidth usage.
// bytecount 命令用于请求实时通知 OpenVPN 带宽使用情况。

Command syntax:

  bytecount n (where n > 0) -- set up automatic notification of bandwidth usage once every n seconds
  // 设置每 n 秒自动通知带宽使用情况
  bytecount 0 -- turn off bytecount notifications
  // 关闭 bytecount 通知

If OpenVPN is running as a client, the bytecount notification will look like this:
// 如果 OpenVPN 作为客户端运行，则 bytecount 通知如下所示：

  >BYTECOUNT:{BYTES_IN},{BYTES_OUT}

BYTES_IN is the number of bytes that have been received from the server and BYTES_OUT is the number of bytes that have been sent to the server.
// BYTES_IN 是从服务器接收的字节数，BYTES_OUT 是发送到服务器的字节数。

If OpenVPN is running as a server, the bytecount notification will look like this:
// 如果 OpenVPN 作为服务器运行，则 bytecount 通知如下所示：

  >BYTECOUNT_CLI:{CID},{BYTES_IN},{BYTES_OUT}

CID is the Client ID, BYTES_IN is the number of bytes that have been received from the client and BYTES_OUT is the number of bytes that have been sent to the client.
// CID 是客户端 ID，BYTES_IN 是从客户端接收的字节数，BYTES_OUT 是发送到客户端的字节数。

Note that when the bytecount command is used on the server, every connected client will report its bandwidth numbers once every n seconds.
// 请注意，当在服务器上使用 bytecount 命令时，每个连接的客户端将每 n 秒报告其带宽数字。

When the client disconnects, the final bandwidth numbers will be placed in the 'bytes_received' and 'bytes_sent' environmental variables as included in the >CLIENT:DISCONNECT notification.
// 当客户端断开连接时，最终的带宽数字将放在“bytes_received”和“bytes_sent”环境变量中，如 >CLIENT:DISCONNECT 通知中所包含的那样。

COMMAND -- echo
---------------

The echo capability is used to allow GUI-specific parameters to be either embedded in the OpenVPN config file or pushed to an OpenVPN client from a server.
// echo 功能用于允许将 GUI 特定参数嵌入到 OpenVPN 配置文件中，或从服务器推送到 OpenVPN 客户端。

Command examples:

  echo on      -- turn on real-time notification of echo messages
  // 打开实时通知
  echo all     -- print the current echo history list
  // 打印当前的 echo 历史列表
  echo off     -- turn off real-time notification of echo messages
  // 关闭实时通知
  echo on all  -- atomically enable real-time notification, plus show any messages in history buffer
  // 原子地启用实时通知，以及显示历史缓冲区中的任何消息

For example, suppose you are developing a OpenVPN GUI and you want to give the OpenVPN server the ability to ask the GUI to forget any saved passwords.
// 例如，假设您正在开发一个 OpenVPN GUI，并且您希望 OpenVPN 服务器能够要求 GUI 忘记任何保存的密码。

In the OpenVPN server config file, add:
// 在 OpenVPN 服务器配置文件中添加：

  push "echo forget-passwords"

When the OpenVPN client receives its pulled list of directives from the server, the "echo forget-passwords" directive will be in the list, and it will cause the management interface to save the "forget-passwords" string in its list of echo parameters.
// 当 OpenVPN 客户端从服务器接收其拉取的指令列表时，“echo forget-passwords”指令将在列表中，并且它将导致管理接口将“forget-passwords”字符串保存在其 echo 参数列表中。

The management client can use "echo all" to output the full list of echoed parameters, "echo on" to turn on real-time notification of echoed parameters via the ">ECHO:" prefix, or "echo off" to turn off real-time notification.
// 管理客户端可以使用“echo all”输出完整的回显参数列表，“echo on”通过“>ECHO:”前缀打开回显参数的实时通知，或“echo off”关闭实时通知。

When the GUI connects to the OpenVPN management socket, it can issue an "echo all" command, which would produce output like this:
// 当 GUI 连接到 OpenVPN 管理套接字时，它可以发出“echo all”命令，这将产生如下输出：

  1101519562,forget-passwords
  END

Essentially the echo command allowed us to pass parameters from the OpenVPN server to the OpenVPN client, and then to the management client (such as a GUI).
The large integer is the unix date/time when the echo parameter was received.
// 本质上，echo 命令允许我们将参数从 OpenVPN 服务器传递到 OpenVPN 客户端，然后传递到管理客户端（例如 GUI）。
// 大整数是接收回显参数时的 unix 日期/时间。

If the management client had issued the command "echo on", it would have enabled real-time notifications of echo parameters.
In this case, our "forget-passwords" message would be output like this:
// 如果管理客户端发出了“echo on”命令，则会启用回显参数的实时通知。
// 在这种情况下，我们的“forget-passwords”消息将如下所示：

  >ECHO:1101519562,forget-passwords

Like the log command, the echo command can atomically show history while simultaneously activating real-time updates:
// 与 log 命令一样，echo 命令可以在同时激活实时更新的同时原子地显示历史记录：

  echo on all

The size of the echo buffer is currently hardcoded to 100 messages.
// 回显缓冲区的大小当前为 100 条消息。

COMMAND -- exit, quit
---------------------

Close the managment session, and resume listening on the management port for connections from other clients.
Currently, the OpenVPN daemon can at most support a single management client any one time.
// 关闭 managment 会话，并继续侦听管理端口以接受来自其他客户端的连接。
// 当前，OpenVPN 守护程序最多可以同时支持单个管理客户端。

COMMAND -- help
---------------

Print a summary of commands.
// 打印命令摘要。

COMMAND -- hold
---------------

The hold command can be used to manipulate the hold flag, or release OpenVPN from a hold state.
// hold 命令可用于操作 hold 标志，或从 hold 状态释放 OpenVPN。

If the hold flag is set on initial startup or restart, OpenVPN will hibernate prior to initializing the tunnel until the management interface receives a "hold release" command.
// 如果在初始启动或重新启动时设置了 hold 标志，则 OpenVPN 将在初始化隧道之前休眠，直到管理接口接收到“hold release”命令。

The --management-hold directive of OpenVPN can be used to start OpenVPN with the hold flag set.
// OpenVPN 的 --management-hold 指令可用于启动设置了 hold 标志的 OpenVPN。

The hold flag setting is persistent and will not be reset by restarts.
// hold 标志设置是持久的，并且不会被重新启动重置。

OpenVPN will indicate that it is in a hold state by sending a real-time notification to the management client:
// OpenVPN 将通过向管理客户端发送实时通知来指示它处于保持状态：

  >HOLD:Waiting for hold release

Command examples:

  hold         -- show current hold flag, 0=off, 1=on.
  // 显示当前 hold 标志，0=关闭，1=打开。
  hold on      -- turn on hold flag so that future restarts will hold.
  // 打开 hold 标志，以便将来的重新启动将保持。
  hold off     -- turn off hold flag so that future restarts will not hold.
  // 关闭 hold 标志，以便将来的重新启动不会保持。
  hold release -- leave hold state and start OpenVPN, but do not alter the current hold flag setting.
  // 退出保持状态并启动 OpenVPN，但不更改当前的 hold 标志设置。

COMMAND -- kill
---------------

In server mode, kill a particlar client instance.
// 在服务器模式下，杀死特定的客户端实例。

Command examples:

  kill Test-Client -- kill the client instance having a common name of "Test-Client".
  // 杀死具有“Test-Client”通用名称的客户端实例。
  kill *******:4000 -- kill the client instance having a source address and port of *******:4000
  // 杀死具有源地址和端口

Use the "status" command to see which clients are connected.
// 使用“status”命令查看已连接的客户端。

COMMAND -- log
--------------

Show the OpenVPN log file.
Only the most recent n lines of the log file are cached by the management interface, where n is controlled by the OpenVPN --management-log-cache directive.
// 显示 OpenVPN 日志文件。
// 管理接口仅缓存日志文件的最近 n 行，其中 n 受 OpenVPN --management-log-cache 指令的控制。

Command examples:

  log on     -- Enable real-time output of log messages.
  // 启用日志消息的实时输出。
  log all    -- Show currently cached log file history.
  // 显示当前缓存的日志文件历史记录。
  log on all -- Atomically show all currently cached log file history then enable real-time notification of new log file messages.
  // 原子地显示当前缓存的所有日志文件历史记录，然后启用对新日志文件消息的实时通知。
  log off    -- Turn off real-time notification of log messages.
  // 关闭日志消息的实时通知。
  log 20     -- Show the most recent 20 lines of log file history.
  // 显示日志文件历史记录的最近 20 行。

Real-time notification format:
// 实时通知格式：

Real-time log messages begin with the ">LOG:" prefix followed by the following comma-separated fields:
// 实时日志消息以“>LOG:”前缀开头，后跟以下逗号分隔的字段：

  (a) unix integer date/time,
  // unix 整数日期/时间，
  (b) zero or more message flags in a single string:
  // 单个字符串中的零个或多个消息标志：
      I -- informational    // 信息
      F -- fatal error      // 致命错误
      N -- non-fatal error  // 非致命错误
      W -- warning          // 警告
      D -- debug, and       // 调试，和
  (c) message text.
  // 消息文本。

COMMAND -- mute
---------------

Change the OpenVPN --mute parameter.
The mute parameter is used to silence repeating messages of the same message category.
// 更改 OpenVPN --mute 参数。
// mute 参数用于消除相同消息类别的重复消息。

Command examples:

  mute 40 -- change the mute parameter to 40
    // 将 mute 参数更改为 40
  mute    -- show the current mute setting
    // 显示当前的 mute 设置

COMMAND -- net
--------------

(Windows Only) Produce output equivalent to the OpenVPN --show-net directive.
The output includes OpenVPN's view of the system network adapter list and routing table based on information returned by the Windows IP helper API.
// （仅限 Windows）生成等效于 OpenVPN --show-net 指令的输出。
// 输出包括 OpenVPN 对系统网络适配器列表和路由表的视图，该视图基于 Windows IP 帮助器 API 返回的信息。

COMMAND -- pid
--------------

Shows the process ID of the current OpenVPN process.
// 显示当前 OpenVPN 进程的进程 ID。

COMMAND -- password and username
--------------------------------

  The password command is used to pass passwords to OpenVPN.
  // password 命令用于将密码传递给 OpenVPN。

  If OpenVPN is run with the --management-query-passwords directive, it will query the management interface for RSA private key passwords and the --auth-user-pass username/password.
  // 如果使用 --management-query-passwords 指令运行 OpenVPN，则它将查询管理接口以获取 RSA 私钥密码和 --auth-user-pass 用户名/密码。

  When OpenVPN needs a password from the management interface, it will produce a real-time ">PASSWORD:" message.
  // 当 OpenVPN 需要来自管理接口的密码时，它将生成实时“>PASSWORD:”消息。

  Example 1:

    >PASSWORD:Need 'Private Key' password

  OpenVPN is indicating that it needs a password of type "Private Key".
  // OpenVPN 表示它需要类型为“Private Key”的密码。

  The management client should respond to this query as follows:
  // 管理客户端应如下响应此查询：

    password "Private Key" foo

  Example 2:

    >PASSWORD:Need 'Auth' username/password

  OpenVPN needs a --auth-user-pass password.  The management client should respond:
  // OpenVPN 需要 --auth-user-pass 密码。管理客户端应该回应：

    username "Auth" foo
    password "Auth" bar

  The username/password itself can be in quotes, and special characters such as double quote or backslash must be escaped, for example,
  // 用户名/密码本身可以用引号括起来，必须转义特殊字符，例如双引号或反斜杠，

    password "Private Key" "foo\"bar"

  The escaping rules are the same as for the config file. See the "Command Parsing" section below for more info.
  // 转义规则与配置文件相同。有关更多信息，请参见下面的“命令解析”部分。

  The PASSWORD real-time message type can also be used to indicate password or other types of authentication failure:
  // PASSWORD 实时消息类型也可用于指示密码或其他类型的身份验证失败：

  Example 3:
  The private key password is incorrect and OpenVPN is exiting:
  // 私钥密码不正确，OpenVPN 正在退出：

    >PASSWORD:Verification Failed: 'Private Key'

  Example 4:
  The --auth-user-pass username/password failed, and OpenVPN is exiting:
  // --auth-user-pass 用户名/密码失败，OpenVPN 正在退出：

    >PASSWORD:Verification Failed: 'Auth'

  Example 5:
  The --auth-user-pass username/password failed, and the server provided a custom client-reason-text string using the client-deny server-side management interface command.
  // --auth-user-pass 用户名/密码失败，并且服务器使用 client-deny 服务器端管理接口命令提供了自定义客户端原因文本字符串。

    >PASSWORD:Verification Failed: 'custom server-generated string'

COMMAND -- forget-passwords
---------------------------

The forget-passwords command will cause the daemon to forget passwords entered during the session.
// forget-passwords 命令将导致守护程序在会话期间忘记输入的密码。

Command example:

  forget-passwords -- forget passwords entered so far. -- 忘记到目前为止输入的密码。

COMMAND -- signal
-----------------

The signal command will send a signal to the OpenVPN daemon.
The signal can be one of SIGHUP, SIGTERM, SIGUSR1, or SIGUSR2.
// signal 命令将向 OpenVPN 守护程序发送信号。
// 信号可以是 SIGHUP、SIGTERM、SIGUSR1 或 SIGUSR2 中的一个。

Command example:

  signal SIGUSR1 -- send a SIGUSR1 signal to daemon -- 向守护程序发送 SIGUSR1 信号

COMMAND -- state
----------------

Show the current OpenVPN state, show state history, or enable real-time notification of state changes.
// 显示当前的OpenVPN状态，显示状态历史记录，或启用实时通知状态更改。

These are the OpenVPN states:
// 这些是OpenVPN状态：

CONNECTING    -- OpenVPN's initial state.                                       -- OpenVPN的初始状态。
WAIT          -- (Client only) Waiting for initial response from server.        -- （仅限客户端）等待服务器的初始响应。
AUTH          -- (Client only) Authenticating with server.                      -- （仅限客户端）与服务器进行身份验证。
GET_CONFIG    -- (Client only) Downloading configuration options from server.   -- （仅限客户端）从服务器下载配置选项。
ASSIGN_IP     -- Assigning IP address to virtual network interface.             -- 将IP地址分配给虚拟网络接口。
ADD_ROUTES    -- Adding routes to system.                                       -- 将路由添加到系统。
CONNECTED     -- Initialization Sequence Completed.                             -- 初始化序列已完成。
RECONNECTING  -- A restart has occurred.                                        -- 发生了重新启动。
EXITING       -- A graceful exit is in progress.                                -- 正在进行优雅的退出。

Command examples:

  state        -- Print current OpenVPN state.                                  -- 打印当前 OpenVPN 状态。
  state on     -- Enable real-time notification of state changes.               -- 启用状态更改的实时通知。
  state off    -- Disable real-time notification of state changes.              -- 禁用状态更改的实时通知。
  state all    -- Print current state history.                                  -- 打印当前状态历史记录。
  state 3      -- Print the 3 most recent state transitions.                    -- 打印最近的 3 个状态转换。
  state on all -- Atomically show state history while at the same time enable real-time state notification of future state transitions.
                                                                                -- 原子地显示状态历史记录，同时启用将来状态转换的实时状态通知。

The output format consists of 4 comma-separated parameters:
  (a) the integer unix date/time,
  (b) the state name,
  (c) optional descriptive string (used mostly on RECONNECTING and EXITING to show the reason for the disconnect),
  (d) optional TUN/TAP local IP address (shown for ASSIGN_IP and CONNECTED), and
  (e) optional address of remote server (OpenVPN 2.1 or higher).
// 输出格式由 4 个逗号分隔的参数组成：
// （a）整数unix日期/时间，
// （b）状态名称，
// （c）可选的描述字符串（主要用于 RECONNECTING 和 EXITING 以显示断开连接的原因），
// （d）可选的 TUN/TAP 本地 IP 地址（显示为 ASSIGN_IP 和 CONNECTED），以及
// （e）远程服务器的可选地址（OpenVPN 2.1 或更高版本）。

Real-time state notifications will have a ">STATE:" prefix prepended to them.
// 实时状态通知将在其前面添加“>STATE:”前缀。

COMMAND -- status
-----------------

Show current daemon status information, in the same format as that produced by the OpenVPN --status directive.
// 以与 OpenVPN --status 指令生成的格式相同的格式显示当前的守护程序状态信息。

Command examples:

status   -- Show status information using the default status format version.    -- 使用默认状态格式版本显示状态信息。

status 3 -- Show status information using the format of --status-version 3.     -- 使用 --status-version 3 的格式显示状态信息。

COMMAND -- username
-------------------

See the "password" section above.
// 请参阅上面的“密码”部分。

COMMAND -- verb
---------------

Change the OpenVPN --verb parameter.
The verb parameter controls the output verbosity, and may range from 0 (no output) to 15 (maximum output).
See the OpenVPN man page for additional info on verbosity levels.
// 更改 OpenVPN --verb 参数。
// verb 参数控制输出详细程度，范围从 0（无输出）到 15（最大输出）。
// 有关详细信息，请参阅 OpenVPN 手册页。

Command examples:

  verb 4  -- change the verb parameter to 4         -- 将 verb 参数更改为 4
  mute    -- show the current verb setting          -- 显示当前的 verb 设置

COMMAND -- version
------------------

Show the current OpenVPN and Management Interface versions.
// 显示当前的 OpenVPN 和管理接口版本。

COMMAND -- auth-retry
---------------------

Set the --auth-retry setting to control how OpenVPN responds to username/password authentication errors.
See the manual page for more info.
// 设置 --auth-retry 设置以控制 OpenVPN 如何响应用户名/密码身份验证错误。
// 有关更多信息，请参阅手册页。

Command examples:

  auth-retry interact -- Don't exit when bad username/passwords are entered.
                         Query for new input and retry.
                      -- 当输入错误的用户名/密码时不要退出。
                          查询新输入并重试。

COMMAND -- needok  (OpenVPN 2.1 or higher)
------------------------------------------

Confirm a ">NEED-OK" real-time notification, normally used by OpenVPN to block while waiting for a specific user action.
// 确认“>NEED-OK”实时通知，通常由 OpenVPN 用于在等待特定用户操作时阻止。

Example:

  OpenVPN needs the user to insert a cryptographic token, so it sends a real-time notification:
  // OpenVPN 需要用户插入加密令牌，因此它发送了实时通知：

    >NEED-OK:Need 'token-insertion-request' confirmation MSG:Please insert your cryptographic token

  The management client, if it is a GUI, can flash a dialog box containing the text after the "MSG:" marker to the user.
  When the user acknowledges the dialog box, the management client can issue this command:
  // 如果管理客户端是 GUI，则可以向用户显示包含“MSG:”标记后的文本的对话框。
  // 当用户确认对话框时，管理客户端可以发出此命令：

     needok token-insertion-request ok
  or
     needok token-insertion-request cancel

COMMAND -- needstr  (OpenVPN 2.1 or higher)
-------------------------------------------

Confirm a ">NEED-STR" real-time notification, normally used by OpenVPN to block while waiting for a specific user input.
// 确认“>NEED-STR”实时通知，通常由 OpenVPN 用于在等待特定用户输入时阻止。

Example:

  OpenVPN needs the user to specify some input, so it sends a real-time notification:
  // OpenVPN 需要用户指定一些输入，因此它发送了实时通知：

    >NEED-STR:Need 'name' input MSG:Please specify your name

  The management client, if it is a GUI, can flash a dialog box containing the text after the "MSG:" marker to the user.
  When the user acknowledges the dialog box, the management client can issue this command:
  // 如果管理客户端是 GUI，则可以向用户显示包含“MSG:”标记后的文本的对话框。
  // 当用户确认对话框时，管理客户端可以发出此命令：

     needstr name "John"

COMMAND -- pkcs11-id-count  (OpenVPN 2.1 or higher)
---------------------------------------------------

Retrieve available number of certificates.
// 检索可用证书的数量。

Example:

     pkcs11-id-count
     >PKCS11ID-COUNT:5

COMMAND -- pkcs11-id-get  (OpenVPN 2.1 or higher)
-------------------------------------------------

Retrieve certificate by index, the ID string should be provided as PKCS#11 identity, the blob is BASE64 encoded certificate.
// 按索引检索证书，ID 字符串应作为 PKCS#11 标识提供，blob 是 BASE64 编码的证书。

Example:

     pkcs11-id-get 1
     PKCS11ID-ENTRY:'1', ID:'', BLOB:''

COMMAND -- client-auth  (OpenVPN 2.1 or higher)
-----------------------------------------------

Authorize a ">CLIENT:CONNECT" or ">CLIENT:REAUTH" request and specify "client-connect" configuration directives in a subsequent text block.
// 授权“>CLIENT:CONNECT”或“>CLIENT:REAUTH”请求，并在后续文本块中指定“client-connect”配置指令。

The OpenVPN server should have been started with the --management-client-auth directive so that it will ask the management interface to approve client connections.
// OpenVPN 服务器应该已经使用 --management-client-auth 指令启动，以便它将要求管理接口批准客户端连接。


  client-auth {CID} {KID}
  line_1
  line_2
  ...
  line_n
  END

CID,KID -- client ID and Key ID.  See documentation for ">CLIENT:" notification for more info.
// CID，KID -- 客户端 ID 和密钥 ID。有关更多信息，请参阅“>CLIENT:”通知的文档。

line_1 to line_n -- client-connect configuration text block, as would be returned by a --client-connect script.
The text block may be null, with "END" immediately following the "client-auth" line (using a null text block is equivalent to using the client-auth-nt command).
// line_1 到 line_n -- client-connect 配置文本块，如 --client-connect 脚本返回的那样。
// 文本块可以为空，"END" 紧跟 "client-auth" 行（使用空文本块等效于使用 client-auth-nt 命令）。

A client-connect configuration text block contains OpenVPN directives that will be applied to the client instance object representing a newly connected client.
// 一个客户端连接配置文本块包含了OpenVPN指令，这些指令将应用于代表新连接的客户端实例对象。

COMMAND -- client-auth-nt  (OpenVPN 2.1 or higher)
--------------------------------------------------

Authorize a ">CLIENT:CONNECT" or ">CLIENT:REAUTH" request without specifying client-connect configuration text.
// 授权“>CLIENT:CONNECT”或“>CLIENT:REAUTH”请求，而不指定 client-connect 配置文本。

The OpenVPN server should have been started with the --management-client-auth directive so that it will ask the management interface to approve client connections.
// OpenVPN 服务器应该已经使用 --management-client-auth 指令启动，以便它将要求管理接口批准客户端连接。

  client-auth-nt {CID} {KID}

CID,KID -- client ID and Key ID.  See documentation for ">CLIENT:" notification for more info.
// CID，KID -- 客户端 ID 和密钥 ID。有关更多信息，请参阅“>CLIENT:”通知的文档。

COMMAND -- client-deny  (OpenVPN 2.1 or higher)
-----------------------------------------------

Deny a ">CLIENT:CONNECT" or ">CLIENT:REAUTH" request.
// 拒绝“>CLIENT:CONNECT”或“>CLIENT:REAUTH”请求。

  client-deny {CID} {KID} "reason-text" ["client-reason-text"]

CID,KID -- client ID and Key ID.  See documentation for ">CLIENT:" notification for more info.
// CID，KID -- 客户端 ID 和密钥 ID。有关更多信息，请参阅“>CLIENT:”通知的文档。

reason-text: a human-readable message explaining why the authentication request was denied.
This message will be output to the OpenVPN log file or syslog.
// reason-text: 人类可读的消息，解释为什么拒绝了身份验证请求。
// 此消息将输出到 OpenVPN 日志文件或 syslog。

client-reason-text: a message that will be sent to the client as part of the AUTH_FAILED message.
// client-reason-text: 将作为 AUTH_FAILED 消息的一部分发送到客户端的消息。

Note that client-deny denies a specific Key ID (pertaining to a TLS renegotiation).
A client-deny command issued in response to an initial TLS key negotiation (notified by ">CLIENT:CONNECT") will terminate the client session after returning "AUTH-FAILED" to the client.
On the other hand, a client-deny command issued in response to a TLS renegotiation (">CLIENT:REAUTH") will invalidate the renegotiated key, however the TLS session associated with the currently active key will continue to live for up to --tran-window seconds before expiration.
// 请注意，client-deny 拒绝特定的密钥 ID（涉及 TLS 重新协商）。
// 对初始 TLS 密钥协商（由“>CLIENT:CONNECT”通知）发出的 client-deny 命令将在向客户端返回“AUTH-FAILED”后终止客户端会话。
// 另一方面，对 TLS 重新协商（“>CLIENT:REAUTH”）的响应中发出的 client-deny 命令将使重新协商的密钥无效，但是与当前活动密钥关联的 TLS 会话将继续存在，直到 --tran-window 秒到期。

To immediately kill a client session, use "client-kill".
// 要立即终止客户端会话，请使用“client-kill”。

COMMAND -- client-kill  (OpenVPN 2.1 or higher)
-----------------------------------------------

Immediately kill a client instance by CID.
// 通过 CID 立即终止客户端实例。

  client-kill {CID}

CID -- client ID.  See documentation for ">CLIENT:" notification for more info.
// CID -- 客户端 ID。有关更多信息，请参阅“>CLIENT:”通知的文档。

COMMAND -- client-pf  (OpenVPN 2.1 or higher)
---------------------------------------------

Push a packet filter file to a specific client.
// 将数据包过滤器文件推送到特定客户端。

The OpenVPN server should have been started with the --management-client-pf directive so that it will require that VPN tunnel packets sent or received by client instances must conform to that client's packet filter configuration.
// OpenVPN 服务器应该已经使用 --management-client-pf 指令启动，以便它将要求客户端实例发送或接收的 VPN 隧道数据包必须符合该客户端的数据包过滤器配置。

  client-pf {CID}
  line_1
  line_2
  ...
  line_n
  END

CID -- client ID.  See documentation for ">CLIENT:" notification for more info.
// CID -- 客户端 ID。有关更多信息，请参阅“>CLIENT:”通知的文档。

line_1 to line_n -- the packet filter configuration file for this client.
// line_1 到 line_n -- 此客户端的数据包过滤器配置文件。

Packet filter file grammar:

 [CLIENTS DROP|ACCEPT]
 {+|-}common_name1
 {+|-}common_name2
 . . .
 [SUBNETS DROP|ACCEPT]
 {+|-}subnet1
 {+|-}subnet2
 . . .
 [END]

 Subnet: IP-ADDRESS | IP-ADDRESS/NUM_NETWORK_BITS | "unknown"

 CLIENTS refers to the set of clients (by their common-name) which this instance is allowed ('+') to connect to, or is excluded ('-') from connecting to.
 Note that in the case of client-to-client connections, such communication must be allowed by the packet filter configuration files of both clients AND the --client-to-client directive must have been specified in the OpenVPN server config.
 // CLIENTS 是指允许此实例连接到的客户端集（按其公共名称），或者被排除（'-'）连接到的客户端集。
 // 请注意，在客户端到客户端的连接的情况下，必须允许这种通信通过两个客户端的数据包过滤器配置文件，并且必须在 OpenVPN 服务器配置中指定 --client-to-client 指令。

 SUBNETS refers to IP addresses or IP address subnets which this client instance may connect to ('+') or is excluded ('-') from connecting to, and applies to IPv4 and ARP packets.
 The special "unknown" tag refers to packets of unknown type, i.e. a packet that is not IPv4 or ARP.
 // SUBNETS 是指此客户端实例可以连接到的 IP 地址或 IP 地址子网（'+'），或者被排除（'-'）连接到的 IP 地址或 IP 地址子网，并且适用于 IPv4 和 ARP 数据包。
 // 特殊的“unknown”标签是指未知类型的数据包，即不是 IPv4 或 ARP 的数据包。

 DROP or ACCEPT defines default policy when there is no explicit match for a common-name or subnet.  The [END] tag must exist.
 // DROP 或 ACCEPT 定义了没有公共名称或子网的显式匹配时的默认策略。必须存在 [END] 标签。

 Notes:

 * The SUBNETS section currently only supports IPv4 addresses and subnets.
 // SUBNETS 部分目前仅支持 IPv4 地址和子网。

 * A given client or subnet rule applies to both incoming and outgoing packets.
 // 给定的客户端或子网规则适用于传入和传出数据包。

 * The CLIENTS list is order-invariant.  Because the list is stored as a hash-table, the order of the list does not affect its function.
 // CLIENTS 列表是无序的。因为列表存储为哈希表，所以列表的顺序不会影响其功能。

 * The SUBNETS table is scanned sequentially, and the first item to match is chosen.  Therefore the SUBNETS table is NOT order-invariant.
 // SUBNETS 表按顺序扫描，并选择第一个匹配的项目。因此，SUBNETS 表不是无序的。

 * No client-to-client communication is allowed unless the --client-to-client configuration directive is enabled AND the CLIENTS list of BOTH clients allows the communication.
 // 除非启用了 --client-to-client 配置指令并且两个客户端的 CLIENTS 列表都允许通信，否则不允许客户端到客户端的通信。

Example packet filter spec, as transmitted to the management interface:
// 作为传输到管理接口的示例数据包过滤器规范：

 client-pf 42
 [CLIENTS ACCEPT]
 -accounting
 -enigma
 [SUBNETS DROP]
 -**********
 +10.0.0.0/8
 [END]
 END

The above example sets the packet filter policy for the client identified by CID=42.
This client may connect to all other clients except those having a common name of "accounting" or "enigma".
The client may only interact with external IP addresses in the 10.0.0.0/8 subnet, however access to ********** is specifically excluded.
// 上面的示例设置了 CID=42 的客户端的数据包过滤器策略。
// 此客户端可以连接到除“accounting”或“enigma”之外的所有其他客户端。
// 客户端只能与

Another example packet filter spec, as transmitted to the management interface:
// 另一个示例数据包过滤器规范，作为传输到管理接口：

 client-pf 99
 [CLIENTS DENY]
 +public
 [SUBNETS ACCEPT]
 +*********
 -10.0.0.0/8
 -unknown
 [END]
 END

The above example sets the packet filter policy for the client identified by CID=99.
This client may not connect to any other clients except those having a common name of "public".
It may interact with any external IP address except those in the 10.0.0.0/8 netblock.
However interaction with one address in the 10.0.0.0/8 netblock is allowed: *********.
Also, the client may not interact with external IP addresses using an "unknown" protocol (i.e. one that is not IPv4 or ARP).
// 上面的示例设置了 CID=99 的客户端的数据包过滤器策略。
// 此客户端不能连接到除“public”之外的任何其他客户端。
// 它可以与除了10.0.0.0/8网段之外的任何外部IP地址进行交互。
// 然而，允许与10.0.0.0/8网段中的一个地址进行交互：*********。
// 此外，客户端不能使用“未知”协议（即非IPv4或ARP）与外部IP地址进行交互。

COMMAND -- remote  (OpenVPN AS 2.1.5/OpenVPN 2.3 or higher)
--------------------------------------------

Provide remote host/port in response to a >REMOTE notification (client only).
Requires that the --management-query-remote directive is used.
// 在 >REMOTE 通知（仅客户端）的响应中提供远程主机/端口。
// 需要使用 --management-query-remote 指令。

  remote ACTION [HOST PORT]

The "remote" command should only be given in response to a >REMOTE notification.
For example, the following >REMOTE notification indicates that the client config file would ordinarily connect to vpn.example.com port 1194 (UDP):
// “remote”命令只应在响应 >REMOTE 通知时给出。
// 例如，以下 >REMOTE 通知表示客户端配置文件通常将连接到 vpn.example.com 端口 1194（UDP）：

  >REMOTE:vpn.example.com,1194,udp

Now, suppose we want to override the host and port, connecting instead to vpn.otherexample.com port 1234.
After receiving the above notification, use this command:
// 现在，假设我们想要覆盖主机和端口，而是连接到 vpn.otherexample.com 端口 1234。
// 在收到上述通知后，使用此命令：

  remote MOD vpn.otherexample.com 1234

To accept the same host and port as the client would ordinarily have connected to, use this command:
// 要接受与客户端通常连接的相同主机和端口，请使用此命令：

  remote ACCEPT

To skip the current connection entry and advance to the next one, use this command:
// 要跳过当前连接条目并前进到下一个条目，请使用此命令：

  remote SKIP

COMMAND -- proxy  (OpenVPN 2.3 or higher)
--------------------------------------------

Provide proxy server host/port and flags in response to a >PROXY notification (client only).
Requires that the --management-query-proxy directive is used.
// 在 >PROXY 通知（仅客户端）的响应中提供代理服务器主机/端口和标志。
// 需要使用 --management-query-proxy 指令。

  proxy TYPE HOST PORT ["nct"]

The "proxy" command must only be given in response to a >PROXY notification.
Use the "nct" flag if you only want to allow non-cleartext auth with the proxy server.
The following >PROXY notification indicates that the client config file would ordinarily connect to the first --remote configured, vpn.example.com using TCP:
// “proxy”命令必须仅在响应 >PROXY 通知时给出。
// 如果您只想允许与代理服务器进行非明文身份验证，请使用“nct”标志。
// 以下 >PROXY 通知表示客户端配置文件通常将连接到第一个 --remote 配置，vpn.example.com 使用 TCP：

  >PROXY:1,TCP,vpn.example.com

Now, suppose we want to connect to the remote host using the proxy server proxy.intranet port 8080 with secure authentication only, if required.
After receiving the above notification, use this command:
// 现在，假设我们想要使用代理服务器 proxy.intranet 端口 8080 连接到远程主机，如果需要，只能使用安全身份验证。
// 在收到上述通知后，使用此命令：

  proxy HTTP proxy.intranet 8080 nct

You can also use the SOCKS keyword to pass a SOCKS server address, like:
// 您还可以使用 SOCKS 关键字传递 SOCKS 服务器地址，例如：

  proxy SOCKS fe00::1 1080

To accept connecting to the host and port directly, use this command:
// 要接受直接连接到主机和端口，请使用此命令：

  proxy NONE

COMMAND -- rsa-sig (OpenVPN 2.3 or higher)
------------------------------------------
Provides support for external storage of the private key.
Requires the --management-external-key option.
This option can be used instead of "key" in client mode, and allows the client to run without the need to load the actual private key.
When the SSL protocol needs to perform an RSA sign operation, the data to be signed will be sent to the management interface via a notification as follows:
// 提供对私钥的外部存储支持。
// 需要 --management-external-key 选项。
// 此选项可用于客户端模式中的“key”，并允许客户端在无需加载实际私钥的情况下运行。
// 当 SSL 协议需要执行 RSA 签名操作时，将通过通知将要签名的数据发送到管理接口，如下所示：

>RSA_SIGN:[BASE64_DATA]

The management interface client should then sign BASE64_DATA using the private key and return the SSL signature as follows:
// 然后，管理接口客户端应使用私钥对 BASE64_DATA 进行签名，并返回 SSL 签名，如下所示：

rsa-sig
[BASE64_SIG_LINE]
.
.
.
END

Base64 encoded output of RSA_sign(NID_md5_sha1,... will provide a correct signature.
// RSA_sign(NID_md5_sha1,... 的 Base64 编码输出将提供正确的签名。

This capability is intended to allow the use of arbitrary cryptographic service providers with OpenVPN via the management interface.
// 此功能旨在通过管理接口允许使用任意加密服务提供程序与 OpenVPN。


OUTPUT FORMAT
-------------

(1) Command success/failure indicated by "SUCCESS: [text]" or "ERROR: [text]".
// 命令成功/失败由“SUCCESS: [text]”或“ERROR: [text]”指示。

(2) For commands which print multiple lines of output, the last line will be "END".
// 对于打印多行输出的命令，最后一行将是“END”。

(3) Real-time messages will be in the form ">[source]:[text]", where source is "CLIENT", "ECHO", "FATAL", "HOLD", "INFO", "LOG", "NEED-OK", "PASSWORD", or "STATE".
// 实时消息的形式将是“>[source]:[text]”，其中 source 是“CLIENT”、“ECHO”、“FATAL”、“HOLD”、“INFO”、“LOG”、“NEED-OK”、“PASSWORD”或“STATE”。


REAL-TIME MESSAGE FORMAT
------------------------

The OpenVPN management interface produces two kinds of output: (a) output from a command, or (b) asynchronous, real-time output which can be generated at any time.
// OpenVPN 管理接口会产生两种输出：(a) 命令输出，或 (b) 可以在任何时候生成的异步实时输出。

Real-time messages start with a '>' character in the first column and are immediately followed by a type keyword indicating the type of real-time message.
The following types are currently defined:
// 实时消息以第一列中的“>”字符开头，紧接着是一个类型关键字，指示实时消息的类型。
// 当前定义了以下类型：

BYTECOUNT -- Real-time bandwidth usage notification, as enabled by "bytecount" command when OpenVPN is running as a client.
             // 当 OpenVPN 作为客户端运行时，由“bytecount”命令启用的实时带宽使用情况通知。

BYTECOUNT_CLI -- Real-time bandwidth usage notification per-client, as enabled by "bytecount" command when OpenVPN is running as a server.
                 // 当 OpenVPN 作为服务器运行时，由“bytecount”命令启用的每个客户端的实时带宽使用情况通知。

CLIENT   -- Notification of client connections and disconnections on an OpenVPN server.
            Enabled when OpenVPN is started with the --management-client-auth option.
            CLIENT notifications may be multi-line.
            See "The CLIENT notification" section below for detailed info.
            // OpenVPN 服务器上客户端连接和断开连接的通知。
            // 当 OpenVPN 以 --management-client-auth 选项启动时启用。
            // CLIENT 通知可能是多行的。
            // 有关详细信息，请参阅下面的“CLIENT 通知”部分。

ECHO     -- Echo messages as controlled by the "echo" command.
            // 由“echo”命令控制的回显消息。

FATAL    -- A fatal error which is output to the log file just prior to OpenVPN exiting.
            // 致命错误，OpenVPN 退出前输出到日志文件。

HOLD     -- Used to indicate that OpenVPN is in a holding state and will not start until it receives a "hold release" command.
            // 用于指示 OpenVPN 处于保持状态，直到它收到“hold release”命令才会启动。

INFO     -- Informational messages such as the welcome message.
            // 信息消息，例如欢迎消息。

LOG      -- Log message output as controlled by the "log" command.
            // 由“log”命令控制的日志消息输出。

NEED-OK  -- OpenVPN needs the end user to do something, such as insert a cryptographic token.
            The "needok" command can be used to tell OpenVPN to continue.
            // OpenVPN 需要最终用户做某事，例如插入加密令牌。
            // “needok”命令可用于告诉 OpenVPN 继续。

NEED-STR -- OpenVPN needs information from end, such as a certificate to use.
            The "needstr" command can be used to tell OpenVPN to continue.
            // OpenVPN 需要来自最终用户的信息，例如要使用的证书。
            // “needstr”命令可用于告诉 OpenVPN 继续。

PASSWORD -- Used to tell the management client that OpenVPN needs a password, also to indicate password verification failure.
            // 用于告诉管理客户端 OpenVPN 需要密码，也用于指示密码验证失败。

STATE    -- Shows the current OpenVPN state, as controlled by the "state" command.
            // 显示当前的 OpenVPN 状态，由“state”命令控制。

The CLIENT notification
-----------------------

The ">CLIENT:" notification is enabled by the --management-client-auth OpenVPN configuration directive that gives the management interface client the responsibility to authenticate OpenVPN clients after their client certificate has been verified.
CLIENT notifications may be multi-line, and the sequentiality of a given CLIENT notification, its associated environmental variables, and the terminating ">CLIENT:ENV,END" line are guaranteed to be atomic.
// “>CLIENT:”通知由 --management-client-auth OpenVPN 配置指令启用，该指令使管理接口客户端负责在验证了客户端证书之后对 OpenVPN 客户端进行身份验证。
// CLIENT 通知可能是多行的，并且保证给定的 CLIENT 通知、其关联的环境变量以及终止的“>CLIENT:ENV,END”行的顺序性是原子的。

CLIENT notification types:

(1) Notify new client connection ("CONNECT") or existing client TLS session renegotiation ("REAUTH").
    Information about the client is provided by a list of environmental variables which are documented in the OpenVPN man page.
    The environmental variables passed are equivalent to those that would be passed to an --auth-user-pass-verify script.
    // 通知新的客户端连接（“CONNECT”）或现有的客户端 TLS 会话重新协商（“REAUTH”）。
    // 关于客户端的信息由环境变量列表提供，这些环境变量在 OpenVPN 手册页中有文档。
    // 传递的环境变量等效于将传递给 --auth-user-pass-verify 脚本的环境变量。

    >CLIENT:CONNECT|REAUTH,{CID},{KID}
    >CLIENT:ENV,name1=val1
    >CLIENT:ENV,name2=val2
    >CLIENT:ENV,...
    >CLIENT:ENV,END

(2) Notify successful client authentication and session initiation.
    Called after CONNECT.
    // 通知成功的客户端身份验证和会话启动。
    // 在 CONNECT 之后调用。

    >CLIENT:ESTABLISHED,{CID}
    >CLIENT:ENV,name1=val1
    >CLIENT:ENV,name2=val2
    >CLIENT:ENV,...
    >CLIENT:ENV,END

(3) Notify existing client disconnection.
    The environmental variables passed are equivalent to those that would be passed to a --client-disconnect script.
    // 通知现有的客户端断开连接。
    // 传递的环境变量等效于将传递给 --client-disconnect 脚本的环境变量。

    >CLIENT:DISCONNECT,{CID}
    >CLIENT:ENV,name1=val1
    >CLIENT:ENV,name2=val2
    >CLIENT:ENV,...
    >CLIENT:ENV,END

(4) Notify that a particular virtual address or subnet is now associated with a specific client.
    // 通知特定的虚拟地址或子网现在与特定的客户端关联。

    >CLIENT:ADDRESS,{CID},{ADDR},{PRI}

Variables:

CID --  Client ID, numerical ID for each connecting client, sequence = 0,1,2,...
        // 客户端 ID，每个连接的客户端的数字 ID，序列 = 0,1,2,...
KID --  Key ID, numerical ID for the key associated with a given client TLS session, sequence = 0,1,2,...
        // 密钥 ID，与给定客户端 TLS 会话关联的密钥的数字 ID，序列 = 0,1,2,...
PRI --  Primary (1) or Secondary (0) VPN address/subnet.
        All clients have at least one primary IP address.
        Secondary address/subnets are associated with client-specific "iroute" directives.
        // 主要（1）或次要（0）VPN 地址/子网。
        // 所有客户端都至少有一个主 IP 地址。
        // 次要地址/子网与特定于客户端的“iroute”指令相关联。
ADDR -- IPv4 address/subnet in the form ******* or *******/*************
        // IPv4 地址格式为 ******* 或 *******/************* 的子网

In the unlikely scenario of an extremely long-running OpenVPN server, CID and KID should be assumed to recycle to 0 after (2^32)-1, however this recycling behavior is guaranteed to be collision-free.
// 在极少数情况下，如果 OpenVPN 服务器运行时间非常长，CID 和 KID 应该在 (2^32)-1 之后循环到 0，但是保证这种循环行为是无冲突的。

Command Parsing
---------------

The management interface uses the same command line lexical analyzer as is used by the OpenVPN config file parser.
// 管理接口使用与 OpenVPN 配置文件解析器使用的相同的命令行词法分析器。

Whitespace is a parameter separator.
// 空格是参数分隔符。

Double quotation or single quotation characters ("", '') can be used to enclose parameters containing whitespace.
// 双引号或单引号字符（“”，''）可用于包含空格的参数。

Backslash-based shell escaping is performed, using the following  mappings, when not in single quotations:
// 当不在单引号中时，执行基于反斜杠的 shell 转义，使用以下映射：

\\       Maps to a single backslash character (\).
\"       Pass a literal doublequote character ("), don't interpret it as enclosing a parameter.
\[SPACE] Pass a literal space or tab character, don't interpret it as a parameter delimiter.
// \\       映射到单个反斜杠字符（\）。
// \"       传递一个文字双引号字符（"），不要将其解释为包含参数。
// \[SPACE] 传递一个文字空格或制表符字符，不要将其解释为参数分隔符。

Challenge/Response Protocol
---------------------------

The OpenVPN Challenge/Response Protocol allows an OpenVPN server to generate challenge questions that are shown to the user, and to see the user's responses to those challenges.
Based on the responses, the server can allow or deny access.
// OpenVPN Challenge/Response 协议允许 OpenVPN 服务器生成显示给用户的挑战问题，并查看用户对这些挑战的响应。
// 基于响应，服务器可以允许或拒绝访问。

In this way, the OpenVPN Challenge/Response Protocol can be used to implement multi-factor authentication.
Two different variations on the challenge/response protocol are supported: the "Dynamic" and "Static" protocols.
// 这样，OpenVPN Challenge/Response 协议可以用于实现多因素身份验证。
// 支持 challenge/response 协议的两种不同变体：动态和静态协议。

The basic idea of Challenge/Response is that the user must enter an additional piece of information, in addition to the username and password, to successfully authenticate.
Normally, this information is used to prove that the user posesses a certain key-like device such as cryptographic token or a particular mobile phone.
// Challenge/Response 的基本思想是，用户必须输入额外的信息，除了用户名和密码之外，才能成功验证。
// 通常，此信息用于证明用户拥有某种类似密钥的设备，例如加密令牌或特定的手机。

Dynamic protocol:

The OpenVPN dynamic challenge/response protocol works by returning a specially formatted error message after initial successful authentication.
This error message contains the challenge question, and is formatted as such:
// OpenVPN动态 challenge/response 协议的工作原理是在初始认证成功后返回一个特殊格式的错误信息。
// 该错误信息包含挑战问题，格式如下：

  CRV1::::

flags: a series of optional, comma-separated flags:
 E : echo the response when the user types it
 R : a response is required
// flags：一系列可选的、逗号分隔的标志：
//  E ：当用户输入时回显响应
//  R ：需要响应

state_id: an opaque string that should be returned to the server along with the response.
// state_id：一个不透明的字符串，应该与响应一起返回给服务器。

username_base64 : the username formatted as base64
// username_base64：用户名格式化为 base64

challenge_text : the challenge text to be shown to the user
// challenge_text：要显示给用户的 challenge 文本

Example challenge:

  CRV1:R,E:Om01u7Fh4LrGBS7uh0SWmzwabUiGiW6l:Y3Ix:Please enter token PIN

After showing the challenge_text and getting a response from the user (if R flag is specified), the client should submit the following auth creds back to the OpenVPN server:
// 显示 challenge_text 并从用户获取响应（如果指定了 R 标志），客户端应该将以下 auth 凭据提交回 OpenVPN 服务器：

Username: [username decoded from username_base64]
Password: CRV1::::

Where state_id is taken from the challenge request and response_text is what the user entered in response to the challenge_text.
If the R flag is not present, response_text may be the empty string.
// 其中 state_id 取自 challenge 请求，response_text 是用户对 challenge_text 的响应。
// 如果 R 标志不存在，response_text 可能是空字符串。

Example response (suppose the user enters "8675309" for the token PIN):

  Username: cr1 ("Y3Ix" base64 decoded)
  Password: CRV1::Om01u7Fh4LrGBS7uh0SWmzwabUiGiW6l::8675309

Static protocol:

The static protocol differs from the dynamic protocol in that the challenge question and response field is given to the user in the initial username/password dialog, and the username, password, and response are delivered back to the server in a single transaction.
// 静态协议与动态协议的不同之处在于，挑战问题和响应字段在初始用户名/密码对话框中提供给用户，并且用户名、密码和响应在单个事务中传递回服务器。

The "static-challenge" directive is used to give the challenge text to OpenVPN and indicate whether or not the response should be echoed.
// "static-challenge" 指令用于将挑战文本提供给 OpenVPN，并指示是否应该回显响应。

When the "static-challenge" directive is used, the management interface will respond as such when credentials are needed:
// 当使用 "static-challenge" 指令时，管理接口将在需要凭据时响应如下：

  >PASSWORD:Need 'Auth' username/password SC:,

  ECHO: "1" if response should be echoed, "0" to not echo
  TEXT: challenge text that should be shown to the user to facilitate their response
  // ECHO："1" 如果响应应该回显，"0" 不回显
  // TEXT：应该显示给用户以促进他们的响应的 challenge 文本

For example:

  >PASSWORD:Need 'Auth' username/password SC:1,Please enter token PIN

The above notification indicates that OpenVPN needs a --auth-user-pass password plus a response to a static challenge ("Please enter token PIN").
The "1" after the "SC:" indicates that the response should be echoed.
// 上述通知表示 OpenVPN 需要一个 --auth-user-pass 密码加上一个静态挑战的响应（"Please enter token PIN"）。
// "SC:" 后面的 "1" 表示响应应该回显。

The management interface client in this case should add the static challenge text to the auth dialog followed by a field for the user to enter a response.
Then the client should pack the password and response together into an encoded password:
// 在这种情况下，管理接口客户端应该将静态挑战文本添加到 auth 对话框，然后是一个字段，供用户输入响应。
// 然后，客户端应该将密码和响应一起打包到编码密码中：

  username "Auth" foo
  password "Auth" "SCRV1::"

For example, if the user entered "bar" as the password and 8675309 as the PIN, the following management interface commands should be issued:
// 例如，如果用户将 "bar" 输入为密码，将 8675309 输入为 PIN，则应发出以下管理接口命令：

  username "Auth" foo
  password "Auth" "SCRV1:Zm9v:ODY3NTMwOQ=="

Client-side support for challenge/response protocol:
// 客户端支持 challenge/response 协议：

Currently, the Access Server client and standalone OpenVPN client support both static and dynamic challenge/response protocols.
However, any OpenVPN client UI that drives OpenVPN via the management interface needs to add explicit support for the challenge/response protocol.
// 目前，Access Server 客户端和独立的 OpenVPN 客户端都支持静态和动态 challenge/response 协议。
// 但是，通过管理接口驱动 OpenVPN 的任何 OpenVPN 客户端 UI 都需要为 challenge/response 协议添加显式支持。