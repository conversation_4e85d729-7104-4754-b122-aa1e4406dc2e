# OpenVPN 管理接口说明

## 概述

OpenVPN 管理接口允许通过 TCP 或 Unix 域套接字从外部程序对 OpenVPN 进行管理控制。

该接口专为希望以编程方式或远程控制 OpenVPN 守护进程的开发人员设计，可在 OpenVPN 作为客户端或服务器运行时使用。

## 接口特性

- **连接方式**: 使用客户端/服务器 TCP 连接或 Unix 域套接字实现
- **监听模式**: OpenVPN 在指定的 IP 地址和端口上监听管理接口客户端连接
- **安全性**: 管理协议目前是明文传输，没有显式的安全层

## 安全建议

建议管理接口监听在以下位置之一：
- Unix 域套接字
- 本地主机 (127.0.0.1)
- 本地 VPN 地址

可以通过 VPN 本身远程连接到管理接口，但在此模式下某些功能会受到限制，例如提供私钥密码的能力。

## 启用管理接口

在 OpenVPN 配置文件中使用以下指令启用管理接口：

```
--management
```

## 连接和使用

1. 启动带有管理层的 OpenVPN 后，可以 telnet 到管理端口（确保使用理解"原始"模式的 telnet 客户端）
2. 连接到管理端口后，可以使用 `help` 命令列出所有命令

## 主要命令

### bytecount - 带宽统计

用于请求 OpenVPN 带宽使用的实时通知。

**语法:**
```
bytecount n    # n > 0，每 n 秒自动通知带宽使用情况
bytecount 0    # 关闭带宽统计通知
```

**客户端模式输出:**
```
>BYTECOUNT:{BYTES_IN},{BYTES_OUT}
```

**服务器模式输出:**
```
>BYTECOUNT_CLI:{CID},{BYTES_IN},{BYTES_OUT}
```

### echo - 回显功能

允许将 GUI 特定参数嵌入到 OpenVPN 配置文件中或从服务器推送到 OpenVPN 客户端。

**命令示例:**
```
echo on      # 开启回显消息的实时通知
echo all     # 打印当前回显历史列表
echo off     # 关闭回显消息的实时通知
echo on all  # 原子性地启用实时通知并显示历史缓冲区中的消息
```

### exit, quit - 退出

关闭管理会话，并继续在管理端口上监听来自其他客户端的连接。

### help - 帮助

打印命令摘要。

### hold - 保持状态

用于操作保持标志或从保持状态释放 OpenVPN。

**命令示例:**
```
hold         # 显示当前保持标志，0=关闭，1=开启
hold on      # 开启保持标志，使未来的重启会保持
hold off     # 关闭保持标志，使未来的重启不会保持
hold release # 离开保持状态并启动 OpenVPN，但不改变当前保持标志设置
```

### kill - 终止客户端

在服务器模式下，终止特定的客户端实例。

**命令示例:**
```
kill Test-Client              # 终止通用名称为 "Test-Client" 的客户端实例
kill tcp:*******:4000        # 终止源地址、端口和协议为 tcp:*******:4000 的客户端实例
```

### log - 日志

显示 OpenVPN 日志文件。

**命令示例:**
```
log on     # 启用日志消息的实时输出
log all    # 显示当前缓存的日志文件历史
log off    # 关闭日志消息的实时通知
log 20     # 显示最近 20 行日志文件历史
```

### password 和 username - 密码和用户名

用于向 OpenVPN 传递密码。

**示例 1 - 私钥密码:**
```
>PASSWORD:Need 'Private Key' password
```
响应: `password "Private Key" foo`

**示例 2 - 用户认证:**
```
>PASSWORD:Need 'Auth' username/password
```
响应:
```
username "Auth" foo
password "Auth" bar
```

### state - 状态

显示当前 OpenVPN 状态、状态历史或启用状态变化的实时通知。

**OpenVPN 状态:**
- `CONNECTING` - OpenVPN 的初始状态
- `WAIT` - (仅客户端) 等待服务器的初始响应
- `AUTH` - (仅客户端) 与服务器进行身份验证
- `GET_CONFIG` - (仅客户端) 从服务器下载配置选项
- `ASSIGN_IP` - 为虚拟网络接口分配 IP 地址
- `ADD_ROUTES` - 向系统添加路由
- `CONNECTED` - 初始化序列完成
- `RECONNECTING` - 发生了重启
- `EXITING` - 正在进行优雅退出

**命令示例:**
```
state        # 打印当前 OpenVPN 状态
state on     # 启用状态变化的实时通知
state off    # 禁用状态变化的实时通知
state all    # 打印当前状态历史
```

### status - 状态信息

显示当前守护进程状态信息，格式与 OpenVPN `--status` 指令产生的格式相同。

**命令示例:**
```
status   # 使用默认状态格式版本显示状态信息
status 3 # 使用 --status-version 3 的格式显示状态信息
```

### verb - 详细程度

更改 OpenVPN `--verb` 参数。verb 参数控制输出详细程度，范围从 0（无输出）到 15（最大输出）。

**命令示例:**
```
verb 4  # 将 verb 参数更改为 4
verb    # 显示当前 verb 设置
```

### version - 版本

设置客户端支持的管理接口版本（整数）或显示当前 OpenVPN 和管理接口版本。

**命令示例:**
```
version 2  # 将客户端的管理版本更改为 2（默认 = 1）
version    # 显示 OpenVPN 及其管理接口的版本
```

## 输出格式

1. **命令成功/失败**: 通过 `SUCCESS: [text]` 或 `ERROR: [text]` 指示
2. **多行输出**: 最后一行将是 `END`
3. **实时消息**: 格式为 `>[source]:[text]`，其中 source 可以是 "CLIENT"、"ECHO"、"FATAL"、"HOLD"、"INFO"、"LOG"、"NEED-OK"、"PASSWORD" 或 "STATE"

## 实时消息类型

- `BYTECOUNT` - 实时带宽使用通知
- `CLIENT` - 客户端连接和断开通知
- `ECHO` - 回显消息
- `FATAL` - 致命错误
- `HOLD` - 保持状态指示
- `INFO` - 信息消息
- `LOG` - 日志消息输出
- `PASSWORD` - 密码请求和验证失败
- `STATE` - 当前 OpenVPN 状态

## 命令解析

管理接口使用与 OpenVPN 配置文件解析器相同的命令行词法分析器：

- 空白字符是参数分隔符
- 可以使用双引号或单引号包围包含空白字符的参数
- 支持基于反斜杠的 shell 转义

**转义映射:**
- `\\` - 映射到单个反斜杠字符 (\)
- `\"` - 传递字面双引号字符 (")
- `\[SPACE]` - 传递字面空格或制表符字符

## 高级命令

### forget-passwords - 忘记密码

使守护进程忘记会话期间输入的密码。

**命令示例:**
```
forget-passwords  # 忘记到目前为止输入的密码
```

### signal - 信号

向 OpenVPN 守护进程发送信号。信号可以是 SIGHUP、SIGTERM、SIGUSR1 或 SIGUSR2 之一。

**命令示例:**
```
signal SIGUSR1  # 向守护进程发送 SIGUSR1 信号
```

### mute - 静音

更改 OpenVPN `--mute` 参数。mute 参数用于静音相同消息类别的重复消息。

**命令示例:**
```
mute 40  # 将 mute 参数更改为 40
mute     # 显示当前 mute 设置
```

### net - 网络信息

（仅限 Windows）产生等效于 OpenVPN `--show-net` 指令的输出。输出包括基于 Windows IP 助手 API 返回信息的 OpenVPN 系统网络适配器列表和路由表视图。

### pid - 进程 ID

显示当前 OpenVPN 进程的进程 ID。

### auth-retry - 认证重试

设置 `--auth-retry` 设置以控制 OpenVPN 如何响应用户名/密码认证错误。

**命令示例:**
```
auth-retry interact  # 输入错误的用户名/密码时不退出，查询新输入并重试
```

### needok - 需要确认

确认 `>NEED-OK` 实时通知，通常由 OpenVPN 用于在等待特定用户操作时阻塞。

**示例:**
```
>NEED-OK:Need 'token-insertion-request' confirmation MSG:Please insert your cryptographic token
```

响应:
```
needok token-insertion-request ok
# 或
needok token-insertion-request cancel
```

### needstr - 需要字符串

确认 `>NEED-STR` 实时通知，通常由 OpenVPN 用于在等待特定用户输入时阻塞。

**示例:**
```
>NEED-STR:Need 'name' input MSG:Please specify your name
```

响应:
```
needstr name "John"
```

### pkcs11-id-count - PKCS#11 证书计数

检索可用证书的数量。

**示例:**
```
pkcs11-id-count
>PKCS11ID-COUNT:5
```

### pkcs11-id-get - 获取 PKCS#11 证书

通过索引检索证书，ID 字符串应作为 PKCS#11 身份提供，blob 是 base64 编码的证书。

**示例:**
```
pkcs11-id-get 1
PKCS11ID-ENTRY:'1', ID:'<snip>', BLOB:'<snip>'
```

## 客户端管理命令

### client-auth - 客户端授权

授权 `>CLIENT:CONNECT` 或 `>CLIENT:REAUTH` 请求，并在后续文本块中指定"client-connect"配置指令。

**语法:**
```
client-auth {CID} {KID}
line_1
line_2
...
line_n
END
```

### client-auth-nt - 客户端授权（无文本）

授权 `>CLIENT:CONNECT` 或 `>CLIENT:REAUTH` 请求，而不指定 client-connect 配置文本。

**语法:**
```
client-auth-nt {CID} {KID}
```

### client-pending-auth - 客户端待认证

指示 OpenVPN 服务器向客户端发送 AUTH_PENDING 和 INFO_PRE 消息，以表示待认证状态。

**语法:**
```
client-pending-auth {CID} {KID} {EXTRA} {TIMEOUT}
```

### client-deny - 拒绝客户端

拒绝 `>CLIENT:CONNECT` 或 `>CLIENT:REAUTH` 请求。

**语法:**
```
client-deny {CID} {KID} "reason-text" ["client-reason-text"]
```

### client-kill - 终止客户端

通过 CID 立即终止客户端实例。

**语法:**
```
client-kill {CID}
```

### remote - 远程主机

（仅客户端）响应 `>REMOTE` 通知提供远程主机/端口。

**命令示例:**
```
remote MOD vpn.otherexample.com 1234  # 修改主机和端口
remote ACCEPT                         # 接受默认主机和端口
remote SKIP                           # 跳过当前连接条目
remote SKIP n                         # 跳过 n 个远程条目
```

### proxy - 代理

（仅客户端）响应 `>PROXY` 通知提供代理服务器主机/端口和标志。

**命令示例:**
```
proxy HTTP proxy.intranet 8080 nct  # 使用 HTTP 代理
proxy SOCKS fe00::1 1080             # 使用 SOCKS 代理
proxy NONE                           # 直接连接
```

## 挑战/响应协议

OpenVPN 挑战/响应协议允许 OpenVPN 服务器生成向用户显示的挑战问题，并查看用户对这些挑战的响应。

### 静态协议

使用 OpenVPN 的 `--static-challenge` 选项。当需要凭据且使用 `--static-challenge` 选项时，管理接口将发送：

```
>PASSWORD:Need 'Auth' username/password SC:<flag>,<TEXT>
```

**标志说明:**
- `ECHO = (flag & 0x1)` - 1 表示应回显响应，0 表示不回显
- `FORMAT = (flag & 0x2)` - 1 表示响应应与密码连接为纯文本，0 表示应按描述编码

### 动态协议

通过在初始成功认证后返回特殊格式的错误消息来工作：

```
>PASSWORD:Verification Failed: 'Auth' ['CRV1:<flags>:<state_id>:<username_base64>:<challenge_text>']
```

**响应格式:**
```
username "Auth" <username>
password "Auth" CRV1::<state_id>::<response_text>
```

## CLIENT 通知

`>CLIENT:` 通知由 `--management-client-auth` OpenVPN 配置指令启用。

**通知类型:**

1. **CONNECT/REAUTH** - 新客户端连接或现有客户端 TLS 会话重新协商
2. **ESTABLISHED** - 成功的客户端认证和会话启动
3. **DISCONNECT** - 现有客户端断开连接
4. **ADDRESS** - 特定虚拟地址或子网现在与特定客户端关联
5. **CR_RESPONSE** - 基于文本的挑战/响应

**变量说明:**
- `CID` - 客户端 ID，每个连接客户端的数字 ID
- `KID` - 密钥 ID，与给定客户端 TLS 会话关联的密钥的数字 ID
- `PRI` - 主要 (1) 或次要 (0) VPN 地址/子网
- `ADDR` - IPv4 地址/子网，格式为 ******* 或 *******/*************

## 外部密钥支持

### pk-sig/rsa-sig - 私钥签名

为私钥的外部存储提供支持。需要 `--management-external-key` 选项。

**通知格式:**
```
>PK_SIGN:[BASE64_DATA],[ALG]  # 管理版本 > 2
>PK_SIGN:[BASE64_DATA]        # 管理版本 > 1
>RSA_SIGN:[BASE64_DATA]       # 旧客户端
```

**响应格式:**
```
pk-sig
[BASE64_SIG_LINE]
...
END
```

### certificate - 证书

为证书的外部存储提供支持。需要 `--management-external-cert` 选项。

**通知格式:**
```
>NEED-CERTIFICATE:macosx-keychain:subject:o=OpenVPN-TEST
```

**响应格式:**
```
certificate
[BASE64_CERT_LINE]
...
END
```

---

*本文档基于 OpenVPN 管理接口官方文档翻译整理*
