OpenVPN Management Interface Notes
----------------------------------

The OpenVPN Management interface allows OpenVPN to
be administratively controlled from an external program via
a TCP or unix domain socket.

The interface has been specifically designed for developers
who would like to programmatically or remotely control
an OpenVPN daemon, and can be used when OpenVPN is running
as a client or server.

The management interface is implemented using a client/server TCP
connection or unix domain socket where OpenVPN will listen on a
provided IP address and port for incoming management interface client
connections.

The management protocol is currently cleartext without an explicit
security layer.  For this reason, it is recommended that the
management interface either listen on a unix domain socket,
localhost (127.0.0.1), or on the local VPN address.  It's possible
to remotely connect to the management interface over the VPN itself,
though some capabilities will be limited in this mode, such as the
ability to provide private key passwords.

The management interface is enabled in the OpenVPN
configuration file using the following directive:

--management

See the man page for documentation on this and related
directives.

Once OpenVPN has started with the management layer enabled,
you can telnet to the management port (make sure to use
a telnet client which understands "raw" mode).

Once connected to the management port, you can use
the "help" command to list all commands.

COMMAND -- bytecount
--------------------

The bytecount command is used to request real-time notification
of OpenVPN bandwidth usage.

Command syntax:

  bytecount n (where n > 0) -- set up automatic notification of
                               bandwidth usage once every n seconds
  bytecount 0 -- turn off bytecount notifications

If OpenVPN is running as a client, the bytecount notification
will look like this:

  >BYTECOUNT:{BYTES_IN},{BYTES_OUT}

BYTES_IN is the number of bytes that have been received from
the server and BYTES_OUT is the number of bytes that have been
sent to the server.

If OpenVPN is running as a server, the bytecount notification
will look like this:

  >BYTECOUNT_CLI:{CID},{BYTES_IN},{BYTES_OUT}
 
CID is the Client ID, BYTES_IN is the number of bytes that have
been received from the client and BYTES_OUT is the number of
bytes that have been sent to the client.

Note that when the bytecount command is used on the server, every
connected client will report its bandwidth numbers once every n
seconds.

When the client disconnects, the final bandwidth numbers will be
placed in the 'bytes_received' and 'bytes_sent' environmental variables
as included in the >CLIENT:DISCONNECT notification.

COMMAND -- echo
---------------

The echo capability is used to allow GUI-specific
parameters to be either embedded in the OpenVPN config file
or pushed to an OpenVPN client from a server.

Command examples:

  echo on      -- turn on real-time notification of echo messages
  echo all     -- print the current echo history list
  echo off     -- turn off real-time notification of echo messages
  echo on all  -- atomically enable real-time notification,
                  plus show any messages in history buffer

For example, suppose you are developing a OpenVPN GUI and
you want to give the OpenVPN server the ability to ask
the GUI to forget any saved passwords.

In the OpenVPN server config file, add:

  push "echo forget-passwords"

When the OpenVPN client receives its pulled list of directives
from the server, the "echo forget-passwords" directive will
be in the list, and it will cause the management interface
to save the "forget-passwords" string in its list of echo
parameters.

The management interface client can use "echo all" to output the full
list of echoed parameters, "echo on" to turn on real-time
notification of echoed parameters via the ">ECHO:" prefix,
or "echo off" to turn off real-time notification.

When the GUI connects to the OpenVPN management socket, it
can issue an "echo all" command, which would produce output
like this:

  1101519562,forget-passwords
  END

Essentially the echo command allowed us to pass parameters from
the OpenVPN server to the OpenVPN client, and then to the
management interface client (such as a GUI).  The large integer is the
unix date/time when the echo parameter was received.

If the management interface client had issued the command "echo on",
it would have enabled real-time notifications of echo
parameters.  In this case, our "forget-passwords" message
would be output like this:

  >ECHO:1101519562,forget-passwords

Like the log command, the echo command can atomically show
history while simultaneously activating real-time updates:

  echo on all

The size of the echo buffer is currently hardcoded to 100
messages.


Generally speaking, the OpenVPN Core does not understand echo
messages at all (so a cooperating GUI and Server can use this
mechanism for arbitrary information transport).

This said, a few echo commands have been agreed upon between the
community maintained OpenVPN Windows GUI and Tunnelblick for MacOS,
and documentation of these can be found in doc/gui-notes.txt.


COMMAND -- exit, quit
---------------------

Close the management session, and resume listening on the
management port for connections from other clients. Currently,
the OpenVPN daemon can at most support a single management interface
client any one time.

COMMAND -- help
---------------

Print a summary of commands.

COMMAND -- hold
---------------

The hold command can be used to manipulate the hold flag,
or release OpenVPN from a hold state.

If the hold flag is set on initial startup or
restart, OpenVPN will hibernate prior to initializing
the tunnel until the management interface receives
a "hold release" command.

The --management-hold directive of OpenVPN can be used
to start OpenVPN with the hold flag set.

The hold flag setting is persistent and will not
be reset by restarts.

OpenVPN will indicate that it is in a hold state by
sending a real-time notification to the management interface
client, the parameter indicates how long OpenVPN would
wait without UI (as influenced by connect-retry exponential
backoff). The UI needs to wait for releasing the hold if it
wants similar behavior:

  >HOLD:Waiting for hold release:10

Command examples:

  hold         -- show current hold flag, 0=off, 1=on.
  hold on      -- turn on hold flag so that future restarts
                  will hold.
  hold off     -- turn off hold flag so that future restarts will
                  not hold.
  hold release -- leave hold state and start OpenVPN, but
                  do not alter the current hold flag setting.

COMMAND -- kill
---------------

In server mode, kill a particular client instance.

Command examples:

  kill Test-Client -- kill the client instance having a
                      common name of "Test-Client".
  kill tcp:*******:4000 -- kill the client instance having a
                           source address, port and proto of
                           tcp:*******:4000

  Note that kill by address won't work for IPv6-connected
  clients yet, so rely on kill by CN or CID instead.

Use the "status" command to see which clients are connected.

COMMAND -- log
--------------

Show the OpenVPN log file.  Only the most recent n lines
of the log file are cached by the management interface, where
n is controlled by the OpenVPN --management-log-cache directive.

Command examples:

  log on     -- Enable real-time output of log messages.
  log all    -- Show currently cached log file history.
  log on all -- Atomically show all currently cached log file
                history then enable real-time notification of
                new log file messages.
  log off    -- Turn off real-time notification of log messages.
  log 20     -- Show the most recent 20 lines of log file history.

Real-time notification format:

Real-time log messages begin with the ">LOG:" prefix followed
by the following comma-separated fields:

  (a) unix integer date/time,
  (b) zero or more message flags in a single string:
      I -- informational
      F -- fatal error
      N -- non-fatal error
      W -- warning
      D -- debug, and
  (c) message text.

COMMAND -- mute
---------------

Change the OpenVPN --mute parameter.  The mute parameter is
used to silence repeating messages of the same message
category.

Command examples:

  mute 40 -- change the mute parameter to 40
  mute    -- show the current mute setting

COMMAND -- net
--------------

(Windows Only) Produce output equivalent to the OpenVPN
--show-net directive.  The output includes OpenVPN's view
of the system network adapter list and routing table based
on information returned by the Windows IP helper API.

COMMAND -- pid
--------------

Shows the process ID of the current OpenVPN process.

COMMAND -- password and username
--------------------------------

  The password command is used to pass passwords to OpenVPN.

  If OpenVPN is run with the --management-query-passwords
  directive, it will query the management interface for RSA
  private key passwords and the --auth-user-pass
  username/password.

  When OpenVPN needs a password from the management interface,
  it will produce a real-time ">PASSWORD:" message.

  Example 1:

    >PASSWORD:Need 'Private Key' password

  OpenVPN is indicating that it needs a password of type
  "Private Key".

  The management interface client should respond as follows:

    password "Private Key" foo

  Example 2:

    >PASSWORD:Need 'Auth' username/password

  OpenVPN needs a --auth-user-pass username and password.  The
  management interface client should respond:

    username "Auth" foo
    password "Auth" bar

  The username/password itself can be in quotes, and special
  characters such as double quote or backslash must be escaped,
  for example,

    password "Private Key" "foo\"bar"

  The escaping rules are the same as for the config file.
  See the "Command Parsing" section below for more info.

  The PASSWORD real-time message type can also be used to
  indicate password or other types of authentication failure:

  Example 3: The private key password is incorrect and OpenVPN
  is exiting:

    >PASSWORD:Verification Failed: 'Private Key'

  Example 4: The --auth-user-pass username/password failed,
  and OpenVPN will exit with a fatal error if '--auth-retry none'
  (which is the default) is in effect:

    >PASSWORD:Verification Failed: 'Auth'

  Example 5: The --auth-user-pass username/password failed,
  and the server provided a custom client-reason-text string
  using the client-deny server-side management interface command.

    >PASSWORD:Verification Failed: 'custom server-generated string'

  Example 6: If server pushes --auth-token to the client, the OpenVPN
  will produce a real-time PASSWORD message:

    >PASSWORD:Auth-Token:foobar

  Example 7: Static challenge/response:

    >PASSWORD:Need 'Auth' username/password SC:1,Please enter token PIN

  OpenVPN needs an --auth-user-pass username and password and the
  response to a challenge. The user's response to "Please enter
  token PIN" should be obtained and included in the management
  interface client's response along with the username and password
  formatted as described in the Challenge/Response Protocol section
  below.

  Example 8: Dynamic challenge/response:

    >PASSWORD:Verification Failed: ['CRV1:R,E:Om01u7Fh4LrGBS7uh0SWmzwabUiGiW6l:Y3Ix:Please enter token PIN']

  The previous --auth-user-pass username/password failed or is not
  fully complete, and the server provided a custom
  client-reason-text string indicating that a dynamic
  challenge/response should occur the next time that a "Need 'Auth'
  username/password" message is seen.

  When the next "Need 'Auth' username/password" without a static
  challenge is seen, the user's response to "Please enter token PIN"
  should be obtained and included in the management interface client's
  response along with the username and password formatted as described
  in the Challenge/Response Protocol section below

See the "Challenge/Response Protocol" section below for more details
about examples 7 and 8, including how the management interface client
should respond.

COMMAND -- forget-passwords
---------------------------

The forget-passwords command will cause the daemon to forget passwords
entered during the session.

Command example:

  forget-passwords -- forget passwords entered so far.

COMMAND -- signal
-----------------

The signal command will send a signal to the OpenVPN daemon.
The signal can be one of SIGHUP, SIGTERM, SIGUSR1, or SIGUSR2.

Command example:

  signal SIGUSR1 -- send a SIGUSR1 signal to daemon

COMMAND -- state
----------------

Show the current OpenVPN state, show state history, or
enable real-time notification of state changes.

These are the OpenVPN states:

CONNECTING    -- OpenVPN's initial state.
WAIT          -- (Client only) Waiting for initial response
                 from server.
AUTH          -- (Client only) Authenticating with server.
GET_CONFIG    -- (Client only) Downloading configuration options
                 from server.
ASSIGN_IP     -- Assigning IP address to virtual network
                 interface.
ADD_ROUTES    -- Adding routes to system.
CONNECTED     -- Initialization Sequence Completed.
RECONNECTING  -- A restart has occurred.
EXITING       -- A graceful exit is in progress.
RESOLVE       -- (Client only) DNS lookup
TCP_CONNECT   -- (Client only) Connecting to TCP server
AUTH_PENDING  -- (Client only) Authentication pending

Command examples:

  state        -- Print current OpenVPN state.
  state on     -- Enable real-time notification of state changes.
  state off    -- Disable real-time notification of state changes.
  state all    -- Print current state history.
  state 3      -- Print the 3 most recent state transitions.
  state on all -- Atomically show state history while at the
                  same time enable real-time state notification
		  of future state transitions.

The output format consists of up to 9 comma-separated parameters:
  (a) the integer unix date/time,
  (b) the state name,
  (c) optional descriptive string (used mostly on RECONNECTING
      and EXITING to show the reason for the disconnect),
  (d) optional TUN/TAP local IPv4 address
  (e) optional address of remote server,
  (f) optional port of remote server,
  (g) optional local address,
  (h) optional local port, and
  (i) optional TUN/TAP local IPv6 address.

Fields (e)-(h) are shown for CONNECTED state,
(d) and (i) are shown for ASSIGN_IP and CONNECTED states.

(e) is available starting from OpenVPN 2.1
(f)-(i) are available starting from OpenVPN 2.4

For AUTH_PENDING, if (c) is present, it would read
as "timeout number" where number is the number of seconds
before authentication will timeout. It is printed as an
unsigned integer (%u).

Real-time state notifications will have a ">STATE:" prefix
prepended to them.

COMMAND -- status
-----------------

Show current daemon status information, in the same format as
that produced by the OpenVPN --status directive.

Command examples:

status   -- Show status information using the default status
            format version.

status 3 -- Show status information using the format of
            --status-version 3.

COMMAND -- username
-------------------

See the "password" section above.

COMMAND -- verb
---------------

Change the OpenVPN --verb parameter.  The verb parameter
controls the output verbosity, and may range from 0 (no output)
to 15 (maximum output).  See the OpenVPN man page for additional
info on verbosity levels.

Command examples:

  verb 4  -- change the verb parameter to 4
  verb    -- show the current verb setting

COMMAND -- version
------------------

Set the version (integer) of Management Interface supported by the
client or show the current OpenVPN and Management Interface versions.

Command examples:
  version 2  -- Change management version of client to 2 (default = 1)
  version    -- Show the version of OpenVPN and its Management Interface

COMMAND -- auth-retry
---------------------

Set the --auth-retry setting to control how OpenVPN responds to
username/password authentication errors.  See the manual page
for more info.

Command examples:

  auth-retry interact -- Don't exit when bad username/passwords are entered.
                         Query for new input and retry.

COMMAND -- needok  (OpenVPN 2.1 or higher)
------------------------------------------

Confirm a ">NEED-OK" real-time notification, normally used by
OpenVPN to block while waiting for a specific user action.

Example:

  OpenVPN needs the user to insert a cryptographic token,
  so it sends a real-time notification:

    >NEED-OK:Need 'token-insertion-request' confirmation MSG:Please insert your cryptographic token

  The management interface client, if it is a GUI, can flash a dialog
  box containing the text after the "MSG:" marker to the user.
  When the user acknowledges the dialog box,
  the management interface client should issue either:

     needok token-insertion-request ok
  or
     needok token-insertion-request cancel

COMMAND -- needstr  (OpenVPN 2.1 or higher)
-------------------------------------------

Confirm a ">NEED-STR" real-time notification, normally used by
OpenVPN to block while waiting for a specific user input.

Example:

  OpenVPN needs the user to specify some input, so it sends a
  real-time notification:

    >NEED-STR:Need 'name' input MSG:Please specify your name

  The management interface client, if it is a GUI, can flash a dialog
  box containing the text after the "MSG:" marker to the user.
  When the user acknowledges the dialog box,
  the management interface client should issue this command:

     needstr name "John"

COMMAND -- pkcs11-id-count  (OpenVPN 2.1 or higher)
---------------------------------------------------

Retrieve available number of certificates.

Example:

     pkcs11-id-count
     >PKCS11ID-COUNT:5

COMMAND -- pkcs11-id-get  (OpenVPN 2.1 or higher)
-------------------------------------------------

Retrieve certificate by index, the ID string should be provided
as PKCS#11 identity, the blob is a base 64 encoded certificate.

Example:

     pkcs11-id-get 1
     PKCS11ID-ENTRY:'1', ID:'<snip>', BLOB:'<snip>'

COMMAND -- client-auth  (OpenVPN 2.1 or higher)
-----------------------------------------------

Authorize a ">CLIENT:CONNECT" or ">CLIENT:REAUTH" request and specify
"client-connect" configuration directives in a subsequent text block.

The OpenVPN server should have been started with the
--management-client-auth directive so that it will ask the management
interface to approve client connections.


  client-auth {CID} {KID}
  line_1
  line_2
  ...
  line_n
  END

CID,KID -- client ID and Key ID.  See documentation for ">CLIENT:"
notification for more info.

line_1 to line_n -- client-connect configuration text block, as would be
returned by a --client-connect script.  The text block may be null, with
"END" immediately following the "client-auth" line (using a null text
block is equivalent to using the client-auth-nt command).

A client-connect configuration text block contains OpenVPN directives
that will be applied to the client instance object representing a newly
connected client.

COMMAND -- client-auth-nt  (OpenVPN 2.1 or higher)
--------------------------------------------------

Authorize a ">CLIENT:CONNECT" or ">CLIENT:REAUTH" request without specifying
client-connect configuration text.

The OpenVPN server should have been started with the
--management-client-auth directive so that it will ask the management
interface to approve client connections.

  client-auth-nt {CID} {KID}

CID,KID -- client ID and Key ID.  See documentation for ">CLIENT:"
notification for more info.

COMMAND -- client-pending-auth  (OpenVPN 2.5 or higher)
----------------------------------------------------

Instruct OpenVPN server to send AUTH_PENDING and INFO_PRE message
to signal a pending authenticating to the client. A pending auth means
that connecting requires extra authentication like a one time
password or doing a single sign on via web.

    client-pending-auth {CID} {KID} {EXTRA} {TIMEOUT}

The server will send AUTH_PENDING and INFO_PRE,{EXTRA} to the client. If the
client supports accepting keywords to AUTH_PENDING (announced via IV_PROTO),
TIMEOUT parameter will be also be announced to the client to allow it to modify
its own timeout. The client is expected to inform the user that authentication
is pending and display the extra information and also show the user the
remaining time to complete the auth if applicable.

Receiving an AUTH_PENDING message will make the client change its timeout to
the timeout proposed by the server, even if the timeout is shorter.
If the client does not receive a packet from the server for hand-window the
connection times out regardless of the timeout. This ensures that the connection
still times out relatively quickly in case of network problems. The client will
continuously send PULL_REQUEST messages to the server until the timeout is reached.
This message also triggers an ACK message from the server that resets the
hand-window based timeout.

Both client and server limit the maximum timeout to the smaller value of half the
--tls-reneg minimum time and --hand-window time (defaults to 60s).

For the format of {EXTRA} see below. For OpenVPN server this is a stateless
operation and needs to be followed by a client-deny/client-auth[-nt] command
(that is the result of the out-of-band authentication).

Note that the {KID} argument has been added in management version 5
to specify the pending client key the authentication belongs to.
This ensures that the pending auth message is tied strictly to the
authentication session.

Before issuing a client-pending-auth to a client instead of a
client-auth/client-deny, the server should check the IV_SSO
environment variable for whether the method is supported. Currently,
defined methods are crtext for challenge/response using text
(e.g., TOTP), openurl (deprecated) and webauth for opening a URL in
the client to continue authentication. A client supporting webauth and
crtext would set

    setenv IV_SSO webauth,crtext

The variable name IV_SSO is historic as AUTH_PENDING was first used
to signal single sign on support. To keep compatibility with existing
implementations the name IV_SSO is kept in lieu of a better name.

The management interface of the client receives notification of
pending auth via

>STATE:datetime,AUTH_PENDING,[timeout number],,,,,

If {EXTRA} is present the client is informed using INFOMSG
notification as

>INFOMSG:{EXTRA}

where {EXTRA} is formatted as received from the server.
Currently defined formats for {EXTRA} are detailed below.

webauth and openurl
===================
For a web based extra authentication (like for
SSO/SAML) {EXTRA} should be

    OPEN_URL:url

or

    WEB_AUTH:flags:url

The OPEN_URL method is deprecated as it does not allow to send flags which
proved to be needed to signal certain behaviour to the client.

The client should ask the user to open the URL to continue.

The space in a control message is limited, so this url should be kept
short to avoid issues. If a longer url is required a URL that redirects
to the longer URL should be sent instead. The total length is limited to 1024
bytes which includes the INFO_PRE:WEB_AUTH:flags.

flags is a list of flags which are separated by commas. Currently defined
flags are:

- proxy     (see next pargraph)
- hidden    start the webview in hidden mode (see openvpn3 webauth documentation)
- external  Do not use an internal webview but use an external browser. Some
            authentication providers refuse to work in an internal webview.


A complete documentation how URLs should be handled on the client is available
in the openvpn3 repository:

https://github.com/OpenVPN/openvpn3/blob/master/doc/webauth.md

webauth with proxy
==================
This is a variant of webauth that allows opening a url via an
HTTP proxy. It could be used to avoid issues with OpenVPN connection's
persist-tun that may cause the web server to be unreachable.
The client should announce proxy in its IV_SSO and parse the
proxy flag in the WEB_AUTH message. The format of {EXTRA} in this case is

    WEB_AUTH:proxy=<proxy>;<proxy_port>;<proxy_user_base64>;<proxy_password_base64>,flags:url

The proxy should be a literal IPv4 address or IPv6 address enclosed in [] to avoid
ambiguity in parsing. A literal IP address is preferred as DNS might not be
available when the client needs to open the url. The IP address will usually
be the address that client uses to connect to the VPN server. For dual-homed
VPN servers, the server should respond with the same address that the client
connects to.

This address is also usually excluded from being redirected over the VPN
by a host route. If the platform (like Android) uses another way of protecting
the VPN connection from routing loops, the client needs to also exclude the
connection to the proxy in the same manner.

Should another IP be used, then the VPN configuration should include a route
statement to exclude that address from being routed over the VPN.

crtext
=======
The format of {EXTRA} is similar to the already used two step authentication
described in Challenge/Response Protocol section of this document. Since
most of the fields are not necessary or can be inferred, only the <flags>
and <challenge_text> fields are used:

    CR_TEXT:<flags>:<challenge_text>

<flags>: a series of optional, comma-separated flags:
 E : echo the response when the user types it.
 R : a response is required.

<challenge_text>: the challenge text to be shown to the user.

The client should return the response to the crtext challenge
using the cr-response command.

COMMAND -- client-deny  (OpenVPN 2.1 or higher)
-----------------------------------------------

Deny a ">CLIENT:CONNECT" or ">CLIENT:REAUTH" request.

  client-deny {CID} {KID} "reason-text" ["client-reason-text"]

CID,KID -- client ID and Key ID.  See documentation for ">CLIENT:"
notification for more info.

reason-text: a human-readable message explaining why the authentication
request was denied.  This message will be output to the OpenVPN log
file or syslog.

client-reason-text: a message that will be sent to the client as
part of the AUTH_FAILED message.

Note that client-deny denies a specific Key ID (pertaining to a
TLS renegotiation).  A client-deny command issued in response to
an initial TLS key negotiation (notified by ">CLIENT:CONNECT") will
terminate the client session after returning "AUTH-FAILED" to the client.
On the other hand, a client-deny command issued in response to
a TLS renegotiation (">CLIENT:REAUTH") will invalidate the renegotiated
key, however the TLS session associated with the currently active
key will continue to live for up to --tran-window seconds before
expiration.

To immediately kill a client session, use "client-kill".

COMMAND -- client-kill  (OpenVPN 2.1 or higher)
-----------------------------------------------

Immediately kill a client instance by CID.

  client-kill {CID}

CID -- client ID.  See documentation for ">CLIENT:" notification for more
info.

COMMAND -- remote-entry-count (OpenVPN 2.6+ management version > 3)
-------------------------------------------------------------------

Retrieve available number of remote host/port entries

Example:

  Management interface client sends:

    remote-entry-count

  OpenVPN daemon responds with

  5
  END

COMMAND -- remote-entry-get (OpenVPN 2.6+ management version > 3)
------------------------------------------------------------------

  remote-entry-get <start> [<end>]

Retrieve remote entry (host, port, protocol, and status) for index
<start> or indices from <start> to <end>-1. Alternatively
<start> = "all" retrieves all remote entries. The index is 0-based.
If the entry is disabled due to protocol or proxy restrictions
(i.e., ce->flag & CE_DISABLED == 1), the status is returned as "disabled",
otherwise it reads "enabled" without quotes.

Example 1:

  Management interface client sends:

    remote-entry-get 1

  OpenVPN daemon responds with

    1,vpn.example.com,1194,udp,enabled
    END

Example 2:

  Management interface client sends:

    remote-entry-get 1 3

  OpenVPN daemon responds with

    1,vpn.example.com,1194,udp,enabled
    2,vpn.example.net,443,tcp-client,disabled
    END

Example 3:
  Management interface client sends:

    remote-entry-get all

  OpenVPN daemon with 3 connection entries responds with

    0,vpn.example.com,1194,udp,enabled
    1,vpn.example.com,443,tcp-client,enabled
    2,vpn.example.net,443,udp,enabled
    END

COMMAND -- remote  (OpenVPN AS 2.1.5/OpenVPN 2.3 or higher)
--------------------------------------------

Provide remote host/port in response to a >REMOTE notification
(client only).  Requires that the --management-query-remote
directive is used.

  remote ACTION [HOST PORT]

The "remote" command should only be given in response to a >REMOTE
notification.  For example, the following >REMOTE notification
indicates that the client config file would ordinarily connect
to vpn.example.com port 1194 (UDP):

  >REMOTE:vpn.example.com,1194,udp

Now, suppose we want to override the host and port, connecting
instead to vpn.otherexample.com port 1234.  After receiving
the above notification, use this command:

  remote MOD vpn.otherexample.com 1234

To accept the same host and port as the client would ordinarily
have connected to, use this command:

  remote ACCEPT

To skip the current connection entry and advance to the next one,
use this command:

  remote SKIP

Starting OpenVPN version 2.6 (management version > 3), skip
multiple remotes using:

  remote SKIP n

where n > 0 is the number of remotes to skip.

COMMAND -- proxy  (OpenVPN 2.3 or higher)
--------------------------------------------

Provide proxy server host/port and flags in response to a >PROXY
notification (client only).  Requires that the --management-query-proxy
directive is used.

  proxy TYPE HOST PORT ["nct"]

The "proxy" command must only be given in response to a >PROXY
notification.  Use the "nct" flag if you only want to allow
non-cleartext auth with the proxy server.  The following >PROXY
notification indicates that the client config file would ordinarily
connect to the first --remote configured, vpn.example.com using TCP:

  >PROXY:1,TCP,vpn.example.com

Now, suppose we want to connect to the remote host using the proxy server
proxy.intranet port 8080 with secure authentication only, if required.
After receiving the above notification, use this command:

  proxy HTTP proxy.intranet 8080 nct

You can also use the SOCKS keyword to pass a SOCKS server address, like:

  proxy SOCKS fe00::1 1080

To accept connecting to the host and port directly, use this command:

  proxy NONE

COMMAND -- cr-response (OpenVPN 2.5 or higher)
-------------------------------------------------
Provides support for sending responses to a challenge/response
query via INFOMSG,CR_TEXT (client-only). The response should
be base64 encoded:

  cr-response SGFsbG8gV2VsdCE=

This command is intended to be used after the client receives a
CR_TEXT challenge (see client-pending-auth section). The argument
to cr-response is the base64 encoded answer to the challenge and
depends on the challenge itself. For a TOTP challenge this would be
a number encoded as base64; for a challenge like "what day is it today?"
it would be a string encoded as base64.

COMMAND -- pk-sig (OpenVPN 2.5 or higher, management version > 1)
COMMAND -- rsa-sig (OpenVPN 2.3 or higher, management version <= 1)
-----------------------------------------------------------------
Provides support for external storage of the private key. Requires the
--management-external-key option. This option can be used instead of "key"
in client mode, and allows the client to run without the need to load the
actual private key. When the SSL protocol needs to perform a sign
operation, the data to be signed will be sent to the management interface
via a notification as follows:

>PK_SIGN:[BASE64_DATA],[ALG] (if client announces support for management version > 2)
>PK_SIGN:[BASE64_DATA] (if client announces support for management version > 1)
>RSA_SIGN:[BASE64_DATA] (only older clients will be prompted like this)

The management interface client should then create an appropriate signature of
the (decoded) BASE64_DATA using the private key and return the SSL signature as
follows:

pk-sig (or rsa-sig)
[BASE64_SIG_LINE]
.
.
.
END

Base 64 encoded output of RSA_private_encrypt for RSA or ECDSA_sign()
for EC using OpenSSL or mbedtls_pk_sign() using mbed TLS will provide a
correct signature.
The rsa-sig interface expects PKCS1 padded signatures for RSA keys
(RSA_PKCS1_PADDING). EC signatures are always unpadded.

This capability is intended to allow the use of arbitrary cryptographic
service providers with OpenVPN via the management interface.

New and updated clients are expected to use the version command to announce
a version > 1 and handle '>PK_SIGN' prompt and respond with 'pk-sig'.

The signature algorithm is indicated in the PK_SIGN request only if the
management client-version is > 2.  In particular, to support TLS1.3 and
TLS1.2 using OpenSSL 1.1.1, unpadded signature support is required  and this
can be indicated in the signing request only if the client version is > 2"

The currently defined padding algorithms are:

 - RSA_PKCS1_PADDING            -  PKCS1 padding and RSA signature
 - RSA_NO_PADDING               -  No padding may be added for the signature
 - ECDSA                        -  EC signature.
 - RSA_PKCS1_PSS_PADDING,params -  RSA signature with PSS padding

   The params for PSS are specified as 'hashalg=name,saltlen=[max|digest]'.

   The hashalg names are short common names such as SHA256, SHA224, etc.
   PSS saltlen="digest" means use the same size as the hash to sign, while
   "max" indicates maximum possible saltlen which is
   '(nbits-1)/8 - hlen - 2'. Here 'nbits' is the number of bits in the
   key modulus and 'hlen' the size in octets of the hash.
   (See: RFC 8017 sec 8.1.1 and 9.1.1)

   In the case of PKCS1_PADDING, when the hash algorithm is not legacy
   MD5-SHA1, the hash is encoded with DigestInfo header before presenting
   to the management interface. This is identical to CKM_RSA_PKCS in Cryptoki
   as well as what RSA_private_encrypt() in OpenSSL expects.

COMMAND -- certificate (OpenVPN 2.4 or higher)
----------------------------------------------
Provides support for external storage of the certificate. Requires the
--management-external-cert option. This option can be used instead of "cert"
in client mode. On SSL protocol initialization a notification will be sent
to the management interface with a hint as follows:

>NEED-CERTIFICATE:macosx-keychain:subject:o=OpenVPN-TEST

The management interface client should use the hint to obtain the specific
SSL certificate and then return base 64 encoded certificate as follows:

certificate
[BASE64_CERT_LINE]
.
.
.
END

This capability is intended to allow the use of certificates
stored outside of the filesystem (e.g. in Mac OS X Keychain)
with OpenVPN via the management interface.

OUTPUT FORMAT
-------------

(1) Command success/failure indicated by "SUCCESS: [text]" or
    "ERROR: [text]".

(2) For commands which print multiple lines of output,
    the last line will be "END".

(3) Real-time messages will be in the form ">[source]:[text]",
    where source is "CLIENT", "ECHO", "FATAL", "HOLD", "INFO", "LOG",
    "NEED-OK", "PASSWORD", or "STATE".

REAL-TIME MESSAGE FORMAT
------------------------

The OpenVPN management interface produces two kinds of
output: (a) output from a command, or (b) asynchronous,
real-time output which can be generated at any time.

Real-time messages start with a '>' character in the first
column and are immediately followed by a type keyword
indicating the type of real-time message.  The following
types are currently defined:

BYTECOUNT -- Real-time bandwidth usage notification, as enabled
             by "bytecount" command when OpenVPN is running as
             a client.

BYTECOUNT_CLI -- Real-time bandwidth usage notification per-client,
	         as enabled by "bytecount" command when OpenVPN is
                 running as a server.

CLIENT   -- Notification of client connections and disconnections
            on an OpenVPN server.  Enabled when OpenVPN is started
            with the --management-client-auth option.  CLIENT
            notifications may be multi-line.  See "The CLIENT
            notification" section below for detailed info.

ECHO     -- Echo messages as controlled by the "echo" command.

FATAL    -- A fatal error which is output to the log file just
            prior to OpenVPN exiting.

HOLD     -- Used to indicate that OpenVPN is in a holding state
            and will not start until it receives a
            "hold release" command.

INFO     -- Informational messages such as the welcome message.

LOG      -- Log message output as controlled by the "log" command.

NEED-OK  -- OpenVPN needs the end user to do something, such as
            insert a cryptographic token.  The "needok" command can
            be used to tell OpenVPN to continue.

NEED-STR -- OpenVPN needs information from end, such as
            a certificate to use.  The "needstr" command can
            be used to tell OpenVPN to continue.

PASSWORD -- Used to tell the management interface client that OpenVPN
            needs a password, also to indicate password
            verification failure.

STATE    -- Shows the current OpenVPN state, as controlled
            by the "state" command.

INFOMSG  -- Authentication related info from server such as
            CR_TEXT or OPEN_URL. See description under client-pending-auth

The CLIENT notification
-----------------------

The ">CLIENT:" notification is enabled by the --management-client-auth
OpenVPN configuration directive that gives the management interface client
the responsibility to authenticate OpenVPN clients after their client
certificate has been verified.  CLIENT notifications may be multi-line, and
the sequentiality of a given CLIENT notification, its associated environmental
variables, and the terminating ">CLIENT:ENV,END" line are guaranteed to be
atomic.

CLIENT notification types:

(1) Notify new client connection ("CONNECT") or existing client TLS session
    renegotiation ("REAUTH").  Information about the client is provided
    by a list of environmental variables which are documented in the OpenVPN
    man page.  The environmental variables passed are equivalent to those
    that would be passed to an --auth-user-pass-verify script.

    >CLIENT:CONNECT|REAUTH,{CID},{KID}
    >CLIENT:ENV,name1=val1
    >CLIENT:ENV,name2=val2
    >CLIENT:ENV,...
    >CLIENT:ENV,END

(2) Notify successful client authentication and session initiation.
    Called after CONNECT.

    >CLIENT:ESTABLISHED,{CID}
    >CLIENT:ENV,name1=val1
    >CLIENT:ENV,name2=val2
    >CLIENT:ENV,...
    >CLIENT:ENV,END

(3) Notify existing client disconnection.  The environmental variables passed
    are equivalent to those that would be passed to a --client-disconnect
    script.

    >CLIENT:DISCONNECT,{CID}
    >CLIENT:ENV,name1=val1
    >CLIENT:ENV,name2=val2
    >CLIENT:ENV,...
    >CLIENT:ENV,END

(4) Notify that a particular virtual address or subnet
    is now associated with a specific client.

    >CLIENT:ADDRESS,{CID},{ADDR},{PRI}

(5) Text based challenge/Response

   >CLIENT:CR_RESPONSE,{CID},{KID},{response_base64}
   >CLIENT:ENV,name1=val1
   >CLIENT:ENV,name2=val2
   >CLIENT:ENV,...
   >CLIENT:ENV,END

   Use of the cr-response command on the client side will trigger this
   message on the server side.

   CR_RESPONSE notification fulfills the same purpose as the
   CRV1 response in the traditional challenge/response. See that section
   below for more details. Since this uses the same cid as in the original
   client-pending-auth challenge,  we do not include the username and opaque
   session data in this notification. The string {response_base64} only contains
   the actual response received from the client.

   It is important to note that OpenVPN2 merely passes the authentication
   information and does not do any further checks. (E.g. if a CR was issued
   before or if multiple CR responses were sent from the client or if
   data has a valid base64 encoding)

   This interface should be be sufficient for almost all challenge/response
   system that can be implemented with a single round and base64 encoding of the
   response. Mechanisms that need multiple rounds or more complex answers
   should implement a different response type than CR_RESPONSE.


Variables:

CID --  Client ID, numerical ID for each connecting client, sequence = 0,1,2,...
KID --  Key ID, numerical ID for the key associated with a given client TLS session,
        sequence = 0,1,2,...
PRI --  Primary (1) or Secondary (0) VPN address/subnet.  All clients have at least
        one primary IP address.  Secondary address/subnets are associated with
        client-specific "iroute" directives.
ADDR -- IPv4 address/subnet in the form ******* or *******/*************

In the unlikely scenario of an extremely long-running OpenVPN server,
CID and KID should be assumed to recycle to 0 after (2^32)-1, however this
recycling behavior is guaranteed to be collision-free.

Command Parsing
---------------

The management interface uses the same command line lexical analyzer
as is used by the OpenVPN config file parser.

Whitespace is a parameter separator.

Double quotation or single quotation characters ("", '') can be used
to enclose parameters containing whitespace.

Backslash-based shell escaping is performed, using the following
mappings, when not in single quotations:

\\       Maps to a single backslash character (\).
\"       Pass a literal doublequote character ("), don't
         interpret it as enclosing a parameter.
\[SPACE] Pass a literal space or tab character, don't
         interpret it as a parameter delimiter.

Challenge/Response Protocol
---------------------------

The OpenVPN Challenge/Response Protocol allows an OpenVPN server to
generate challenge questions that are shown to the user, and to see
the user's responses to those challenges.  Based on the responses, the
server can allow or deny access.

The protocol can be used to implement multi-factor authentication
because the user must enter an additional piece of information,
in addition to a username and password, to successfully authenticate.
In multi-factor authentication, this information is used to prove
that the user possesses a certain key-like device such as
cryptographic token or a particular mobile phone.

Two variations on the challenge/response protocol are supported:
the "static" and "dynamic" protocols:

 * The static protocol uses OpenVPN's "--static-challenge" option.

 * The dynamic protocol does not involve special OpenVPN options
   or actions. It is an agreement between the auth-user-pass
   verification process on the server and the management interface
   client to use custom strings that begin with "['CRV1" in
   "Verification Failed" messages. (The "[" character and a matching
   "]" character at the end of the message are added by the client
   OpenVPN program, and are not present in the string generated by the
   auth-user-pass verification process or in the string sent by the
   server.)

Dynamic protocol:

The OpenVPN dynamic challenge/response protocol works by returning
a specially formatted error message after initial successful
authentication.  The error message has two purposes:

 1. It causes OpenVPN to restart the connection attempt.

 2. It contains information about the challenge, which should be used
    to construct the response to the next authentication request (which
    will occur after the restart).

Notes:

 * '--auth-retry interact' must be in effect so that the
   connection is restarted and credentials are requested again.

 * '--auth-retry none' (which is the default) will cause
  OpenVPN to exit with a fatal error without retrying and the dynamic
  challenge/response will never happen because "Need 'Auth'
  username/password" will not be sent.

The error message is formatted as follows:

   >PASSWORD:Verification Failed: 'Auth' ['CRV1:<flags>:<state_id>:<username_base64>:<challenge_text>']

<flags>: a series of optional, comma-separated flags:
 E : echo the response when the user types it.
 R : a response is required.

<state_id>: an opaque string that should be returned to the server
          along with the response.

<username_base64>: the username encoded as base 64.

<challenge_text>: the challenge text to be shown to the user.

<state_id> may not contain colon characters (":"), but <challenge_text>
may.

Example challenge:

  CRV1:R,E:Om01u7Fh4LrGBS7uh0SWmzwabUiGiW6l:Y3Ix:Please enter token PIN

The next time the username and password are requested with

   >PASSWORD:Need 'Auth' username/password

the management interface client should display the challenge text and,
if the R flag is specified, get a response from the user. The management
interface client should respond:

   username "Auth" <username>
   password "Auth" CRV1::<state_id>::<response_text>

Where <username> is the username decoded from <username_base64>,
<state_id> is taken from the challenge request, and <response_text>
is what the user entered in response to the challenge, which can be an
empty string.  If the R flag is not present, <response_text> should
be an empty string.

(As in all username/password responses described in the "COMMAND --
password and username" section above, the username and/or password
can be in quotes, and special characters such as double quotes or
backslashes must be escaped. See the "Command Parsing" section above
for more info.)

Example response (suppose the user enters "8675309" for the token PIN):

   username "Auth" cr1
   password "Auth" CRV1::Om01u7Fh4LrGBS7uh0SWmzwabUiGiW6l::8675309

("Y3Ix" is the base 64 encoding of "cr1".)

Static protocol:

The static protocol differs from the dynamic protocol in that the
challenge question is sent to the management interface client in a
a username/password request, and the username, password, and
response are delivered back to the server in response to that
request.

OpenVPN's --static-challenge option is used to provide the
challenge text to OpenVPN and indicate whether or not the response
should be echoed and how the response should be combined with the
password.

When credentials are needed and the --static-challenge option is
used, the management interface will send:

  >PASSWORD:Need 'Auth' username/password SC:<flag>,<TEXT>

  flag: an integer whose least significant bit is the ECHO flag and
        the next significant bit is the FORMAT flag.
        ECHO = (flag & 0x1) is 1 if response should be echoed, 0 to not echo
        FORMAT = (flag & 0x2) is 1 if response should be concatenated with
        password as plain text, 0 if response and password should be encoded
        as described below. Thus flag could take values 0, 1, 2, or 3.
  TEXT: challenge text that should be shown to the user to
      facilitate their response

For example:

  >PASSWORD:Need 'Auth' username/password SC:1,Please enter token PIN

The above notification indicates that OpenVPN needs a --auth-user-pass
username and password plus a response to a static challenge ("Please
enter token PIN"). The "1" after the "SC:" indicates that the response
should be echoed.

The management interface client in this case should add the static
challenge text to the auth dialog followed by a field for the user to
enter a response. If flag = 0 or 1 (i.e., FORMAT=0), the management
interface client should pack the password and response together into
an encoded password and send:

  username "Auth" <username>
  password "Auth" "SCRV1:<password_base64>:<response_base64>"

Where <username> is the username entered by the user, <password_base64>
is the base 64 encoding of the password entered by the user, and
<response_base64> is the base 64 encoding of the response entered by
the user. The <password_base64> and/or the <response_base64> can be
empty strings.

If flag = 2 or 3 (i.e., FORMAT=1), the client should simply concatenate
password and response with no separator and send:

  username "Auth" <username>
  password "Auth" "<password><response>"

(As in all username/password responses described in the "COMMAND --
password and username" section above, the username can be in quotes,
and special characters such as double quotes or backslashes must be
escaped. See the "Command Parsing" section above for more info.)

For example, if user "foo" entered "bar" as the password and 8675309
as the PIN, the following management interface commands should be
issued if flag = 0 or 1 (i.e., FORMAT = 0):

  username "Auth" foo
  password "Auth" "SCRV1:YmFy:ODY3NTMwOQ=="

  ("YmFy" is the base 64 encoding of "bar" and "ODY3NTMwOQ==" is the
   base 64 encoding of "8675309".)

or, if flag = 2 or 3 (i.e., FORMAT = 1):

  username "Auth" foo
  password "Auth" "bar8675309"
