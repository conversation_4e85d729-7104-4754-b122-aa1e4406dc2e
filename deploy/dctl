#! /bin/sh
image_version="1.0-SNAPSHOT"
# 公共配置信息
port=8080
jvm_opts="-Xms256m -Xmx2048m"
container_name=vpnmgr
jar_file=vpnmgr.jar
image_name=vpnmgr
image=$image_name:$image_version
profile="prd"

cd "$(dirname "$0")" || exit
work_dir=$(pwd)
config_dir="$work_dir/config"
log_dir="$work_dir/logs"

create() {
  if [ ! -d "$log_dir" ]; then
    mkdir "$log_dir"
  fi

  docker run \
    --restart always \
    --name $container_name \
    -p "$port":8080 \
    -e JVM_OPTS="$jvm_opts" \
    -e SPRING_PROFILE="$profile" \
    -v "$config_dir":/config \
    -v "$log_dir":/logs \
    -d $image
}

build() {
  cd src || exit
  docker build -t $image --build-arg JAR_FILE=$jar_file .
}

start() {
  docker start $container_name
}

stop() {
  docker stop $container_name
}

ps() {
  docker ps -a | grep $container_name
}

restart() {
  docker restart $container_name
}

rm() {
  docker stop $container_name
  docker rm $container_name
}

rmi() {
  docker rmi $image
}

log() {
  docker logs -f $container_name --tail=200
}

shell() {
  docker exec -it $container_name bash
}

images(){
  docker images | grep $image_name
}

help() {
  echo "build     build docker image"
  echo "create    create docker container"
  echo "start     start docker container"
  echo "stop      stop docker container"
  echo "restart   restart docker container"
  echo "ps        ps docker container $container_name"
  echo "rm        stop and remove docker container"
  echo "rmi       remove docker image"
  echo "log       docker logs"
  echo "shell     docker exec -it $container_name bash"
}

case "$1" in
build)
  echo "build docker image $image"
  build
  ;;
create)
  echo "create container $container_name"
  create
  ;;
start)
  echo "start container $container_name"
  start
  ;;
stop)
  echo "stop container $container_name"
  stop
  ;;
restart)
  echo "restart container $container_name"
  restart
  ;;
ps)
  echo "ps docker container $container_name"
  ps
  ;;
rm)
  echo "rm container $container_name"
  rm
  ;;
rmi)
  echo "rmi image $image"
  rmi
  ;;
log)
  log
  ;;
shell)
  shell
  ;;
images)
  images
  ;;
*)
  help
  exit 1
  ;;
esac
exit 0
