#!/bin/sh
version="0.1-SNAPSHOT"
jar_name=vpnmgr-$version.jar
profiles=prd
jvm_opts="-Xms512m -Xmx2048m"
log_file=nohup.log

cd "$(dirname "$0")" || exit
work_dir=$(pwd)

# shellcheck disable=SC2009
pid=$(ps -ef | grep -v "grep" | grep "$work_dir/$jar_name" | awk '{print $2}')

# shellcheck disable=SC2009
ps=$(ps -ef | grep -v "grep" | grep "$work_dir/$jar_name")

start() {
  # shellcheck disable=SC2086
  nohup java -jar $jvm_opts \
    -Dfile.encoding=UTF-8 \
    -Djava.security.egd=file:/dev/./urandom \
    "$work_dir/$jar_name" \
    --server.shutdown=graceful \
    --spring.profiles.active=$profiles >$log_file 2>&1 &
}

stop() {
  kill "$pid"
}

restart() {
  kill -9 "$pid"
  start
}

ps() {
  echo "$ps"
}

log() {
  tail -f -200 $log_file
}

help() {
  echo "start     start jar"
  echo "stop      stop jar"
  echo "ps        ps -ef"
  echo "log       tail -f"
}

case "$1" in
start)
  start
  ;;
stop)
  stop
  ;;
restart)
  restart
  ;;
ps)
  ps
  ;;
log)
  log
  ;;
*)
  help
  exit 1
  ;;
esac
exit 0
