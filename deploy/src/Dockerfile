FROM azul/zulu-openjdk-debian:17.0.3-jre
LABEL maintainer="cn.sh.ideal"
ARG JAR_FILE
ADD ${JAR_FILE} application.jar
RUN mkdir /config \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && cp /usr/lib/jvm/zulu17/bin/java /usr/lib/jvm/zulu17/bin/vpnmgr \
    && ln -s /usr/lib/jvm/zulu17/bin/vpnmgr /usr/bin/vpnmgr
EXPOSE 8080
ENV JVM_OPTS=""
ENV SPRING_PROFILE="docker"
ENTRYPOINT ["sh","-c","exec vpnmgr -jar $JVM_OPTS -Dfile.encoding=UTF-8 -Djava.security.egd=file:/dev/./urandom /application.jar --server.shutdown=graceful --server.port=8080 --spring.profiles.active=$SPRING_PROFILE"]
