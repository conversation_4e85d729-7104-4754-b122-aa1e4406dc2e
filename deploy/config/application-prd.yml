spring:
  datasource:
    url: "***********************************************************************************************************************************************************************************************************"
    username: openvpn
    password: ,admin@WSX
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimumIdle: 0
      maximum-pool-size: 10
      connection-timeout: 5000
      validation-timeout: 5000
      max-lifetime: 1800000
      idle-timeout: 600000
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    open-in-view: true
openvpn:
  cer-path: /root/
  base-dir: /etc/openvpn
  server-dir: ${openvpn.base-dir}/server
  server-conf-path: ${openvpn.server-dir}/server.conf
  client-template-path: ${openvpn.server-dir}/client-common.txt
  client-save-dir: /etc/openvpn_mgr/client
  easy-rsa:
    base-dir: ${openvpn.server-dir}/easy-rsa
    run-file-path: ./easyrsa