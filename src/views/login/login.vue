<template>
  <div class="body">
    <!-- 账号密码登录 -->
    <div class="car">
      <div>
        <div style="text-align: center;;margin:15px;color:#3a79f1;font-size:24px">
          账号密码登录
        </div>
        <el-form ref="form" :model="form" label-width="50px">
          <el-form-item label="账号">
            <el-input v-model="form.userName" />
          </el-form-item>
          <el-form-item label="密码">
            <el-input  @keyup.enter="loginto" type="password" v-model="form.passwd" />
          </el-form-item>
        </el-form>
        <el-button style="width:100%" type="primary" round @click="loginto">
          登录
        </el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Login',
  data () {
    return {
      form: {
        userName: '',
        passwd: '',
      },
    }
  },
  methods: {
    loginto () {
      this.$store.dispatch('loginUser', this.form).then((res) => {
        console.log(res, 'res')
        if (res.message === 'ok' && res.data) {
          this.$router.push('/ideal')/* Bearer  */
          this.$store.commit('SET_HEADERS', { 'X-Auth-Metadata': `${res.data}` })
          console.log(this.$store, 'this.form', this.form)
          //
        } else {
          this.$message.error(res.message)
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.body {
  background-color: rgb(82, 80, 80);
  width: 100%;
  .car {
    box-sizing: border-box;
    padding: 24px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border: 1px solid white;
    background-color: white;
    width: 20%;
    height: 25%;
    min-width: 400px;
    min-height: 250px;
    border-radius: 12px;
  }
}
</style>
