<template>
  <el-card class="user-info">
    <el-form ref="searchForm" :model="search" label-width="70px" inline @submit.prevent="loadingList" class="search">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="用户名" prop="userName">
            <el-input v-model="search.userName" placeholder="用户名" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="用户编码" prop="userCode">
            <el-input v-model="search.userCode" placeholder="用户编码" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="手机号" prop="mobilePhone">
            <el-input v-model="search.mobilePhone" placeholder="手机号" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态" prop="statusCd">
            <el-select v-model="search.statusCd" placeholder="状态">
              <el-option v-for="item in statusCdList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="用户类型" prop="type">
            <el-select v-model="search.type" placeholder="用户类型">
              <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="18" style="text-align:right">
          <el-form-item>
            <el-button type="primary" @click="create" v-ripple>
              创建
            </el-button>
            <el-button type="primary" @click="loadingList" v-ripple>
              搜索
            </el-button>
            <el-button type="primary" @click="searchForm.resetFields && searchForm.resetFields(); loadingList()" v-ripple>
              重置
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-divider content-position="left">
      用户列表
    </el-divider>
    <el-table :data="userList" height="calc(100vh - 328px)" stripe v-loading="loading">
      <el-table-column show-overflow-tooltip prop="userName" label="姓名" min-width="120" />
      <el-table-column show-overflow-tooltip prop="mobilePhone" label="电话" min-width="120" />
      <el-table-column show-overflow-tooltip prop="mail" label="邮箱" min-width="120" />
      <el-table-column show-overflow-tooltip prop="type" label="用户类型" width="90" :formatter="(r, c, v) => v === 'Person' ? '人员' : v === 'Gateway' ? '网关' : '其他'" />
      <el-table-column show-overflow-tooltip prop="createTime" label="创建时间" min-width="120" />
      <el-table-column show-overflow-tooltip prop="userCode" label="用户编码" min-width="120" />
      <el-table-column show-overflow-tooltip prop="bindIp" label="绑定IP" min-width="120" />
      <el-table-column show-overflow-tooltip prop="statusCd" label="状态" width="75" :filters="[{ text: '有效', value: 1 }, { text: '冻结', value: 0 }]" :filter-method="filterHandler" :formatter="formatterStatusCd" />
      <!-- <el-table-column show-overflow-tooltip prop="id" label="ID" min-width="220" /> -->
      <el-table-column label="操作" width="280" align="center" fixed="right">
        <template #default="{ row }">
          <el-link v-if="row.statusCd === 1" type="primary" :underline="false" style="margin: 0 6px 0 6px;" @click="edit(row)">
            编辑
          </el-link>
          <el-popconfirm v-if="row.statusCd === 1" title="确认要冻结用户吗?" width="240" @confirm="remove(row)">
            <template #reference>
              <el-link type="danger" :underline="false" style="margin: 0 6px 0 6px;">
                冻结
              </el-link>
            </template>
          </el-popconfirm>
          <el-popconfirm v-if="row.statusCd === 0" title="确认要激活用户吗?" width="240" @confirm="userActive(row)">
            <template #reference>
              <el-link type="primary" :underline="false" style="margin: 0 6px 0 6px;">
                激活
              </el-link>
            </template>
          </el-popconfirm>
          <el-popconfirm v-if="row.statusCd === 0" title="确认要删除用户吗?" width="240" @confirm="userdel(row)">
            <template #reference>
              <el-link type="primary" :underline="false" style="margin: 0 6px 0 6px;">
                删除
              </el-link>
            </template>
          </el-popconfirm>
          <el-dropdown v-if="row.statusCd === 1" style="vertical-align: middle;" @command="handleCommand($event, row)">
            <el-link type="primary" :underline="false" style="margin: 0 0px 0 6px;line-height: 1.5;">
              更多
              <el-icon class="el-icon--right">
                <Icon.ArrowDown />
              </el-icon>
            </el-link>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="重置密码">
                  <el-link type="primary" :underline="false">
                    重置密码
                  </el-link>
                </el-dropdown-item>
                <el-dropdown-item command="租约表">
                  <el-link type="primary" :underline="false">
                    租约表
                  </el-link>
                </el-dropdown-item>
                <el-dropdown-item command="链接记录">
                  <el-link type="primary" :underline="false">
                    链接记录
                  </el-link>
                </el-dropdown-item>
                <el-dropdown-item command="生成QR码">
                  <el-link type="primary" :underline="false">
                    生成QR码
                  </el-link>
                </el-dropdown-item>
                <el-dropdown-item command="用户配置">
                  <el-link type="primary" :underline="false">
                    用户配置
                  </el-link>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:currentPage="page.pages" :page-sizes="[30, 50, 80, 120]" :page-size="page.sizes" layout="prev, pager, next, ->, jumper, sizes, total" :total="page.total" @size-change="loadingList" @current-change="loadingList" />
    <!-- QR码创建 -->
    <el-dialog
      v-model="dialogVisible"
      title="用户QR码"
      width="30%"
    >
      <div style="text-align: center;color:red;padding:10px 0px;">
        警告:验证完成过后会覆盖之前的QR码信息
      </div>
      <div style="display:flex;justify-content: center">
        <qrcode-vue :value="Mvalue" :size="qcsize" level="H" @click="getnowcod" />
      </div>
      <div style="padding:10px 0px;">
        <el-input v-model="otp" placeholder="请输入验证码" />
      </div>
      <template #footer>
        <span class="dialog-footer" style="display:flex;justify-content:space-between">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="CodeOk">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 重置密码 -->
    <el-dialog
      v-model="dialogVisibles"
      title="重置密码"
      width="30%"
    >
      <div style="padding:10px 0px;">
        <el-input v-model="pwd" placeholder="请输入密码" />
      </div>
      <template #footer>
        <span class="dialog-footer" style="display:flex;justify-content:space-between">
          <el-button @click="dialogVisibles = false">取消</el-button>
          <el-button type="primary" @click="CodeOks">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 用户配置 -->
    <el-dialog
      v-model="userVisible"
      title="用户配置"
      width="60%"
    >
      <template #header="{ titleClass }">
        <div class="my-header">
          <span :class="titleClass">用户配置</span>
          <span style="margin-left:10px;font-size:12px">用户名：{{ useronfigName }}</span>
        </div>
      </template>
      <el-form ref="formRef" :model="userForm" :inline="true" label-width="80px" autocomplete="off">
        <el-form-item label="配置类型" prop="type" :rules="[{ required: true, message: '此项必填！' }]">
          <el-select v-model="userForm.type" placeholder="选择配置类型" style="width: 100%;" @change="typeChange">
            <el-option v-for="item in userTypeList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="资源对象" prop="objectId">
          <el-select v-model="userForm.objectId" placeholder="选择资源对象" style="width: 100%;">
            <el-option v-for="item in userResoutceList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="addUserConfig" v-ripple>
            添加
          </el-button>
          <el-button @click="getUseronfigList" v-ripple>
            刷新
          </el-button>
        </el-form-item>
      </el-form>
      <el-divider content-position="left">
        用户配置
      </el-divider>
      <el-table :data="useronfigList" height="calc(100vh - 428px)" stripe v-loading="loading">
        <el-table-column show-overflow-tooltip prop="type" label="配置类型" min-width="90">
          <template #default="{ row }">
            <span>{{ row.type === "access" ? '访问' : row.type === "access_group" ? '访问组' : row.type === "subnet" ? '子网' : row.type === "subnet_group" ? '子网组' : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="objId" label="资源对象" min-width="120" :formatter="objIdFormatter" />
        <el-table-column label="操作" width="190" align="center" fixed="right">
          <template #default="{ row }">
            <el-link type="primary" @click="delUserConfig(row.id)">
              删除
            </el-link>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer" style="display:flex;justify-content: flex-end;">
          <el-button type="primary" @click="closeUserConfig">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script lang="ts" setup>
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, App, reactive } from 'vue'
import type { Ref } from 'vue'
import moment from 'moment'
import type { TableColumnCtx } from 'element-plus/es/components/table/src/table-column/defaults'
import { useStore } from 'vuex'
import { ElMessage, ElForm } from 'element-plus'
import * as Icon from '@element-plus/icons-vue'
import QrcodeVue from 'qrcode.vue'
import leaseDialog from './leaseDialog'
import recordDialog from './recordDialog'
import syncFormDialog from './syncFormDialog'
interface UserData {
  id: string,
  createTime: string,
  lastUpdatePwdTime: string,
  mail: string,
  mobilePhone: string,
  pwdErrCnt: number,
  statusCd: 1 | 0,
  statusTime: string,
  userCode: string,
  userName: string,
  authMode: string,
  clientName: string,
  bindIp: string,
  type: string,
}

interface Response {
  id: string,
  name: string,
  statusCd: 'V'|'R',
  status: string,
}
const store = useStore()

const userVisible = ref(false)
const useronfigId = ref('')
const useronfigName = ref('')
const useronfigList = ref([])
const userResoutceList = ref([])
const ResoutList = ref([])
const GroupList = ref([])
interface SyncForm {
  userId: string;
  type: string;
  value: string;
  objectId: string;
}
const userForm = reactive<SyncForm>({
  userId: '',
  type: '',
  value: '',
  objectId: '',
})
const userTypeList = ref([
  { name: '访问', value: 'access' },
  { name: '访问组', value: 'access_group' },
  { name: '子网', value: 'subnet' },
  { name: '子网组', value: 'subnet_group' },
])

const objIdFormatter = (row: any) => {
  const rObj:any = ResoutList.value.find(v => v.id === row.objId)
  const gObj:any = GroupList.value.find(v => v.id === row.objId)
  if (row.type === 'access' || row.type === 'subnet') {
    return rObj.name
  } else if (row.type === 'access_group' || row.type === 'subnet_group') {
    return gObj.name
  }
}
async function closeUserConfig () {
  userForm.objectId = ''
  userForm.type = ''
  userVisible.value = false
  loadingList()
}
async function userConfig (row: UserData) {
  userVisible.value = true
  useronfigId.value = row.id
  useronfigName.value = row.userName
  getUseronfigList()
  queryResourceList()
  queryGroupList()
}
function queryResourceList () {
  store.dispatch('queryResourceList').then(
    function ({ exitCode, data, massage }) {
      if (Number(exitCode) === 0) {
        ResoutList.value = data
      } else {
        ElMessage.error(massage || '数据错误！')
      }
    },
  )
}
function queryGroupList () {
  store.dispatch('queryResourceGroup').then(
    function ({ exitCode, data, massage }) {
      if (Number(exitCode) === 0) {
        GroupList.value = data
      } else {
        ElMessage.error(massage || '数据错误！')
      }
    },
  )
}
function typeChange (val: any) {
  userForm.objectId = ''
  if (val === 'access' || val === 'subnet') {
    userResoutceList.value = ResoutList.value
  } else if (val === 'access_group' || val === 'subnet_group') {
    userResoutceList.value = GroupList.value
  } else {
    userResoutceList.value = []
  }
}
function addUserConfig () {
  const data = {
    userId: useronfigId.value,
    type: userForm.type,
    objectId: userForm.objectId,
  }
  store.dispatch('userConfigAdd', data).then(
    function ({ exitCode }) {
      if (Number(exitCode) === 0) {
        ElMessage.success('添加成功！')
        // userForm.objectId = ''
        // userForm.type = ''
        getUseronfigList()
      } else ElMessage.error('添加失败！')
      getUseronfigList()
    },
  )
}
function delUserConfig (id: any) {
  store.dispatch('userConfigDel', { confId: id }).then(
    function ({ exitCode }) {
      if (Number(exitCode) === 0) {
        ElMessage.success('删除成功！')
        // userForm.objectId = ''
        // userForm.type = ''
        getUseronfigList()
      } else ElMessage.error('删除失败！')
    },
  )
}
function getUseronfigList (): void {
  const data = { userId: useronfigId.value }
  store.dispatch('userConfigQuery', data).then(
    function ({ exitCode, data, massage }) {
      if (exitCode === 0) {
        useronfigList.value = data
      } else {
        ElMessage.error(massage || '数据错误！')
      }
    }
  )
}
function userActive ({ id }: UserData) {
  store.dispatch('userActive', { id: id }).then(
    function ({ exitCode }) {
      if (Number(exitCode) === 0) {
        ElMessage.success('激活成功！')
        loadingList()
      } else {
        loadingList()
        ElMessage.error('激活失败！')
      }
    },
  )
}
function userdel ({ id }: UserData) {
  store.dispatch('userdel', { id: id }).then(
    function ({ exitCode }) {
      if (Number(exitCode) === 0) {
        ElMessage.success('删除成功！')
        loadingList()
      } else {
        ElMessage.error('删除失败！')
        loadingList()
      }
    },
  )
}
const statusList = [
  {
    label: '人员',
    value: 'Person',
  },
  {
    label: '网关',
    value: 'Gateway',
  },
]
const statusCdList = [
  {
    label: '有效',
    value: '1',
  },
  {
    label: '冻结',
    value: '0',
  },
]

const qcsize = 200
const codeShow = ref(false)
const Mvalue = ref('')
const nowCode = ref('')
const otp = ref('')
// 生成QR码
const dialogVisible = ref(false)
function craQR (row: UserData) {
  nowCode.value = row.userCode
  otp.value = ''
  store.dispatch('admcQR', row.userCode).then((res) => {
    console.log(res.data, 'res')
    Mvalue.value = res.data
    dialogVisible.value = true
  })
}
// 刷新
function getnowcod () {
  console.log('打印')
  if (nowCode.value) {
    store.dispatch('admcQR', nowCode).then((res) => {
      if (res.exitCode === 0) {
        Mvalue.value = res.data
        console.log('res', res)
        ElMessage.success('刷新成功')
      } else {
        ElMessage.error(res.message)
      }
    })
  }
}
// 验证
function CodeOk () {
  console.log(nowCode, otp, 'resres')
  store.dispatch('selcQR', { userCode: nowCode.value, otp: otp.value }).then((res) => {
    console.log(nowCode, otp, 'res', res)
    if (res.message === '验证完成') {
      ElMessage.success(res.message)
      dialogVisible.value = false
    } else {
      ElMessage.error(res.message)
    }
  })
}
// 重置密码
const dialogVisibles = ref(false)
const pwd = ref('')
const userId = ref('')
function delpwd (row:UserData) {
  pwd.value = ''
  userId.value = row.id
  dialogVisibles.value = true
}
function CodeOks () {
  store.dispatch('rootPwd', { userId: userId.value, pwd: pwd.value }).then((res) => {
    console.log(res.message, 'resMiam')
    if (res.message === '重置密码失败') {
      ElMessage.error(res.message)
    } else {
      ElMessage.success(res.message)
      dialogVisibles.value = false
    }
  })
}
/* <InstanceType<typeof ElForm>> */
const searchForm = ref(document.createElement('form'))

const loading = ref(true)
const search: Ref<{ userName: string, type: string, mobilePhone: string, statusCd: string, userCode: string }> = ref({ type: '', userName: '', mobilePhone: '', statusCd: '', userCode: '' })
const userList: Ref<UserData[]> = ref([])

const page = ref({
  sizes: 30,
  pages: 1,
  total: 0,
})

// onMounted(() => {})
loadingList()
function formatterStatusCd (row: {[key: string]: number}, column: TableColumnCtx<UserData>): '有效'|'冻结' {
  return row[column.property] === 1 ? '有效' : '冻结'
}

function loadingList (): void {
  loading.value = true
  const data = { pageNumber: page.value.pages - 1, pageSize: page.value.sizes }
  if (search.value.type) Object.assign(data, { type: search.value.type })
  if (search.value.userName) Object.assign(data, { userName: search.value.userName })
  if (search.value.mobilePhone) Object.assign(data, { mobilePhone: search.value.mobilePhone })
  if (search.value.statusCd) Object.assign(data, { statusCd: search.value.statusCd })
  if (search.value.userCode) Object.assign(data, { userCode: search.value.userCode })
  store.dispatch('queryUser', data).then(
    function ({ exitCode, data, massage }) {
      setTimeout(() => { loading.value = false }, 200)
      if (exitCode === 0 && data.content instanceof Array) {
        page.value = {
          sizes: data.pageable.pageSize,
          pages: data.pageable.pageNumber + 1,
          total: data.totalElements,
        }
        userList.value = (data.content as UserData[]).map(({ id, userName, statusCd, userCode, mail, mobilePhone, pwdErrCnt, statusTime, createTime, clientName, lastUpdatePwdTime, authMode, bindIp, type }) => ({ id, userName, statusCd, userCode, mail, mobilePhone, pwdErrCnt, statusTime, createTime: moment(createTime).format('YYYY-MM-DD HH:mm'), clientName, lastUpdatePwdTime, authMode, bindIp, type }))
      } else {
        ElMessage.error(massage || '数据错误！')
      }
    },
    function () {
      setTimeout(() => { loading.value = false }, 200)
      // console.log('[ ERROR ]', ...arguments)
    },
  )
}

function download ({ name }: Response) {
  store.dispatch('downConf', { cn: name }).then(
    function ({ data, headers }: { data: Blob, headers: { [key: string]: string } }) {
      const type = headers['content-disposition'] || `attachment;fileName=${name}.ovpn`
      const objectURL = window.URL.createObjectURL(new Blob([data], { type }))
      const link = document.createElement('a')
      link.download = String(type).substring(String(type).indexOf('fileName=') + 9) || `${name}.ovpn`
      link.style.display = 'none'
      link.href = objectURL
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(objectURL)
      loadingList()
      ElMessage.success('开始下载！')
    },
    function () {
      ElMessage.error('下载失败！')
      loadingList()
    },
  )
}

async function create () {
  await syncFormDialog({ $isEdit: false }, async (form: { [key:string]: unknown }) => {
    try {
      const { exitCode, message } = await store.dispatch('createUser', form)
      if (exitCode === 0) {
        ElMessage.success('添加成功')
      } else throw Object.assign(new Error('[ 失败 ]'), { exitCode, message })
      return true
    } catch (error) {
      ElMessage.error((error as Error).message || '添加失败')
      return false
    }
  }, [store as { install: (app: App<any>) => void; }])
  loadingList()
}

function remove ({ id }: UserData) {
  store.dispatch('removeUser', { id: id }).then(
    function ({ exitCode }) {
      if (Number(exitCode) === 0) ElMessage.success(`成功删除${name}！`)
      else ElMessage.error(`${name}删除失败！`)
      loadingList()
    },
    function () {
      // console.log('%c [ ERROR ] => -107', 'font-size:13px; background:pink; color:#bf2c9f;', ...arguments)
      loadingList()
    },
  )
}

async function handleCommand (key: string, row: UserData) {
  switch (key) {
    case '重置密码':
      await delpwd(row)
      break
    case '租约表':
      await catLease(row)
      break
    case '链接记录':
      await catRecord(row)
      break
    case '生成QR码':
      await craQR(row)
      break
    case '用户配置':
      await userConfig(row)
      break
  }
}

async function edit (row: UserData) {
  console.log(row)
  await syncFormDialog({ $isEdit: true, ...row }, async (form: { [key:string]: unknown }) => {
    try {
      const { exitCode } = await store.dispatch('editmoUser', { id: row.id, userName: form.userName, mail: form.mail, mobilePhone: form.mobilePhone, authMode: form.authMode, clientName: form.clientName, bindIp: form.bindIp })
      if (exitCode === 0) {
        ElMessage.success('编辑成功')
      } else throw new Error('[ 失败 ]')
      return true
    } catch (error) {
      ElMessage.error('编辑失败')
      return false
    }
  }, [store as { install: (app: App<any>) => void; }])
  loadingList()
}
async function catLease (row: UserData) {
  console.log(row)
  await leaseDialog({ ...row }, async () => {
    return true
  }, [store as { install: (app: App<any>) => void; }])
  loadingList()
}
async function catRecord (row: UserData) {
  console.log(row)
  await recordDialog({ ...row }, async () => {
    return true
  }, [store as { install: (app: App<any>) => void; }])
  loadingList()
}

function filterHandler (value: string, row: { [k: string]: string }, column: TableColumnCtx<Response>) {
  if (Object.hasOwnProperty.call(column, 'property')) return row[column['property']] === value
  else return false
}

defineExpose({
  codeShow,
  Mvalue,
  qcsize,
  craQR,
  loadingList,
  filterHandler,
  download,
  create,
  remove,
  edit,
  searchForm,

  search,
  userList,
  Array,
})
</script>

<style lang="scss" scoped>
.user-info {
  .search {
    > .right {
      text-align: right;
      margin-left: 12px;
      &::before {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
      &::after {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
    }
  }
  :deep(.el-select .el-input){
    // width: calc(100% - 20px);
  }
}
</style>
