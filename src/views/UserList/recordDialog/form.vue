<template>
  <el-dialog v-model="visible" :title="`${isEdit ? '操作' : '查看'}链接记录`" :draggable="true" :modal="false" :close-on-click-modal="false" :append-to-body="true" width="750px" :before-close="handleCloses">
    <template #default>
      <el-scrollbar max-height="500px" style="margin-top: 18px;">
        <!-- <el-form ref="formRef" :model="form" label-width="80px" autocomplete="off">
          <el-form-item label="name" prop="name" :rules="[]">
            <el-input v-model="form.name" :prefix-icon="Icon.Star" autocomplete="nickname" />
          </el-form-item>
        </el-form> -->
        <el-table :data="table.list" border stripe min-height="300px">
          <!-- <el-table-column show-overflow-tooltip prop="userCode" label="用户编码" /> -->
          <el-table-column show-overflow-tooltip prop="type" label="记录类型" />
          <el-table-column show-overflow-tooltip prop="deviceType" label="设备类型" />
          <el-table-column show-overflow-tooltip prop="trustedAddress" label="出访地址" />
          <el-table-column show-overflow-tooltip prop="clientIp" label="客户端IP" />
          <el-table-column show-overflow-tooltip prop="serverIp" label="服务端IP" />
          <el-table-column show-overflow-tooltip prop="duration" label="时长(s)" />
          <el-table-column show-overflow-tooltip prop="connectTime" label="链接时间" />
          <el-table-column show-overflow-tooltip prop="disconnectTime" label="断开时间" />
        </el-table>
        <el-pagination v-model:current-page="table.page" :page-size="table.size" layout="->, total, prev, pager, next" :total="table.total" @size-change="init(data)" @current-change="init(data)" />
      </el-scrollbar>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="default" :loading="loading" @click="handleCloses(doneFun)">返回</el-button>
        <!-- <el-button type="default" :loading="loading" @click="handleCloses(doneFun)">取消</el-button> -->
        <!-- <el-button type="primary" :loading="loading" @click="handleSubmit(formRef)">提交</el-button> -->
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, Ref, reactive, nextTick, computed } from 'vue'
import { ElPagination, ElTable, ElTableColumn, ElDialog, ElScrollbar, ElForm, ElFormItem, ElInput, ElButton, ElSelect, ElOption, ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Ripple as vRipple } from '@/directives/ripple'
import * as Icon from '@element-plus/icons-vue'
import { useStore, createStore } from 'vuex'
import _ from 'lodash'
import moment from 'moment'
import { Message } from '@element-plus/icons-vue'
interface SyncForm {
  [key: string]: unknown;
  id?: string;
  userCode: string;
  userName: string;
  password: string;
  mail: string;
  mobilePhone: string;
  authMode: string;
  bindIp: string;
  clientName: string;
}
/**
 * TODO: 表单
 */
const form = reactive<SyncForm>({
  userCode: '',
  userName: '',
  password: '',
  mail: '',
  mobilePhone: '',
  authMode: '',
  bindIp: '',
  clientName: '',
})

const props = defineProps({
  $isEdit: {
    type: String,
    default: '$isEdit',
  },
})

/***************************************************************/
interface SyncResponse {
  resolve: (form: SyncForm) => void
  reject: (form: SyncForm) => void
}
const PATTERN = reactive({ IP: /^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))$/, NAME: /^(\w)*$/ })
const data: {[key: string]: unknown} = reactive({})
const formRef = ref<FormInstance>()
const doneFun = () => { visible.value = false }
const store = useStore() || createStore({ state: {}, getters: {}, mutations: {}, actions: {}, modules: {} })
const visible = ref(false)
const loading = ref(false)
let callback: (form: SyncForm) => (Promise<boolean>|boolean) = () => false
const promise: SyncResponse = { resolve: (form: SyncForm) => form, reject: (form: SyncForm) => form }
const isEdit = computed(() => Boolean(data[props.$isEdit]))

/***************************************************************/
interface Response {
  userCode: string;
  deviceType: string;
  trustedAddress: string;
  clientIp: string;
  serverIp: string;
  recordTime: string;
  duration: string;
  disconnectTime: string;
  connectTime: string;
  type: 'CONNECT' | 'DISCONNECT';
}
interface TableData {
  page: number;
  size: number;
  total: number;
  list: Response[];
}
const search = reactive<{[key: string]: unknown}>({
})
const table = reactive<TableData>({
  page: 1,
  size: 10,
  total: 0,
  list: [],
})

// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function init (openData: {[key: string]: unknown}): Promise<{[key: string]: unknown}> {
  try {
    const { data, exitCode, message } = await store.dispatch('clientQueryRecord', { userCode: openData.userCode, pageNumber: table.page - 1, pageSize: table.size })
    if (exitCode === 0) {
      table.page = Number(data.number) + 1
      table.size = Number(data.size)
      table.total = Number(data.totalElements)
      table.list.splice(0, table.list.length, ...(data.content instanceof Array ? data.content : []).map((v: Response) => ({ ...v, connectTime: moment(v.connectTime).format('YYYY-MM-DD HH:mm'), disconnectTime: moment(v.disconnectTime).format('YYYY-MM-DD HH:mm') })))
    } else throw new Error(message)
  } catch (error) {
    ElMessage.error((error as Error).message)
    handleCloses(doneFun)
  }
  return {}
}

/***************************************************************/
async function handleCloses (done?: () => void) {
  finish(promise.reject, form)
  if (typeof done === 'function') done()
  else visible.value = false
}

async function handleSubmit (formRef?: FormInstance) {
  await nextTick()
  try {
    if (formRef) {
      try {
        await formRef.validate()
      } catch (error) {
        throw new Error('[表单填写错误]')
      }
    }
    if (typeof callback === 'function') {
      if (!(await syncRuntime(callback, form))) throw new Error('[ 回调阻止 ]')
    }
    finish(promise.resolve, form)
  } catch (error) {
    console.dir(error)
  }
  loading.value = false
}
async function handleReset () {
  console.log('%c [  ] => -195', 'font-size:13px; background:pink; color:#bf2c9f;', data)
  loading.value = true
  try {
    Object.assign(
      data,
      (await Promise.all([
        new Promise((resolve) => setTimeout(resolve, 500, {})),
        init(data),
      ])).reduce((p: {[key: string]: unknown}, c) => Object.assign(p, c), {})
    )
  } catch (error) {
    ElMessage.error('加载失败，请检查网络！')
    return handleCloses(doneFun)
  }
  for (const key in form) {
    if (Object.prototype.hasOwnProperty.call(form, key)) {
      switch (key) {
        case 'userCode':
          if (Object.prototype.hasOwnProperty.call(data, key)) Object.assign(form, { [key]: data[key] })
          else Object.assign(form, { [key]: '' })
          break
        default:
          if (Object.prototype.hasOwnProperty.call(data, key)) Object.assign(form, { [key]: data[key] })
          else Object.assign(form, { [key]: null })
          break
      }
    }
  }
  loading.value = false
}
async function syncRuntime (_callback: (form: SyncForm) => (Promise<boolean>|boolean), _form: SyncForm): Promise<boolean> {
  const syncData = _callback(_form)
  if (syncData instanceof Promise) {
    try {
      return Boolean(await syncData)
    } catch (error) {
      return Boolean(error)
    }
  } else return false
}
async function finish (_callback: (form: SyncForm) => void, _form: SyncForm) {
  visible.value = false
  _callback(_.cloneDeep(_form))
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) delete data[key]
  }
  Object.assign(promise, { resolve: (form: SyncForm) => form, reject: (form: SyncForm) => form })
  callback = () => false
}
defineExpose({
  open (_data: {[key: string]: string}, _callback: (form: SyncForm) => (Promise<boolean>|boolean)) {
    visible.value = true
    loading.value = true
    callback = _callback
    Object.assign(data, _data)
    return new Promise((resolve, reject) => {
      Object.assign(promise, { resolve, reject })
      nextTick(() => {
        handleReset().then(
          async () => { /*  */ },
          () => { handleCloses(doneFun) },
        )
      })
    })
  },
})
</script>

<style lang="scss" scoped>
</style>
