<template>
  <el-card class="user-info">
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane label="客户端链接状态" name="state">
        <StateTable ref="StateTable1" :tabs="activeName" />
      </el-tab-pane>
      <el-tab-pane label="客户端链接记录" name="takeNodes">
        <TakeNodesTable :tabs="activeName" />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import StateTable from './stateTable.vue'
import TakeNodesTable from './takeNodesTable.vue'

const activeName = ref('state')

defineExpose({})
</script>

<style lang="scss" scoped>
.user-info {
  .search {
    > .right {
      margin-left: 12px;
      float: right;
      &::before {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
      &::after {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
    }
  }
}
.box-card {
  :deep(.el-card__body){
    height: 300px;
  }
}
</style>
