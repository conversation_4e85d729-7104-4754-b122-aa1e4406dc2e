<template>
  <div class="user-info">
    <el-form ref="searchForm" :model="search" label-width="70px" inline @submit.prevent="loadingList" class="search">
      <el-form-item label="用户编码" prop="userCode">
        <el-input v-model="search.userCode" placeholder="用户编码" />
      </el-form-item>
      <el-form-item label="客户端IP" prop="clientIp">
        <el-input v-model="search.clientIp" placeholder="客户端IP" />
      </el-form-item>
      <el-form-item label="连接状态" prop="status">
        <el-select v-model="search.status" placeholder="连接状态">
          <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <div class="right">
        <el-button type="primary" @click="loadingList" v-ripple>
          搜索
        </el-button>
        <el-button type="primary" @click="searchForm.resetFields && searchForm.resetFields(); loadingList()" v-ripple>
          重置
        </el-button>
      </div>
    </el-form>
    <el-divider content-position="left">
      链接状态列表
    </el-divider>
    <el-table :data="resourceList" height="calc(100vh - 328px)" stripe v-loading="loading">
      <el-table-column show-overflow-tooltip prop="userCode" label="用户编码" min-width="120">
        <!-- <template #default="{ row }">
          <el-button @click="catRecord(row)" type="primary" link>
            {{ row.userCode }}
          </el-button>
        </template> -->
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="userName" label="用户名称" min-width="120" />
      <el-table-column show-overflow-tooltip prop="deviceType" label="设备类型" min-width="120" />
      <el-table-column show-overflow-tooltip prop="status" label="状态" min-width="60">
        <template #default="{ row }">
          <span :style="{color:row.status === 'CONNECT' ? 'green' : 'red'}">{{ row.status === 'CONNECT' ? '链接' : '断开' }}</span>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="trustedAddress" label="出访地址" min-width="160" />
      <el-table-column show-overflow-tooltip prop="clientIp" label="客户端IP" min-width="120" />
      <el-table-column show-overflow-tooltip prop="serverIp" label="服务端IP" min-width="120" />
      <el-table-column show-overflow-tooltip prop="connectTime" label="链接时间" min-width="140" 
        :formatter="(r, c, v) => moment(v).format('YYYY-MM-DD HH:mm:ss')" />
      <el-table-column show-overflow-tooltip prop="disconnectTime" label="断开时间" min-width="140" 
        :formatter="(r, c, v) => v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '-'" />
    </el-table>
    <el-pagination v-model:currentPage="page.pages" :page-sizes="[30, 50, 80, 120]" :page-size="page.sizes" layout="prev, pager, next, ->, jumper, sizes, total" :total="page.total" @size-change="loadingList" @current-change="loadingList" />
  </div>
</template>

<script lang="ts" setup>
import { App, ref, watch } from 'vue'
import type { Ref } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElForm } from 'element-plus'
import moment from 'moment'
import recordDialog from '../UserList/recordDialog'

const props = defineProps({
  tabs: {
    type: String,
    default: '',
  },
})
watch(
  () => props.tabs,
  (newValue) => {
    if (newValue === 'state') {
      searchForm.value.resetFields()
      page.value = {
        sizes: 30,
        pages: 1,
        total: 0,
      }
      loadingList()
    }
  }
)

const statusList = [
  {
    label: '链接',
    value: 'CONNECT',
  },
  {
    label: '断开',
    value: 'DISCONNECT',
  },
]
interface UserData {
  id: string,
  status: string,
  userCode: string,
  deviceType: string,
  trustedAddress: string,
  clientIp: string,
  serverIp: string,
  connectTime: string,
  disconnectTime: string,
}
const store = useStore()

// async function catRecord (row: UserData) {
//   console.log(row)
//   console.log(recordDialog)
//   await recordDialog({ ...row }, async () => {
//     return true
//   }, [store as { install: (app: App<unknown>) => void; }])
//   loadingList()
// }

/* <InstanceType<typeof ElForm>> */
const searchForm = ref(document.createElement('form'))

const loading = ref(true)
const search: Ref<{ userCode: string, clientIp: string, status: string }> = ref({ userCode: '', clientIp: '', status: '' })
const resourceList: Ref<UserData[]> = ref([])

const page = ref({
  sizes: 30,
  pages: 1,
  total: 0,
})
loadingList()
function loadingList (): void {
  loading.value = true
  const data = { pageNumber: page.value.pages - 1, pageSize: page.value.sizes }
  if (search.value.userCode) Object.assign(data, { userCode: search.value.userCode })
  if (search.value.clientIp) Object.assign(data, { clientIp: search.value.clientIp })
  if (search.value.status) Object.assign(data, { status: search.value.status })
  store.dispatch('clientQueryStatus', data).then(
    function ({ exitCode, data, massage }) {
      setTimeout(() => { loading.value = false }, 200)
      if (exitCode === 0 && data.content instanceof Array) {
        page.value = {
          sizes: data.pageable.pageSize,
          pages: data.pageable.pageNumber + 1,
          total: data.totalElements,
        }
        resourceList.value = (data.content as UserData[])
      } else {
        ElMessage.error(massage || '数据错误！')
      }
    },
    function () {
      setTimeout(() => { loading.value = false }, 200)
      // console.log('[ ERROR ]', ...arguments)
    },
  )
}

defineExpose({
  loadingList,
  searchForm,
  search,
  resourceList,
  Array,
})
</script>

<style lang="scss" scoped>
.user-info {
  .search {
    > .right {
      margin-left: 12px;
      float: right;
      &::before {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
      &::after {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
    }
  }
}
.box-card {
  :deep(.el-card__body){
    height: 300px;
  }
}
</style>
