<template>
  <div class="user-info">
    <el-form ref="searchForm" :model="search" label-width="90px" inline @submit.prevent="loadingList" class="search">
      <el-form-item label="用户编码" prop="userCode">
        <el-input v-model="search.userCode" placeholder="用户编码" />
      </el-form-item>
      <!--  value-format="YYYY-MM-DD" -->
      <!-- <el-form-item label="链接时间" prop="connTime">
        <el-date-picker v-model="search.connTime" type="date" placeholder="链接时间" size="default" />
      </el-form-item> -->
      <div class="right">
        <el-button type="primary" @click="loadingList" v-ripple>
          搜索
        </el-button>
        <el-button type="primary" @click="searchForm.resetFields && searchForm.resetFields(); loadingList()" v-ripple>
          重置
        </el-button>
      </div>
    </el-form>
    <el-divider content-position="left">
      链接记录列表
    </el-divider>
    <el-table :data="resourceList" height="calc(100vh - 328px)" stripe v-loading="loading">
      <el-table-column show-overflow-tooltip prop="userCode" label="用户编码" min-width="120" />
      <el-table-column show-overflow-tooltip prop="userName" label="用户姓名" min-width="150" />
      <el-table-column show-overflow-tooltip prop="sessionId" label="会话ID" min-width="150" />
      <el-table-column show-overflow-tooltip prop="type" label="记录类型" min-width="80">
        <template #default="{ row }">
          <span :style="{color:getStatusColor(row.type)}">{{ getStatusText(row.type) }}</span>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="deviceType" label="设备类型" min-width="80" />
      <el-table-column show-overflow-tooltip prop="trustedAddress" label="出访地址" min-width="150" />
      <el-table-column show-overflow-tooltip prop="clientIp" label="客户端IP" min-width="100" />
      <!-- <el-table-column show-overflow-tooltip prop="serverIp" label="服务端IP" min-width="100" /> -->
      <el-table-column show-overflow-tooltip prop="duration" label="链接时长" min-width="100" />
      <el-table-column show-overflow-tooltip prop="connectTime" label="链接时间" min-width="140" 
      :formatter="(r, c, v) => moment(v).format('YYYY-MM-DD HH:mm:ss')" />
      <el-table-column show-overflow-tooltip prop="disconnectTime" label="断开时间" min-width="140" 
      :formatter="(r, c, v) => v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '-'" />
    </el-table>
    <el-pagination v-model:currentPage="page.pages" :page-sizes="[30, 50, 80, 120]" :page-size="page.sizes" layout="prev, pager, next, ->, jumper, sizes, total" :total="page.total" @size-change="loadingList" @current-change="loadingList" />
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import type { Ref } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElForm } from 'element-plus'
import moment from 'moment'

const props = defineProps({
  tabs: {
    type: String,
    default: '',
  },
})
watch(
  () => props.tabs,
  (newValue) => {
    if (newValue === 'takeNodes') {
      searchForm.value.resetFields()
      page.value = {
        sizes: 30,
        pages: 1,
        total: 0,
      }
      loadingList()
    }
  }
)

interface UserData {
  id: string,
  name: string,
  desc: string,
}
const store = useStore()

/* <InstanceType<typeof ElForm>> */
const searchForm = ref(document.createElement('form'))

const loading = ref(true)
const search: Ref<{ userCode: string, connTime: string }> = ref({ name: '', connTime: '' })
const resourceList: Ref<UserData[]> = ref([])

const page = ref({
  sizes: 30,
  pages: 1,
  total: 0,
})
loadingList()
function loadingList (): void {
  loading.value = true
  const data = { pageNumber: page.value.pages - 1, pageSize: page.value.sizes }
  if (search.value.userCode) Object.assign(data, { userCode: search.value.userCode })
  if (search.value.connTime) Object.assign(data, { connTime: search.value.connTime })
  store.dispatch('clientQueryRecord', data).then(
    function ({ exitCode, data, massage }) {
      setTimeout(() => { loading.value = false }, 200)
      if (exitCode === 0 && data.content instanceof Array) {
        page.value = {
          sizes: data.pageable.pageSize,
          pages: data.pageable.pageNumber + 1,
          total: data.totalElements,
        }
        resourceList.value = (data.content as UserData[])
      } else {
        ElMessage.error(massage || '数据错误！')
      }
    },
    function () {
      setTimeout(() => { loading.value = false }, 200)
    },
  )
}
const colorMap = {
        'AUTH_FAILED': {
          'color': 'red',
          'name': '认证失败',
        },
        'ESTABLISHED': {
          'color': 'green',
          'name': '连接成功',
        },
        'RECONNECTED': {
          'color': 'orange',
          'name': '断线重连',
        },
        'DISCONNECTED': {
          'color': 'grey',
          'name': '断开连接',
        },
      };

function getStatusText(status: keyof typeof colorMap) {
  return colorMap[status].name || '未知状态'
}

function getStatusColor(status: keyof typeof colorMap) {
  return colorMap[status].color || 'black'
}

defineExpose({
  loadingList,
  searchForm,
  search,
  resourceList,
  Array,
})
</script>

<style lang="scss" scoped>
.user-info {
  .search {
    > .right {
      margin-left: 12px;
      float: right;
      &::before {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
      &::after {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
    }
  }
}
.box-card {
  :deep(.el-card__body){
    height: 300px;
  }
}
</style>
