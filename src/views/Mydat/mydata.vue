<template>
  <el-card class="user-info">
    <div>
      <el-button type="primary" @click="craQR">
        生成QR码
      </el-button>
    </div>
    <div style="display: flex;justify-content: space-between;margin:15px 0">
      <div>用户编码:<span style="margin-left: 10px;color:#3a79f1">{{ userinfo.userCode }}</span></div>
      <div>用户姓名:<span style="margin-left: 10px;color:#3a79f1">{{ userinfo.userName }}</span></div>
      <div>用户邮箱:<span style="margin-left: 10px;color:#3a79f1">{{ userinfo.mail }}</span></div>
      <div>用户手机号:<span style="margin-left: 10px;color:#3a79f1">{{ userinfo.mobilePhone }}</span></div>
      <div>绑定证书:<span style="margin-left: 10px;color:#3a79f1"><el-link :underline="false" href="javascript:void(0)" style="vertical-align: bottom;" @click="downConf(userinfo.downConf)">下载</el-link></span></div>
      <div>用户状态:<span style="margin-left: 10px;color:#3a79f1">{{ statusCd[userinfo.statusCd] }}</span></div>
    </div>
    <el-table :data="table.list" border stripe min-height="300px">
      <el-table-column show-overflow-tooltip prop="userCode" label="用户编码" />
      <el-table-column show-overflow-tooltip prop="deviceType" label="操作系统" />
      <el-table-column show-overflow-tooltip prop="trustedAddress" label="出访地址" />
      <el-table-column show-overflow-tooltip prop="clientIp" label="客户端IP" />
      <el-table-column show-overflow-tooltip prop="serverIp" label="服务端IP" />
      <el-table-column show-overflow-tooltip prop="recordTime" label="记录时间" />
      <el-table-column show-overflow-tooltip prop="duration" label="时长(秒)" />
      <el-table-column show-overflow-tooltip prop="type" label="类型" width="60" :formatter="(_row, _col, value) => new Map([['CONNECT', '连接'], ['DISCONNECT', '断开']]).get(value)" />
    </el-table>
    <el-pagination v-model:current-page="table.page" :page-size="table.size" layout="->, total, prev, pager, next" :total="table.total" @size-change="init" @current-change="init" />

    <!-- QR码创建 -->
    <el-dialog v-model="dialogVisible" title="用户QR码" width="30%">
      <div style="text-align: center;color:red;padding:10px 0px;">
        警告:验证完成过后会覆盖之前的QR码信息
      </div>
      <div style="display:flex;justify-content: center">
        <qrcode-vue :value="Mvalue" :size="qcsize" level="H" @click="getnowcod" />
      </div>
      <div style="padding:10px 0px;">
        <el-input v-model="otp" placeholder="请输入验证码" />
      </div>
      <template #footer>
        <span class="dialog-footer" style="display:flex;justify-content:space-between">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="verifyQrCode">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script>
import QrcodeVue from 'qrcode.vue'
import { ElMessage } from 'element-plus'
import moment from 'moment'
export default {
  name: 'MyData',
  data () {
    return {
      userinfo: {},
      dialogVisible: false,
      Mvalue: '',
      qcsize: 200,
      otp: '',
      statusCd: {
        '0': '失效',
        '1': '生效',
      },
      search: {
        connTime: null,
      },
      table: {
        page: 1,
        size: 10,
        total: 0,
        list: [],
      },
    }
  },
  components: {
    QrcodeVue,
  },
  computed: {
  },
  methods: {
    getnowcod () {
      this.$store.dispatch('createQR').then((res) => {
        console.log('res', res.data)
        if (res.data) {
          this.Mvalue = res.data
          this.otp = ''
          this.$message.success('刷新成功')
        } else {
          this.$message.error('刷新失败')
        }
      })
    },
    craQR () {
      this.otp = ''
      this.$store.dispatch('createQR').then((res) => {
        console.log(res.data, 'res')
        this.Mvalue = res.data
        this.dialogVisible = true
      })
    },
    verifyQrCode () {
      this.$store.dispatch('verifyQrCode', this.otp).then((res) => {
        console.log('res', res)
        if (res.message === '验证失败') {
          this.$message.error(res.message)
          this.getnowcod()
        } else {
          this.$message.success(res.message)
          this.dialogVisible = false
        }
      })
    },
    downConf (userCode) {
      this.$store.dispatch('downCurrentUserConf', {}).then(
        function ({ data, headers, exitCode }) {
          console.log('%c [ data, headers, exitCode ] => -92', 'font-size:13px; background:pink; color:#bf2c9f;', data, headers['content-type'], exitCode)
          if (Number(exitCode) === 200) {
            const type = headers['content-disposition'] || `attachment;fileName=${userCode}.ovpn`
            const objectURL = window.URL.createObjectURL(new Blob([data], { type }))
            const link = document.createElement('a')
            link.download = String(type).substring(String(type).indexOf('fileName=') + 9)
            link.style.display = 'none'
            link.href = objectURL
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            window.URL.revokeObjectURL(objectURL)
            ElMessage.success('开始下载！')
          } else {
            ElMessage.error('下载失败！')
            return Promise.reject(Object.assign(new Error('[ 下载失败 ]'), { data, headers, exitCode }))
          }
        },
        function () {
          ElMessage.error('下载失败！')
        }
      )
    },
    init () {
      this.$store.dispatch('selUser', {}).then((res) => {
        // console.log('res用户信息', res.data)
        this.userinfo = res.data
        this.$store.dispatch('getRecord', { userCode: res.data.userCode, ...(this.search.connTime instanceof Array ? { connTime: this.search.connTime } : {}), pageNumber: this.table.page - 1, pageSize: this.table.size }).then(({ data }) => {
          this.table.list = (data.content instanceof Array ? data.content : []).map((v) => ({ ...v, recordTime: moment(v.recordTime).format('YYYY-MM-DD HH:mm') }))
          this.table.page = Number(data.number) + 1
          this.table.size = Number(data.size)
          this.table.total = Number(data.totalElements)
        })
      })
    },
  },
  created () {
    this.init()
  },
}
</script>
<style lang="scss" scoped>
.user-info {
  min-height: 500px;
}
</style>
