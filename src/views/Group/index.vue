<template>
  <el-card class="user-info">
    <el-form ref="searchForm" :model="search" label-width="90px" inline @submit.prevent="loadingList" class="search">
      <el-form-item label="资源组名称" prop="name">
        <el-input v-model="search.name" placeholder="资源组名称" />
      </el-form-item>
      <div class="right">
        <el-button type="primary" @click="create" v-ripple>
          创建
        </el-button>
        <el-button type="primary" @click="loadingList" v-ripple>
          搜索
        </el-button>
        <el-button type="primary" @click="searchForm.resetFields && searchForm.resetFields(); loadingList()" v-ripple>
          重置
        </el-button>
      </div>
    </el-form>
    <el-divider content-position="left">
      资源组列表
    </el-divider>
    <el-table :data="resourceList" height="calc(100vh - 280px)" stripe v-loading="loading">
      <el-table-column show-overflow-tooltip prop="id" label="资源组编码" min-width="120" />
      <el-table-column show-overflow-tooltip prop="name" label="资源组名称" min-width="120" />
      <el-table-column show-overflow-tooltip prop="desc" label="资源组描述" min-width="120" />
      <el-table-column label="操作" width="190" align="center" fixed="right">
        <template #default="{ row }">
          <el-link type="primary" :underline="false" style="margin: 0 6px 0 6px;" @click="check(row)">
            资源组详情
          </el-link>
          <el-link type="primary" :underline="false" style="margin: 0 6px 0 6px;" @click="edit(row)">
            编辑
          </el-link>
          <el-popconfirm title="确认要删除此资源组吗?删除后将无法恢复." width="240" @confirm="remove(row)">
            <template #reference>
              <el-link type="danger" :underline="false" style="margin: 0 6px 0 6px;">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:currentPage="page.pages" :page-sizes="[30, 50, 80, 120]" :page-size="page.sizes" layout="prev, pager, next, ->, jumper, sizes, total" :total="page.total" @size-change="loadingList" @current-change="loadingList" />
    <el-dialog v-model="dialogVisible" title="资源组详情" width="400px">
      <el-space direction="vertical">
        <el-card class="box-card" style="width: 350px" v-loading="resourceGroupLoading">
          <template #header>
            <div class="card-header">
              <span style="font-size:16px;">新增资源</span>
              <div style="display: flex;justify-content: space-between;margin-top:5px">
                <el-select v-model="resourceId" placeholder="选择资源" style="width: 80%;" name="groupId" @change="changeSelect">
                  <el-option v-for="item in resourceSelectList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
                <el-button text bg @click="handleAddResource" :disabled="resourceId ? false : true">
                  新增
                </el-button>
              </div>
            </div>
          </template>
          <span style="font-size:16px;">资源组已有资源</span>
          <div v-for="item in resourceGroupList" :key="item.id" class="text item">
            <div style="display: flex;justify-content: space-between;margin-top:15px">
              <span>{{ item.name }}</span>
              <el-button size="small" type="danger" :icon="Delete" circle @click="handleDelResource(item.id)" />
            </div>
          </div>
        </el-card>
      </el-space>
    </el-dialog>
  </el-card>
</template>

<script lang="ts" setup>
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, App } from 'vue'
import type { Ref } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElForm } from 'element-plus'
import syncFormDialog from './syncFormDialog'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
interface UserData {
  id: string,
  name: string,
  desc: string,
}
interface ResourceGroupData {
  id: string,
  name: string,
  ip: string,
  netmask: string,
}
const store = useStore()

const dialogVisible = ref(false)
const resourceGroupLoading = ref(false)
const groupId: Ref<string> = ref('')
const resourceId: Ref<string> = ref('')
function check (row: { id: string }):void {
  groupId.value = row.id
  dialogVisible.value = true
  getResourceList(row.id)
  setTimeout(() => {
    getResourceListNo()
  }, 100)
}

function getResourceListNo ():void {
  resourceGroupLoading.value = true
  store.dispatch('queryResourceList').then(
    function ({ exitCode, data, massage }) {
      if (exitCode === 0) {
        resourceAllSelectList.value = data
        resourceSelectList.value = resArr(data, resourceGroupList.value)
        resourceGroupLoading.value = false
      } else {
        ElMessage.error(massage || '数据错误！')
        resourceGroupLoading.value = false
      }
    }
  )
}
function resArr (arr1: any[], arr2: any[]) {
  return arr1.filter((v) => !arr2.find((val) => val.id === v.id))
}

function handleAddResource ():void {
  if (resourceGroupList.value.filter(v => v.id === resourceId.value).length) {
    ElMessage.error('资源组中已存在该资源请重新选择')
    getResourceList(groupId.value)
    getResourceListNo()
  } else {
    const data = {
      groupId: groupId.value,
      resourceId: resourceId.value,
    }
    store.dispatch('addResourceForGroup', data).then(
      async function ({ exitCode, data, massage }) {
        if (exitCode === 0) {
          ElMessage.success('新增成功!')
          resourceGroupLoading.value = true
          await getResourceList(groupId.value)
          await setTimeout(() => {
            console.log(resourceGroupList.value)
            console.log(resourceAllSelectList.value)
            resourceSelectList.value = resArr(resourceAllSelectList.value, resourceGroupList.value)
            resourceGroupLoading.value = false
          }, 1000)
          resourceId.value = ''
        } else {
          ElMessage.error(massage || '数据错误！')
        }
      }
    )
  }
}

function changeSelect (val: string):void {
  resourceId.value = val
}

function handleDelResource (val: string):void {
  const data = {
    groupId: groupId.value,
    resourceId: val,
  }
  store.dispatch('delResourceForGroup', data).then(
    async function ({ exitCode, data, massage }) {
      if (exitCode === 0) {
        ElMessage.success('删除成功!')
        resourceGroupLoading.value = true
        await getResourceList(groupId.value)
        await setTimeout(() => {
          console.log(resourceGroupList.value)
          console.log(resourceAllSelectList.value)
          resourceSelectList.value = resArr(resourceAllSelectList.value, resourceGroupList.value)
          resourceGroupLoading.value = false
        }, 1000)
        resourceId.value = ''
      } else {
        ElMessage.error(massage || '数据错误！')
      }
    }
  )
}

/* <InstanceType<typeof ElForm>> */
const searchForm = ref(document.createElement('form'))

const loading = ref(true)
const search: Ref<{ name: string }> = ref({ name: '' })
const resourceList: Ref<UserData[]> = ref([])
const resourceGroupList: Ref<ResourceGroupData[]> = ref([])
const resourceSelectList: Ref<ResourceGroupData[]> = ref([])
const resourceAllSelectList: Ref<ResourceGroupData[]> = ref([])

function getResourceList (id?: string): void {
  console.log(111)

  const data = { pageNumber: 0, pageSize: 10000 }
  Object.assign(data, { groupId: id })
  store.dispatch('queryResource', data).then(
    function ({ exitCode, data, massage }) {
      if (exitCode === 0 && data.content instanceof Array) {
        if (id) {
          resourceGroupList.value = (data.content as ResourceGroupData[])
        } else {
          // const list = (data.content as ResourceGroupData[])
        //   if (resourceGroupList.value.length) {
        //     resourceSelectList.value = list.filter(v => {
        //       return !resourceGroupList.value.some(e => e.id === v.id)
        //     })
        //   } else {
          // resourceSelectList.value = (data.content as ResourceGroupData[])
        //   }
        }
      } else {
        ElMessage.error(massage || '数据错误！')
      }
    }
  )
}

const page = ref({
  sizes: 30,
  pages: 1,
  total: 0,
})
loadingList()
function loadingList (): void {
  loading.value = true
  const data = { pageNumber: page.value.pages - 1, pageSize: page.value.sizes }
  if (search.value.name) Object.assign(data, { name: search.value.name })
  store.dispatch('queryGroupPage', data).then(
    function ({ exitCode, data, massage }) {
      setTimeout(() => { loading.value = false }, 200)
      if (exitCode === 0 && data.content instanceof Array) {
        page.value = {
          sizes: data.pageable.pageSize,
          pages: data.pageable.pageNumber + 1,
          total: data.totalElements,
        }
        resourceList.value = (data.content as UserData[])
      } else {
        ElMessage.error(massage || '数据错误！')
      }
    },
    function () {
      setTimeout(() => { loading.value = false }, 200)
      // console.log('[ ERROR ]', ...arguments)
    },
  )
}

async function create () {
  await syncFormDialog({ $isEdit: false }, async (form: { [key:string]: unknown }) => {
    try {
      const { exitCode, message } = await store.dispatch('addGroup', form)
      if (exitCode === 0) {
        ElMessage.success('添加成功')
      } else throw Object.assign(new Error('[ 失败 ]'), { exitCode, message })
      return true
    } catch (error) {
      ElMessage.error((error as Error).message || '添加失败')
      return false
    }
  }, [store as { install: (app: App<any>) => void; }])
  loadingList()
}

function remove ({ id, name }: UserData) {
  store.dispatch('delGroup', { id: id }).then(
    function ({ exitCode, message }) {
      if (Number(exitCode) === 0) ElMessage.success(`成功删除 ${name} 资源组！`)
      else ElMessage.error(`${message}！`)
      loadingList()
    },
    function () {
      // console.log('%c [ ERROR ] => -107', 'font-size:13px; background:pink; color:#bf2c9f;', ...arguments)
      loadingList()
    },
  )
}

async function edit (row: UserData) {
  await syncFormDialog({ $isEdit: true, ...row }, async (form: { [key:string]: unknown }) => {
    try {
      const { exitCode } = await store.dispatch('modGroup', { id: row.id, name: form.name, desc: form.remark })
      if (exitCode === 0) {
        ElMessage.success('编辑成功')
      } else throw new Error('[ 失败 ]')
      return true
    } catch (error) {
      ElMessage.error('编辑失败')
      return false
    }
  }, [store as { install: (app: App<any>) => void; }])
  loadingList()
}

defineExpose({
  loadingList,
  create,
  remove,
  edit,
  searchForm,
  search,
  resourceList,
  groupId,
  Array,
})
</script>

<style lang="scss" scoped>
.user-info {
  .search {
    > .right {
      margin-left: 12px;
      float: right;
      &::before {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
      &::after {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
    }
  }
}
.box-card {
  :deep(.el-card__body){
    height: 300px;
    overflow-y: auto;
  }
}
</style>
