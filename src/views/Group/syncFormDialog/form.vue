<template>
  <el-dialog v-model="visible" :title="`${data[isEdit] ? '编辑' : '新建'}资源组`" :draggable="true" :modal="false" :close-on-click-modal="false" :append-to-body="true" width="520px" :before-close="handleCloses">
    <template #default>
      <el-scrollbar max-height="500px" style="margin-top: 18px;">
        <el-form ref="formRef" :model="form" label-width="100px">
          <el-form-item label="资源组名称" prop="name" :rules="[{ required: true, message: '此项必填！', trigger: 'blur' }]">
            <el-input v-model="form.name" name="name" placeholder="请填写资源组名称" />
          </el-form-item>
          <el-form-item label="资源组描述" prop="remark">
            <el-input v-model="form.remark" name="remark" placeholder="请填写资源组描述" />
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="default" :loading="loading" @click="handleCloses(doneFun)">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit(formRef)">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, Ref, reactive, nextTick } from 'vue'
import { ElDialog, ElScrollbar, ElForm, ElFormItem, ElInput, ElButton, ElSelect, ElOption, ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Ripple as vRipple } from '@/directives/ripple'
import * as Icon from '@element-plus/icons-vue'
import { useStore, createStore } from 'vuex'
import _ from 'lodash'
interface SyncForm {
  name: string;
  remark: string;
}
/**
 * TODO: 表单
 */
const form = reactive<SyncForm>({
  name: '',
  remark: '',
})

/***************************************************************/
interface SyncResponse {
  resolve: (form: SyncForm) => void
  reject: (form: SyncForm) => void
}
const data: {[key: string]: unknown} = reactive({})
const formRef = ref<FormInstance>()
const doneFun = () => { visible.value = false }
const store = useStore() || createStore({ state: {}, getters: {}, mutations: {}, actions: {}, modules: {} })
const visible = ref(false)
const loading = ref(false)
let callback: (form: SyncForm) => (Promise<boolean>|boolean) = () => false
const promise: SyncResponse = { resolve: (form: SyncForm) => form, reject: (form: SyncForm) => form }

/***************************************************************/
interface User {
  id?: string,
  userCode: string,
  userName: string,
  password: string,
  mail: string,
  mobilePhone: string,
  authMode: string,
  bindIp: string,
  clientName: string,
}

interface Response {
  id: string,
  clientName: string,
  statusCd: 'V'|'R',
  status: string,
}

const props = defineProps({
  isEdit: {
    type: String,
    default: '$isEdit',
  },
})

const authModeList = ref([
  { name: '禁止链接', value: 'DENY' },
  { name: '不认证', value: 'NONE' },
  { name: '使用密码认证', value: 'PWD' },
  { name: '临时密码', value: 'TMP_PWD' },
  { name: '使用动态码认证', value: 'OTP' },
])

const groupList: Ref<Response[]> = ref([] as Response[])
// const value: Ref<User> = ref(props.data as User)
console.log('%c [ props.data ] => -77', 'font-size:13px; background:pink; color:#bf2c9f;', props)
function uuid (len: number): string {
  const v = (+new Date()).toString(36)
  let str = ''
  for (let index = 0; index < (len || v.length); index++) {
    if (v[index]) {
      switch (Math.floor(Math.random() * 2)) {
        case 0: str += v[index].toLowerCase(); break
        case 1: str += v[index].toUpperCase(); break
      }
    } else {
      switch (Math.floor(Math.random() * 2)) {
        case 0: str += Math.floor(Math.random() * 36).toString(36).toLowerCase(); break
        case 1: str += Math.floor(Math.random() * 36).toString(36).toUpperCase(); break
      }
    }
  }
  return str
}
// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function init (data: {[key: string]: unknown}): Promise<{[key: string]: unknown}> {
  try {
    const { data: { content } } = await store.dispatch('queryResourceGroup')
    groupList.value = content
  } catch (error) {
    /*  */
  }
  return {}
}

/***************************************************************/
async function handleCloses (done?: () => void) {
  finish(promise.reject, form)
  if (typeof done === 'function') done()
  else visible.value = false
}

async function handleSubmit (formRef?: FormInstance) {
  await nextTick()
  try {
    if (formRef) {
      try {
        await formRef.validate()
      } catch (error) {
        throw new Error('[表单填写错误]')
      }
    }
    if (typeof callback === 'function') {
      if (!(await syncRuntime(callback, form))) throw new Error('[ 回调阻止 ]')
    }
    finish(promise.resolve, form)
  } catch (error) {
    console.dir(error)
  }
  loading.value = false
}
async function handleReset () {
  console.log('%c [  ] => -195', 'font-size:13px; background:pink; color:#bf2c9f;', data)
  loading.value = true
  try {
    Object.assign(
      data,
      (await Promise.all([
        new Promise((resolve) => setTimeout(resolve, 500, {})),
        init(data),
      ])).reduce((p: {[key: string]: unknown}, c) => Object.assign(p, c), {})
    )
  } catch (error) {
    ElMessage.error('加载失败，请检查网络！')
    return handleCloses(doneFun)
  }
  for (const key in form) {
    if (Object.prototype.hasOwnProperty.call(form, key)) {
      switch (key) {
        case 'remark':
          if (Object.prototype.hasOwnProperty.call(data, key)) Object.assign(form, { [key]: data[key] })
          else Object.assign(form, { [key]: data['desc'] })
          break
        default:
          if (Object.prototype.hasOwnProperty.call(data, key)) Object.assign(form, { [key]: data[key] })
          else Object.assign(form, { [key]: null })
          break
      }
    }
  }
  loading.value = false
}
async function syncRuntime (_callback: (form: SyncForm) => (Promise<boolean>|boolean), _form: SyncForm): Promise<boolean> {
  const syncData = _callback(_form)
  if (syncData instanceof Promise) {
    try {
      return Boolean(await syncData)
    } catch (error) {
      return Boolean(error)
    }
  } else return false
}
async function finish (_callback: (form: SyncForm) => void, _form: SyncForm) {
  visible.value = false
  _callback(_.cloneDeep(_form))
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) delete data[key]
  }
  Object.assign(promise, { resolve: (form: SyncForm) => form, reject: (form: SyncForm) => form })
  callback = () => false
}
defineExpose({
  open (_data: {[key: string]: string}, _callback: (form: SyncForm) => (Promise<boolean>|boolean)) {
    visible.value = true
    loading.value = true
    callback = _callback
    Object.assign(data, _data)
    return new Promise((resolve, reject) => {
      Object.assign(promise, { resolve, reject })
      nextTick(() => {
        handleReset().then(
          async () => { /*  */ },
          () => { handleCloses(doneFun) },
        )
      })
    })
  },
})
</script>

<style lang="scss" scoped>
</style>
