<template>
  <el-card class="user-info">
    <el-form ref="searchForm" :model="search" label-width="90px" inline @submit.prevent="loadingList" class="search">
      <el-form-item label="资源组ID" prop="groupId">
        <el-select v-model="search.groupId" placeholder="资源组ID">
          <el-option v-for="item in resourceGroupList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="资源名称" prop="name">
        <el-input v-model="search.name" placeholder="资源名称" />
      </el-form-item>
      <el-form-item label="资源IP地址" prop="ip">
        <el-input v-model="search.ip" placeholder="资源IP地址" />
      </el-form-item>
      <div class="right">
        <el-button type="primary" @click="create" v-ripple>
          创建
        </el-button>
        <el-button type="primary" @click="loadingList" v-ripple>
          搜索
        </el-button>
        <el-button type="primary" @click="searchForm.resetFields && searchForm.resetFields(); loadingList()" v-ripple>
          重置
        </el-button>
      </div>
    </el-form>
    <el-divider content-position="left">
      资源列表
    </el-divider>
    <el-table :data="resourceList" height="calc(100vh - 280px)" stripe v-loading="loading">
      <el-table-column show-overflow-tooltip prop="id" label="资源ID" min-width="120" />
      <el-table-column show-overflow-tooltip prop="ip" label="资源IP地址" min-width="120" />
      <el-table-column show-overflow-tooltip prop="name" label="资源名称" min-width="120" />
      <el-table-column show-overflow-tooltip prop="netmask" label="子网掩码" min-width="120" />
      <el-table-column label="操作" width="190" align="center" fixed="right">
        <template #default="{ row }">
          <el-link type="primary" :underline="false" style="margin: 0 6px 0 6px;" @click="edit(row)">
            编辑
          </el-link>
          <el-popconfirm title="确认要删除此资源吗?删除后将无法恢复." width="240" @confirm="remove(row)">
            <template #reference>
              <el-link type="danger" :underline="false" style="margin: 0 6px 0 6px;">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:currentPage="page.pages" :page-sizes="[30, 50, 80, 120]" :page-size="page.sizes" layout="prev, pager, next, ->, jumper, sizes, total" :total="page.total" @size-change="loadingList" @current-change="loadingList" />
  </el-card>
</template>

<script lang="ts" setup>
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, App } from 'vue'
import type { Ref } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElForm } from 'element-plus'
import syncFormDialog from './syncFormDialog'
interface UserData {
  id: string,
  name: string,
  ip: string,
  netmask: string,
}
const store = useStore()
/* <InstanceType<typeof ElForm>> */
const searchForm = ref(document.createElement('form'))

const resourceGroupList = ref([])
queryResourceGroupNo()
function queryResourceGroupNo ():void {
  store.dispatch('queryResourceGroup').then(
    function ({ exitCode, data, massage }) {
      if (exitCode === 0) {
        resourceGroupList.value = data
      } else {
        ElMessage.error(massage || '数据错误！')
      }
    }
  )
}

const loading = ref(true)
const search: Ref<{ groupId: string, name: string, ip: string }> = ref({ groupId: '', name: '', ip: '' })
const resourceList: Ref<UserData[]> = ref([])

const page = ref({
  sizes: 30,
  pages: 1,
  total: 0,
})
loadingList()
function loadingList (): void {
  loading.value = true
  const data = { pageNumber: page.value.pages - 1, pageSize: page.value.sizes }
  if (search.value.groupId) Object.assign(data, { groupId: search.value.groupId })
  if (search.value.name) Object.assign(data, { name: search.value.name })
  if (search.value.ip) Object.assign(data, { ip: search.value.ip })
  store.dispatch('queryResource', data).then(
    function ({ exitCode, data, massage }) {
      setTimeout(() => { loading.value = false }, 200)
      if (exitCode === 0 && data.content instanceof Array) {
        page.value = {
          sizes: data.pageable.pageSize,
          pages: data.pageable.pageNumber + 1,
          total: data.totalElements,
        }
        resourceList.value = (data.content as UserData[])
      } else {
        ElMessage.error(massage || '数据错误！')
      }
    },
    function () {
      setTimeout(() => { loading.value = false }, 200)
      // console.log('[ ERROR ]', ...arguments)
    },
  )
}

async function create () {
  await syncFormDialog({ $isEdit: false }, async (form: { [key:string]: unknown }) => {
    try {
      form.netmask = form.netmask ? form.netmask : '***************'
      const { exitCode, message } = await store.dispatch('addResource', form)
      if (exitCode === 0) {
        ElMessage.success('添加成功')
      } else throw Object.assign(new Error('[ 失败 ]'), { exitCode, message })
      return true
    } catch (error) {
      ElMessage.error((error as Error).message || '添加失败')
      return false
    }
  }, [store as { install: (app: App<any>) => void; }])
  loadingList()
}

function remove ({ id, name }: UserData) {
  store.dispatch('delResource', { id: id }).then(
    function ({ exitCode, message }) {
      if (Number(exitCode) === 0) ElMessage.success(`成功删除 ${name} 资源！`)
      else ElMessage.error(`${message}！`)
      loadingList()
    },
    function () {
      // console.log('%c [ ERROR ] => -107', 'font-size:13px; background:pink; color:#bf2c9f;', ...arguments)
      loadingList()
    },
  )
}

async function edit (row: UserData) {
  console.log(row)
  await syncFormDialog({ $isEdit: true, ...row }, async (form: { [key:string]: unknown }) => {
    try {
      const { exitCode } = await store.dispatch('modResource', { id: row.id, name: form.name, ip: form.ip, netmask: form.netmask })
      if (exitCode === 0) {
        ElMessage.success('编辑成功')
      } else throw new Error('[ 失败 ]')
      return true
    } catch (error) {
      ElMessage.error('编辑失败')
      return false
    }
  }, [store as { install: (app: App<any>) => void; }])
  loadingList()
}

defineExpose({
  loadingList,
  create,
  remove,
  edit,
  searchForm,
  search,
  resourceList,
  Array,
})
</script>

<style lang="scss" scoped>
.user-info {
  .search {
    > .right {
      margin-left: 12px;
      float: right;
      &::before {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
      &::after {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
    }
  }
}
</style>
