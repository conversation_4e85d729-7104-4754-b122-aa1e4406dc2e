import SyncForm from './form.vue'
import { createApp, Component, App } from 'vue'

export default async function open (data: {[key: string]: unknown}, callback: (form: {[key: string]: unknown}) => (Promise<boolean>|boolean), apps?: { install: ((app: App, ...options: {[key: string]: string}[]) => void)}[]) {
  const formInstance: Component<unknown, { open: (_data: {[key: string]: unknown}, _callback: (form: {[key: string]: unknown}) => (Promise<boolean>|boolean)) => Promise<{[key: string]: string}> }> = (apps instanceof Array ? apps : []).reduce((vm, use) => vm.use(use), createApp(SyncForm)).mount(document.createElement('div'))
  let form: { [key: string]: unknown }
  try {
    form = await formInstance.open(data, callback)
  } catch (error: unknown) {
    form = Object.entries(error as { [key: string]: unknown }).reduce((p, [key, value]) => Object.assign(p, { [key]: value }), {})
  }
  console.log('%c [ formInstance ] => -13', 'font-size:13px; background:pink; color:#bf2c9f;', formInstance)
  return form
}
