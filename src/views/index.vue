<template>
  <el-card class="user-info">
    <p>默认模板</p>
    <el-table :data="data" stripe style="width: 100%">
      <el-table-column prop="prop" label="label" width="width" />
    </el-table>
  </el-card>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { ref, Ref, reactive, UnwrapNestedRefs, readonly, DeepReadonly, nextTick } from 'vue'
import { useRoute, RouteLocationNormalizedLoaded, useRouter, Router } from 'vue-router'
import { useStore, Store } from 'vuex'

// 声明额外的选项
export default {
  name: 'PasduiaDSA',
  setup () {
    const store: Store<{[key: string]: string}> = useStore()
    const route: RouteLocationNormalizedLoaded = useRoute()
    const router: Router = useRouter()

    return {
      store,
      route,
      router,
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
