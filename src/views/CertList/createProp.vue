<template>
  <el-form ref="form" :model="value" label-width="80px" autocomplete="off">
    <el-form-item label="通用名" prop="userName">
      <el-input v-model="value.userName" :prefix-icon="Icon.User" name="userName" autocomplete="nickname" />
    </el-form-item>
    <el-form-item label="密码" prop="passwd">
      <el-input v-model="value.passwd" type="password" show-password :prefix-icon="Icon.Lock" name="passwd" autocomplete="new-password">
        <template #append>
          <el-button type="primary" v-ripple @click="value.passwd = uuid(16)">
            自动生成
          </el-button>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model="value.remark" name="remark" />
    </el-form-item>
    <el-form-item label="有效期" prop="expiration">
      <el-input-number v-model="value.expiration" placeholder="有效期(单位天)" :controls="false" :min="1" name="expiration" style="width: calc(100% - 3em - 2px);" />
      <span class="el-input-group__append" style="padding: 0 1em;line-height: 30px;width: fit-content;">天</span>
    </el-form-item>
  </el-form>
</template>

<script lang="ts">
import { defineComponent, ref, Ref } from 'vue'
import { ElForm, ElFormItem, ElInput, ElInputNumber, ElButton } from 'element-plus'
import { Ripple } from '@/directives/ripple'
import * as Icon from '@element-plus/icons-vue'

interface User {
  userName: string,
  passwd: string,
  remark: string
  expiration: string
}
export default defineComponent({
  directives: {
    Ripple,
  },
  components: {
    ElForm, ElFormItem, ElInput, ElInputNumber, ElButton,
  },
  name: 'CreateProp',
  setup () {
    const value: Ref<User> = ref({ userName: '', passwd: '', remark: '', expiration: '' })

    function uuid (len: number): string {
      const v = (+new Date()).toString(36)
      let str = ''
      for (let index = 0; index < (len || v.length); index++) {
        if (v[index]) {
          switch (Math.floor(Math.random() * 2)) {
            case 0: str += v[index].toLowerCase(); break
            case 1: str += v[index].toUpperCase(); break
          }
        } else {
          switch (Math.floor(Math.random() * 2)) {
            case 0: str += Math.floor(Math.random() * 36).toString(36).toLowerCase(); break
            case 1: str += Math.floor(Math.random() * 36).toString(36).toUpperCase(); break
          }
        }
      }
      return str
    }

    return {
      value,
      Icon,

      uuid,
    }
  },
})
</script>

<style lang="scss" scoped>
</style>
