<template>
  <el-card class="user-info">
    <el-form ref="searchForm" :model="search" label-width="80px" inline @submit.prevent="loadingList" class="search">
      <el-form-item label="通用名" prop="cn">
        <el-input v-model="search.cn" placeholder="通用名" />
      </el-form-item>
      <div class="right">
        <el-button type="primary" @click="create" v-ripple>
          创建
        </el-button>
        <el-button type="primary" @click="loadingList" v-ripple>
          搜索
        </el-button>
        <el-button type="primary" @click="searchForm.resetFields && searchForm.resetFields(); loadingList()" v-ripple>
          重置
        </el-button>
      </div>
    </el-form>
    <el-divider content-position="left">
      证书列表
    </el-divider>
    <el-table :data="userList" height="calc(100vh - 280px)" stripe v-loading="loading">
      <!-- <el-table-column show-overflow-tooltip prop="name" label="姓名" width="80" />
      <el-table-column show-overflow-tooltip prop="name" label="电话" width="120" />
      <el-table-column show-overflow-tooltip prop="name" label="分类" width="120" />
      <el-table-column show-overflow-tooltip prop="name" label="邮箱" width="120" /> -->
      <el-table-column show-overflow-tooltip prop="clientName" label="通用名" min-width="180" />
      <el-table-column show-overflow-tooltip prop="fileName" label="文件名" min-width="180" />
      <el-table-column show-overflow-tooltip prop="createTime" label="创建日期" min-width="180" />
      <el-table-column show-overflow-tooltip prop="expiration" label="有效期" min-width="180" />
      <el-table-column show-overflow-tooltip prop="status" label="状态" width="75" :filters="[{ text: '有效', value: '有效' }, { text: '注销', value: '注销' }]" :filter-method="filterHandler" />
      <el-table-column show-overflow-tooltip prop="remark" label="备注" min-width="220" />
      <el-table-column label="操作" width="280" align="center">
        <template #default="{ row }">
          <el-button type="text" @click.prevent="seeload(row)" v-if="row.statusCd === 'V'">
            查看证书
          </el-button>
          <el-button type="text" @click.prevent="download(row)" v-if="row.statusCd === 'V'">
            下载证书配置
          </el-button>
          <el-popconfirm title="确认要删除此VPN证书吗?删除后将无法恢复." v-if="row.statusCd === 'V'" @confirm="remove(row)">
            <template #reference>
              <el-button type="text" :style="{ color: 'red' }">
                删除VPN证书
              </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:currentPage="page.pages" :page-sizes="[30, 50, 80, 120]" :page-size="page.sizes" layout="prev, pager, next, ->, jumper, sizes, total" :total="page.total" @size-change="loadingList" @current-change="loadingList" />
    <!-- 重置密码 -->
    <el-dialog
      v-model="dialogVisibles"
      title="查看证书"
      width="30%"
    >
      <div style="">
        <div style="margin-bottom:5px">
          用户名：<span>{{ FM.clientName }}</span>
        </div>
        <div style="margin-bottom:5px">
          文件名：<span>{{ FM.fileName }}</span>
        </div>
        <div style="margin-bottom:5px">
          备注：<span>{{ FM.remark }}</span>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer" style="display:flex;justify-content:center">
          <!-- <el-button @click="dialogVisibles = false">取消</el-button> -->
          <el-button type="primary" @click="dialogVisibles=false">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script lang="ts" setup>
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ref, nextTick, reactive, readonly, h } from 'vue'
import type { Ref } from 'vue'
import type { TableColumnCtx } from 'element-plus/es/components/table/src/table-column/defaults'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import CreateProp from '@/views/CertList/createProp.vue'
import moment from 'moment'

interface Response {
  id: string,
  fileName: string,
  clientName:string,
  createTime:string,
  password?: string,
  statusCd: 'V'|'R',
  status: string,
  remark: string,
}
const store = useStore()

/* <InstanceType<typeof ElForm>> */
const searchForm = ref(document.createElement('form'))

const loading: Ref<boolean> = ref(true)
const search = ref({ cn: '' })
const userList: Ref<Response[]> = ref([])
const page = ref({
  sizes: 30,
  pages: 1,
  total: 0,
})
loadingList()
const dialogVisibles = ref(false)
const FM = ref({
  clientName: '',
  fileName: '',
  remark: '',
})
function seeload (res: Response) {
  FM.value = {
    clientName: res.clientName,
    fileName: res.fileName,
    remark: res.remark,
  }
  dialogVisibles.value = true
}
function loadingList (): void {
  loading.value = true
  const req = { pageNumber: page.value.pages - 1, pageSize: page.value.sizes }
  if (search.value.cn) Object.assign(req, { cn: search.value.cn })
  store.dispatch('queryConfs', req).then(
    function ({ exitCode, message, data }) {
      console.log('%c [ data ] => -128', 'font-size:13px; background:pink; color:#bf2c9f;', data)
      setTimeout(() => { loading.value = false }, 200)
      if (exitCode === 0 && data.content instanceof Array) {
        page.value = {
          sizes: data.pageable.pageSize,
          pages: data.pageable.pageNumber + 1,
          total: data.totalElements,
        }
        userList.value = (data.content as Response[]).map((v) => ({ ...v, createTime: moment(v.createTime).format('YYYY-MM-DD HH:mm') })).reverse()
      }
    },
    function () {
      setTimeout(() => { loading.value = false }, 200)
      // console.log('[ ERROR ]', ...arguments)
    },
  )
}

function download ({ clientName }: Response) {
  store.dispatch('downConf', { cn: clientName }).then(
    function ({ data, headers }: { data: Blob, headers: { [key: string]: string } }) {
      const type = headers['content-disposition'] || `attachment;fileName=${clientName}.ovpn`
      const objectURL = window.URL.createObjectURL(new Blob([data], { type }))
      const link = document.createElement('a')
      link.download = String(type).substring(String(type).indexOf('fileName=') + 9) || `${clientName}.ovpn`
      link.style.display = 'none'
      link.href = objectURL
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(objectURL)
      loadingList()
      ElMessage.success('开始下载！')
    },
    function () {
      ElMessage.error('下载失败！')
      loadingList()
    },
  )
}

function create () {
  const form = h(CreateProp)
  ElMessageBox({
    title: '创建证书',
    message: form,
    showCancelButton: true,
    confirmButtonText: '创建',
    cancelButtonText: '取消',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = 'Loading...'
        const userData = new FormData(form.el as HTMLFormElement)
        if (new RegExp('^[A-Za-z0-9_]+$').test((userData.get('userName') as string) || '')) {
          store.dispatch('createConf', { userName: userData.get('userName'), passwd: userData.get('passwd'), remark: userData.get('remark'), expiration: userData.get('expiration') }).then(
            function ({ exitCode, message }) {
              done()
              instance.confirmButtonLoading = false
              if (exitCode === 0) {
                ElMessage.success('添加成功')
              } else {
                ElMessage.error(message || '添加失败')
              }
            },
            function ({ data }) {
              console.log('%c [ data ] => -199', 'font-size:13px; background:pink; color:#bf2c9f;', data)
              done()
              instance.confirmButtonLoading = false
              ElMessage.error('添加失败')
            },
          )
        } else {
          ElMessage.error('证书用户名不能包含中文字符')
          setTimeout(() => {
            instance.confirmButtonLoading = false
            instance.confirmButtonText = '创建'
          }, 500)
        }
      } else {
        done()
      }
    },
  }).then((action) => {
    console.log(action, 'action')
    // if (action === 'confirm') {
    //   ElMessage.success('添加成功')
    // }
    loadingList()
  })
}

function remove ({ clientName }: Response) {
  console.log(...arguments, 'opko')
  store.dispatch('revokeConf', { cn: clientName }).then(
    function ({ exitCode }) {
      if (Number(exitCode) === 0) ElMessage.success(`成功删除${name}！`)
      else ElMessage.error(`${name}删除失败！`)
      loadingList()
    },
    function () {
      // console.log('%c [ ERROR ] => -107', 'font-size:13px; background:pink; color:#bf2c9f;', ...arguments)
      loadingList()
    },
  )
}

function filterHandler (value: string, row: { [k: string]: string }, column: TableColumnCtx<Response>) {
  if (Object.hasOwnProperty.call(column, 'property')) return row[column['property']] === value
  else return false
}
defineExpose({
  seeload,
  loadingList,
  filterHandler,
  download,
  create,
  remove,
  searchForm,
  page,
  dialogVisibles,
  search,
  userList,
  FM,
  Array,
})
</script>

<style lang="scss" scoped>
.user-info {
  .search {
    > .right {
      margin-left: 12px;
      float: right;
      &::before {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
      &::after {
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
      }
    }
  }
}
</style>
