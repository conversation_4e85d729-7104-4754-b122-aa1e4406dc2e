<template>
  <el-card class="user-info">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="用户设置" name="1">
        <p>用户设置</p>
      </el-tab-pane>
      <el-tab-pane label="系统设置" name="2">
        <p>系统设置</p>
      </el-tab-pane>
      <el-tab-pane label="角色管理" name="3">
        <p>角色管理</p>
      </el-tab-pane>
      <el-tab-pane label="定时任务" name="4">
        <p>定时任务</p>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { ref, Ref, nextTick, reactive, readonly, defineComponent } from 'vue'
import * as Icon from '@element-plus/icons-vue'
import { useStore } from 'vuex'

interface Response {
  id: string,
  name: string,
  statusCd: 'V'|'R',
  status: string,
}

interface SettingsForm {
  mode: number,
}

export default defineComponent({
  name: 'VSettingsPage',
  setup () {
    // const store = useStore()
    const userList: Ref<Response[]> = ref([])

    const activeName = ref('1')
    const form: Ref<SettingsForm> = ref({
      mode: 1,
    })

    // store.dispatch('queryConfs', { pageNumber: 0, pageSize: 5000 }).then(
    //   function ({ exitCode, message }) {
    //     // console.log('%c [ message ] => -21', 'font-size:13px; background:pink; color:#bf2c9f;', ...arguments)
    //     if (exitCode === 0 && message instanceof Array) {
    //       userList.value = (message as Response[]).map(({ id, name, statusCd, status }) => ({ id, name, statusCd, status }))
    //     }
    //   },
    //   function () {
    //     console.log('[ ERROR ]', ...arguments)
    //   },
    // )

    function handleClick () {
      console.log(...arguments)
    }
    return {
      activeName,
      userList,
      form,
      model: '',

      handleClick,

      Icon,
    }
  },
})
</script>

<style lang="scss" scoped>
</style>
