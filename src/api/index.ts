/* eslint-disable @typescript-eslint/no-unused-vars */
import axios, { CancelToken, AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { ElNotification } from 'element-plus'
import store from '@/store'
import qs from 'qs'
import router from '@/router'

const baseURL = '/api'
interface BaseResponse {
  exitCode: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data?: any;
  message: string;
}
/**
 * Axios 请求/响应拦截器
 */
const request = {
  onFulfilled (config: AxiosRequestConfig): Promise<AxiosRequestConfig> {
    config.params = config.params && typeof config.params === 'object' ? config.params : {}
    return new Promise((resolve, reject) => {
      try {
        Object.assign(config.headers as { [key: string]: string }, (((store || {}).getters || {}).getHeaders || {}))
        Object.assign(config.params, { _t: +new Date() })
        resolve(config)
      } catch (error) {
        reject(config)
      }
    })
  },
  onRejected (error: AxiosRequestConfig|Error): AxiosRequestConfig|Error {
    console.dir(error)
    return error
  },
}
const response = {
  onFulfilled ({ data, status, statusText, headers, config, request }: AxiosResponse): BaseResponse | { data: Blob, headers: { [key: string]: string } } {
    if (data.code === 401) {
      store.commit('CLEAR_HEADERS')
      router.push({ name: 'Login' })
      ElNotification.closeAll()
      ElNotification.error({ title: '没有登录', message: data.message })
    }
    if (config.responseType === 'blob') { /* content-disposition: attachment;fileName=LX_renxs.ovpn */
      return {
        exitCode: status,
        data,
        headers,
      }
    }
    return {
      data: data.data || data.body,
      exitCode: Number(data.exitCode || data.code || 0),
      message: data.message || data.msg || '',
    }
  },
  onRejected ({ response, message }: AxiosError) {
    console.log(response, '走走')
    ElNotification.error({
      title: (response || { statusText: '网络错误' }).statusText,
      message: message,
    })
    return response
  },
}

/**
 * @name DATA_API
 */
export const dataApi: AxiosInstance = axios.create({
  baseURL,
  headers: { 'Content-Type': 'multipart/form-data; charset=UTF-8', ...(((store || {}).getters || {}).getHeaders || {}) },
  transformRequest: (data) => Object.keys(data).reduce((previous, current) => { previous.append(current, data[current]); return previous }, new FormData()),
  paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'brackets' }),
  validateStatus: (status) => status >= 200 && status < 300,
})
dataApi.interceptors.request.use(request.onFulfilled, request.onRejected)
dataApi.interceptors.response.use(response.onFulfilled, response.onRejected)
/**
 * @name FORM_API
 */
export const formApi: AxiosInstance = axios.create({
  baseURL,
  headers: { post: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', ...(((store || {}).getters || {}).getHeaders || {}) } },
  transformRequest: (data) => qs.stringify(data),
  paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'brackets' }),
  validateStatus: (status) => status >= 200 && status < 300,
})
formApi.interceptors.request.use(request.onFulfilled, request.onRejected)
formApi.interceptors.response.use(response.onFulfilled, response.onRejected)
/**
 * @name JSON_API
 */
export const jsonApi: AxiosInstance = axios.create({
  baseURL,
  headers: { post: { 'Content-Type': 'application/json; charset=UTF-8', ...(((store || {}).getters || {}).getHeaders || {}) } },
  transformRequest: (data) => JSON.stringify(data),
  paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'brackets' }),
  validateStatus: (status) => status >= 200 && status < 300,
})
jsonApi.interceptors.request.use(request.onFulfilled, request.onRejected)
jsonApi.interceptors.response.use(response.onFulfilled, response.onRejected)

export default {
  dataApi,
  formApi,
  jsonApi,
}
