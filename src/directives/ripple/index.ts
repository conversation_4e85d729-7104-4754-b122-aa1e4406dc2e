/* eslint-disable @typescript-eslint/no-explicit-any */
import { ObjectDirective, Plugin, DirectiveBinding, nextTick } from 'vue'
const keyCodes = Object.freeze({
  enter: 13,
  tab: 9,
  delete: 46,
  esc: 27,
  space: 32,
  up: 38,
  down: 40,
  left: 37,
  right: 39,
  end: 35,
  home: 36,
  del: 46,
  backspace: 8,
  insert: 45,
  pageup: 33,
  pagedown: 34,
  shift: 16,
})

const rippleStop = Symbol('rippleStop')
const DELAY_RIPPLE = 80

let keyboardRipple = false

function transform (el: HTMLElement, value: string) {
  el.style.transform = value
  el.style.webkitTransform = value
}

function opacity (el: HTMLElement, value: string) {
  el.style.opacity = value.toString()
}

function isTouchEvent (e: Event) {
  return e.constructor.name === 'TouchEvent'
}

function isKeyboardEvent (e: Event) {
  return e.constructor.name === 'KeyboardEvent'
}

function isRippleEnabled (value: {[s: string]: string}) {
  return typeof value === 'undefined' || !!value
}

const calculate = (e: UIEvent, el: HTMLElement, value: {[s: string]: string} = {}) => {
  let localX = 0
  let localY = 0

  if (!isKeyboardEvent(e)) {
    const offset = el.getBoundingClientRect()
    const target = isTouchEvent(e) ? (e as TouchEvent).touches[(e as TouchEvent).touches.length - 1] : e

    localX = (target as MouseEvent).clientX - offset.left
    localY = (target as MouseEvent).clientY - offset.top
  }

  let radius = 0
  let scale = 0.3
  if ((el as any)._ripple && (el as any)._ripple.circle) {
    scale = 0.15
    radius = el.clientWidth / 2
    radius = value.center ? radius : radius + Math.sqrt((localX - radius) ** 2 + (localY - radius) ** 2) / 4
  } else {
    radius = Math.sqrt(el.clientWidth ** 2 + el.clientHeight ** 2) / 2
  }

  const centerX = `${(el.clientWidth - (radius * 2)) / 2}px`
  const centerY = `${(el.clientHeight - (radius * 2)) / 2}px`

  const x = value.center ? centerX : `${localX - radius}px`
  const y = value.center ? centerY : `${localY - radius}px`

  return { radius, scale, x, y, centerX, centerY }
}

const ripples = {
  show (e: Event, el: HTMLElement, value: {[s: string]: string} = {}) {
    if (!(el as any)._ripple || !(el as any)._ripple.enabled) {
      return
    }

    const container = document.createElement('span')
    const animation = document.createElement('span')

    container.appendChild(animation)
    container.className = 'v-ripple__container'

    if (value.class) {
      container.className += ` ${value.class}`
    }

    const { radius, scale, x, y, centerX, centerY } = calculate(e as UIEvent, el, value)

    const size = `${radius * 2}px`
    animation.className = 'v-ripple__animation'
    animation.style.width = size
    animation.style.height = size

    el.appendChild(container)

    const computed = window.getComputedStyle(el)
    if (computed && computed.position === 'static') {
      el.style.position = 'relative'
      el.dataset.previousPosition = 'static'
    }

    animation.classList.add('v-ripple__animation--enter')
    animation.classList.add('v-ripple__animation--visible')
    transform(animation, `translate(${x}, ${y}) scale3d(${scale},${scale},${scale})`)
    opacity(animation, '0')
    animation.dataset.activated = String(performance.now())

    setTimeout(() => {
      animation.classList.remove('v-ripple__animation--enter')
      animation.classList.add('v-ripple__animation--in')
      transform(animation, `translate(${centerX}, ${centerY}) scale3d(1,1,1)`)
      opacity(animation, '0.25')
    }, 0)
  },

  hide (el: HTMLElement) {
    if (!el || !(el as any)._ripple || !(el as any)._ripple.enabled) return

    const ripples = el.getElementsByClassName('v-ripple__animation')

    if (ripples.length === 0) return
    const animation = ripples[ripples.length - 1]

    if ((animation as HTMLElement).dataset.isHiding) return
    else (animation as HTMLElement).dataset.isHiding = 'true'

    const diff = performance.now() - Number((animation as HTMLElement).dataset.activated)
    const delay = Math.max(250 - diff, 0)

    setTimeout(() => {
      animation.classList.remove('v-ripple__animation--in')
      animation.classList.add('v-ripple__animation--out')
      opacity((animation as HTMLElement), '0')

      setTimeout(() => {
        const ripples = el.getElementsByClassName('v-ripple__animation')
        if (ripples.length === 1 && el.dataset.previousPosition) {
          el.style.position = el.dataset.previousPosition
          delete el.dataset.previousPosition
        }

        animation.parentNode && el.removeChild(animation.parentNode)
      }, 300)
    }, delay)
  },
}

function rippleShow (e: Event) {
  const value: {[s: string]: string} = {}
  const element = e.currentTarget

  if (!element || !(element as any)._ripple || (element as any)._ripple.touched || (e as any)[rippleStop]) return

  // Don't allow the event to trigger ripples on any other elements
  (e as any)[rippleStop] = true

  if (isTouchEvent(e)) {
    (element as any)._ripple.touched = true
    ;(element as any)._ripple.isTouch = true
  } else {
    // It's possible for touch events to fire
    // as mouse events on Android/iOS, this
    // will skip the event call if it has
    // already been registered as touch
    if ((element as any)._ripple.isTouch) return
  }
  value.center = (element as any)._ripple.centered || isKeyboardEvent(e)
  if ((element as any)._ripple.class) {
    value.class = (element as any)._ripple.class
  }

  if (isTouchEvent(e)) {
    // already queued that shows or hides the ripple
    if ((element as any)._ripple.showTimerCommit) return

    (element as any)._ripple.showTimerCommit = () => {
      ripples.show(e, element as HTMLElement, value)
    }
    (element as any)._ripple.showTimer = window.setTimeout(() => {
      if (element && (element as any)._ripple && (element as any)._ripple.showTimerCommit) {
        (element as any)._ripple.showTimerCommit()
        ;(element as any)._ripple.showTimerCommit = null
      }
    }, DELAY_RIPPLE)
  } else {
    ripples.show(e, element as HTMLElement, value)
  }
}

function rippleHide (e: Event) {
  const element = e.currentTarget
  if (!element || !(element as any)._ripple) return

  window.clearTimeout((element as any)._ripple.showTimer)

  // The touch interaction occurs before the show timer is triggered.
  // We still want to show ripple effect.
  if (e.type === 'touchend' && (element as any)._ripple.showTimerCommit) {
    (element as any)._ripple.showTimerCommit()
    ;(element as any)._ripple.showTimerCommit = null

    // re-queue ripple hiding
    ;(element as any)._ripple.showTimer = setTimeout(() => {
      rippleHide(e)
    })
    return
  }

  window.setTimeout(() => {
    if ((element as any)._ripple) {
      (element as any)._ripple.touched = false
    }
  })
  ripples.hide(element as HTMLElement)
}

function rippleCancelShow (e: Event) {
  const element = e.currentTarget

  if (!element || !(element as any)._ripple) return

  if ((element as any)._ripple.showTimerCommit) {
    (element as any)._ripple.showTimerCommit = null
  }

  window.clearTimeout((element as any)._ripple.showTimer)
}

function keyboardRippleShow (e: KeyboardEvent) {
  if (!keyboardRipple && (e.keyCode === keyCodes.enter || e.keyCode === keyCodes.space)) {
    keyboardRipple = true
    rippleShow(e)
  }
}

function keyboardRippleHide (e: KeyboardEvent) {
  keyboardRipple = false
  rippleHide(e)
}

function focusRippleHide (e: FocusEvent) {
  if (keyboardRipple === true) {
    keyboardRipple = false
    rippleHide(e)
  }
}

function updateRipple (el: HTMLElement, binding: DirectiveBinding<any>, wasEnabled: boolean) {
  const enabled = isRippleEnabled(binding.value)
  if (!enabled) {
    ripples.hide(el)
  }
  (el as any)._ripple = (el as any)._ripple || {}
  ;(el as any)._ripple.enabled = enabled
  const value = binding.value || {}
  if (value.center) {
    (el as any)._ripple.centered = true
  }
  if (value.class) {
    (el as any)._ripple.class = binding.value.class
  }
  if (value.circle) {
    (el as any)._ripple.circle = value.circle
  }
  if (enabled && !wasEnabled) {
    el.addEventListener('touchstart', rippleShow, { passive: true })
    el.addEventListener('touchend', rippleHide, { passive: true })
    el.addEventListener('touchmove', rippleCancelShow, { passive: true })
    el.addEventListener('touchcancel', rippleHide)

    el.addEventListener('mousedown', rippleShow)
    el.addEventListener('mouseup', rippleHide)
    el.addEventListener('mouseleave', rippleHide)

    el.addEventListener('keydown', keyboardRippleShow)
    el.addEventListener('keyup', keyboardRippleHide)

    el.addEventListener('blur', focusRippleHide)

    // Anchor tags can be dragged, causes other hides to fail - #1537
    el.addEventListener('dragstart', rippleHide, { passive: true })
  } else if (!enabled && wasEnabled) {
    removeListeners(el)
  }
}

function removeListeners (el: HTMLElement) {
  el.removeEventListener('mousedown', rippleShow)
  el.removeEventListener('touchstart', rippleShow)
  el.removeEventListener('touchend', rippleHide)
  el.removeEventListener('touchmove', rippleCancelShow)
  el.removeEventListener('touchcancel', rippleHide)
  el.removeEventListener('mouseup', rippleHide)
  el.removeEventListener('mouseleave', rippleHide)
  el.removeEventListener('keydown', keyboardRippleShow)
  el.removeEventListener('keyup', keyboardRippleHide)
  el.removeEventListener('dragstart', rippleHide)
  el.removeEventListener('blur', focusRippleHide)
}

export const Ripple: ObjectDirective = {
  beforeMount (el, binding, node) {
    // eslint-disable-next-line prefer-rest-params
    updateRipple(el, binding, false)
    if (process.env.NODE_ENV === 'development') {
      // warn if an inline element is used, waiting for el to be in the DOM first
      nextTick(() => {
        const computed = window.getComputedStyle(el)
        if (computed && computed.display === 'inline') {
          const context = node.el ? [node.el, node.props] : [node.type]
          process.env.NODE_ENV === 'development' && console.warn('v-ripple 只能用于块级元素', ...context)
        }
      })
    }
  },
  unmounted (el) {
    delete (el as any)._ripple
    removeListeners(el)
  },
  beforeUpdate (el, binding: DirectiveBinding<any>) {
    if (binding.value === binding.oldValue) {
      return
    }
    const wasEnabled = isRippleEnabled(binding.oldValue)
    updateRipple(el, binding, wasEnabled)
  },
}

const plugin: Plugin = {
  install (Vue) {
    Vue.directive('ripple', Ripple)
  },
}
export default plugin
