package me.meilon.vpnmgr.facade.http;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.args.user.UserPwd;
import me.meilon.vpnmgr.app.dto.base.Result;
import me.meilon.vpnmgr.app.service.UserService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/system")
@RequiredArgsConstructor
public class SystemController {

    private final UserService userService;


    /**
     * 用户登录
     *
     * @param args 用户名密码
     */
    @PostMapping("login")
    public Result<String> login(@RequestBody UserPwd args) {
        String res = userService.login(args.getUserName(), args.getPasswd());
        if (res != null){
            return Result.<String>ok("ok").setBody(res);
        }
        return Result.err("用户名或密码错误");
    }

    
}
