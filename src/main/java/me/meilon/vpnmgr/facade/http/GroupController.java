package me.meilon.vpnmgr.facade.http;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.args.resource.GroupAdd;
import me.meilon.vpnmgr.app.args.resource.GroupMod;
import me.meilon.vpnmgr.app.args.resource.GroupPageQry;
import me.meilon.vpnmgr.app.args.resource.GroupResourceAdd;
import me.meilon.vpnmgr.app.dto.GroupDto;
import me.meilon.vpnmgr.app.dto.ResourceDto;
import me.meilon.vpnmgr.app.dto.base.ListResult;
import me.meilon.vpnmgr.app.dto.base.PageResult;
import me.meilon.vpnmgr.app.dto.base.Result;
import me.meilon.vpnmgr.app.service.GroupService;
import org.springframework.web.bind.annotation.*;

/**
 * 资源组管理
 */
@Slf4j
@RestController
@RequestMapping("/api/group")
@RequiredArgsConstructor
public class GroupController {

    private final GroupService groupService;

    /**
     * 新增资源组
     */
    @PostMapping("add")
    public Result<GroupDto> add(@RequestBody GroupAdd args) {
        return Result.withData(groupService.create(args));
    }

    /**
     * 修改资源组
     */
    @PostMapping("mod")
    public Result<GroupDto> mod(@RequestBody GroupMod args) {
        groupService.update(args);
        return Result.ok();
    }

    /**
     * 删除资源组
     */
    @GetMapping("del")
    public Result<Void> del(@RequestParam String id) {
        groupService.delete(id);
        return Result.ok();
    }

    /**
     * 向资源组添加资源
     */
    @PostMapping("add/resource")
    public Result<Void> addResource(@RequestBody GroupResourceAdd args) {
        groupService.appendResource(args.getGroupId(), args.getResourceId());
        return Result.ok();
    }

    /**
     * 从资源组移除资源
     */
    @GetMapping("del/resource")
    public Result<Void> delResource(@RequestParam String groupId, @RequestParam String resourceId) {
        groupService.removeResource(groupId, resourceId);
        return Result.ok();
    }

    /**
     * 查询资源组下的资源列表
     */
    @GetMapping("query/resource/list")
    public ListResult<ResourceDto> queryResourceList(@RequestParam String groupId) {
        return ListResult.ofList(groupService.queryResourceList(groupId));
    }

    /**
     * 分页查询资源组列表
     */
    @PostMapping("query/page")
    public PageResult<GroupDto> queryPage(@RequestBody GroupPageQry args) {
        return PageResult.ofPath(groupService.queryPage(args));
    }

    /**
     * 查询资源组列表
     */
    @GetMapping("query/list")
    public ListResult<GroupDto> queryList() {
        return ListResult.ofList(groupService.queryList());
    }

}
