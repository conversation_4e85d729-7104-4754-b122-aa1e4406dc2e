package me.meilon.vpnmgr.facade.http;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.args.resource.ResourceAdd;
import me.meilon.vpnmgr.app.args.resource.ResourceMod;
import me.meilon.vpnmgr.app.args.resource.ResourcePageQry;
import me.meilon.vpnmgr.app.dto.ResourceDto;
import me.meilon.vpnmgr.app.dto.base.ListResult;
import me.meilon.vpnmgr.app.dto.base.PageResult;
import me.meilon.vpnmgr.app.dto.base.Result;
import me.meilon.vpnmgr.app.service.ResourceService;
import org.springframework.web.bind.annotation.*;

/**
 * 资源管理
 */
@Slf4j
@RestController
@RequestMapping("/api/resource")
@RequiredArgsConstructor
public class ResourceController {

    private final ResourceService resourceService;

    /**
     * 新增资源
     */
    @PostMapping("add")
    public Result<ResourceDto> add(@RequestBody ResourceAdd args) {
        return Result.withData(resourceService.create(args));
    }

    /**
     * 修改资源
     */
    @PostMapping("mod")
    public Result<ResourceDto> mod(@RequestBody ResourceMod args) {
        resourceService.update(args);
        return Result.ok();
    }

    /**
     * 删除资源
     */
    @GetMapping("del")
    public Result<Void> del(@RequestParam String id) {
        resourceService.delete(id);
        return Result.ok();
    }

    /**
     * 分页查询资源列表
     */
    @PostMapping("query/page")
    public PageResult<ResourceDto> queryPage(@RequestBody ResourcePageQry args) {
        return PageResult.ofPath(resourceService.queryPage(args));
    }

    /**
     * 查询资源列表
     */
    @GetMapping("query/list")
    public ListResult<ResourceDto> queryList() {
        return ListResult.ofList(resourceService.queryList());
    }

}
