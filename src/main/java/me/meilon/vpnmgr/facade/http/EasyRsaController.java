package me.meilon.vpnmgr.facade.http;


import jakarta.servlet.http.HttpServletResponse;
import jakarta.websocket.server.PathParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.args.CerPageQry;
import me.meilon.vpnmgr.app.args.CertificateAdd;
import me.meilon.vpnmgr.app.dto.CertificateDto;
import me.meilon.vpnmgr.app.dto.base.PageResult;
import me.meilon.vpnmgr.app.dto.base.Result;
import me.meilon.vpnmgr.app.service.EasyRsaService;
import me.meilon.vpnmgr.infrastructure.biz.certificate.jpa.CertificateDo;
import me.meilon.vpnmgr.infrastructure.biz.certificate.jpa.CertificateRepository;
import me.meilon.vpnmgr.infrastructure.conf.OpenvpnProperty;
import me.meilon.vpnmgr.infrastructure.exception.ValidationFailed;
import me.meilon.vpnmgr.infrastructure.utils.DesUtil;
import me.meilon.vpnmgr.infrastructure.utils.HttpDownLoadUtils;
import me.meilon.vpnmgr.infrastructure.utils.StrUtils;
import me.meilon.vpnmgr.infrastructure.utils.json.JsonUtil;
import org.springframework.web.bind.annotation.*;

/**
 * 证书管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/cer")
@RequiredArgsConstructor
public class EasyRsaController {

    private final CertificateRepository certificateRepository;
    private final EasyRsaService easyRsaService;
    private final OpenvpnProperty property;


    /**
     * 查询证书
     */
    @PostMapping("query")
    public PageResult<CertificateDto> query(@RequestBody CerPageQry queryCer) {
        log.info("/api/cer/query : {}", JsonUtil.toJsonString(queryCer));
        return PageResult.ofPath(easyRsaService.query(queryCer));
    }

    /**
     * 创建一个证书
     *
     * @param args 客户端证书参数
     * @return 创建好的证书基本信息
     */
    @PostMapping("create")
    public Result<CertificateDto> newClient(@RequestBody CertificateAdd args) {
        log.info("/api/cer/create : {}", JsonUtil.toJsonString(args));
        return Result.withData(easyRsaService.create(args));
    }

    /**
     * 作废一个VPN证书
     *
     * @param cn 客户端名称
     */
    @GetMapping("revokeClient")
    public Result<Void> revokeClient(@PathParam("cn") String cn) {
        log.info("/api/cer/revokeClient : {}", cn);
        if (cn == null || cn.isEmpty()) {
            return Result.err("CN 不能为空");
        }
        easyRsaService.revokeClient(cn);
        return Result.ok();
    }

    /**
     * 下载证书配置文件
     *
     * @param cn 客户端名称
     */
    @GetMapping("downConf")
    public void downConf(HttpServletResponse response, @PathParam("cn") String cn) {
        log.info("/api/cer/downConf : {}", cn);
        if (cn == null || cn.isEmpty()) {
            response.setStatus(403);
            throw new ValidationFailed("客户端名称不能为空");
        }
        String fileName = cn + ".ovpn";
        String clientDir = property.getClientSaveDir();
        HttpDownLoadUtils.copy(clientDir, fileName, response);
    }

    /**
     * 查看证书密码
     */
    @GetMapping("passwd")
    public Result<String> queryPasswd(@PathParam("cn") String cn) {
        CertificateDo cer = certificateRepository.findOneByClientName(cn)
                .orElse(new CertificateDo());
        String pwd = cer.getPassword();
        if (StrUtils.isBlank(pwd)){
            return Result.withData("无密码");
        }
        else {
            return Result.withData(DesUtil.decrypt(pwd));
        }
    }

}
