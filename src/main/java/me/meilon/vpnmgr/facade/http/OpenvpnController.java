package me.meilon.vpnmgr.facade.http;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.dto.LeasesDto;
import me.meilon.vpnmgr.app.dto.base.ListResult;
import me.meilon.vpnmgr.app.dto.base.Result;
import me.meilon.vpnmgr.app.service.LeasesService;
import me.meilon.vpnmgr.app.service.OpenvpnService;
import me.meilon.vpnmgr.infrastructure.management.v3.message.RoutingTable;
import me.meilon.vpnmgr.infrastructure.management.v3.message.StateMessage;
import me.meilon.vpnmgr.infrastructure.utils.ListPagHelper;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * VPN 管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/vpn")
@RequiredArgsConstructor
public class OpenvpnController {

    private final OpenvpnService openvpnService;
    private final LeasesService leasesService;


    /**
     * 获取 openvpn 状态
     */
    @GetMapping("status")
    public Result<String> status() {
        String status = openvpnService.state();
        return Result.ok(status);
    }

    /**
     * 获取 openvpn 状态信息
     */
    @GetMapping("state")
    public Result<StateMessage> statusDetail() {
        StateMessage state = openvpnService.stateDetail();
        if (state == null) {
            return Result.err("UNKNOWN");
        }
        return Result.withData(state);
    }

    /**
     * 查看路由表
     */
    @GetMapping("routeTable")
    public ListResult<RoutingTable> routeTable(@RequestParam Integer pageNum, @RequestParam Integer pageSize) {
        List<RoutingTable> out = openvpnService.routeTable();
        return ListResult.ofList(ListPagHelper.paging(out, pageNum, pageSize));
    }

    /**
     * 读取 openvpn 服务配置
     *
     * @return 服务配置
     */
    @GetMapping("serverConf")
    public ListResult<String> serverConf() {
        return ListResult.ofList(openvpnService.readServerConf());
    }


    /**
     * 查看 IP 租约表
     * @param userCode 基于用户编码过滤
     * @return openvpn IP 租约表
     */
    @GetMapping("leases")
    public ListResult<LeasesDto> leases(@RequestParam(required = false) String userCode){
        return ListResult.ofList(leasesService.queryLeasesByUser(userCode));
    }


}
