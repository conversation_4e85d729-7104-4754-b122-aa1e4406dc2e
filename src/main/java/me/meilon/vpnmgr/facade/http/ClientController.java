package me.meilon.vpnmgr.facade.http;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.args.ClientLinkRecordPageQry;
import me.meilon.vpnmgr.app.args.ConnStatusPageQry;
import me.meilon.vpnmgr.app.dto.base.PageResult;
import me.meilon.vpnmgr.app.dto.base.Result;
import me.meilon.vpnmgr.app.dto.client.ConnRecord;
import me.meilon.vpnmgr.app.dto.client.ConnStatus;
import me.meilon.vpnmgr.app.service.ClientService;
import me.meilon.vpnmgr.infrastructure.management.v3.message.ClientKillMessage;
import me.meilon.vpnmgr.infrastructure.management.v3.message.KillMessage;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.CmdResult;
import org.springframework.web.bind.annotation.*;

/**
 * 客户端管理
 */
@Slf4j
@RestController
@RequestMapping("/api/client")
@RequiredArgsConstructor
public class ClientController {

    private final ClientService clientService;

    /**
     * 查询客户端链接状态
     */
    @PostMapping("query/status")
    public PageResult<ConnStatus> connectStatus(@RequestBody ConnStatusPageQry args){
        return PageResult.ofPath(clientService.queryClientLinkStatus(args));
    }

    /**
     * 查询客户端链接记录
     */
    @PostMapping("query/record")
    public PageResult<ConnRecord> connectRecord(@RequestBody ClientLinkRecordPageQry args){
        return PageResult.ofPath(clientService.queryRecordPage(args));
    }

    /**
     * 驱逐客户端
     */
    @GetMapping("evict")
    public Result<Void> evictClient(@RequestParam String id){
        KillMessage message = clientService.evictClient(id);
        if (message.getResult() == CmdResult.SUCCESS) {
            return Result.ok(message.getMessage());
        }
        else {
            return Result.err(message.getMessage());
        }
    }

}
