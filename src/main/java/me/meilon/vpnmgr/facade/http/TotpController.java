package me.meilon.vpnmgr.facade.http;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.dto.base.Result;
import me.meilon.vpnmgr.app.service.TotpService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 动态码管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/totp")
@RequiredArgsConstructor
public class TotpController {

    private final TotpService totpService;


    /**
     * 创建QR码
     * @param userCode 用户编码
     */
    @GetMapping("createQrCode")
    public Result<String> createQrCode(@RequestParam String userCode){
        return Result.withData(totpService.createQrCode(userCode));
    }

    /**
     * 创建 QR 码验证
     * 验证通过后 QR 码生效
     * @param otp 动态码
     */
    @GetMapping("verifyQrCode")
    public Result<Void> checkQrCode(@RequestParam String userCode,
                                    @RequestParam Integer otp){
        if (totpService.verifyQrCode(userCode, otp)){
            return Result.ok("验证成功");
        }
        return Result.ok("验证失败");
    }

}
