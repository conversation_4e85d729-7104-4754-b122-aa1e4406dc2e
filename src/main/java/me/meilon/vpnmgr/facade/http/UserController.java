package me.meilon.vpnmgr.facade.http;


import jakarta.websocket.server.PathParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.args.ResetPasswdReq;
import me.meilon.vpnmgr.app.args.user.UserAdd;
import me.meilon.vpnmgr.app.args.user.UserConfigAdd;
import me.meilon.vpnmgr.app.args.user.UserMod;
import me.meilon.vpnmgr.app.args.user.UserPageQry;
import me.meilon.vpnmgr.app.dto.UserConfigEntity;
import me.meilon.vpnmgr.app.dto.UserDto;
import me.meilon.vpnmgr.app.dto.base.ListResult;
import me.meilon.vpnmgr.app.dto.base.PageResult;
import me.meilon.vpnmgr.app.dto.base.Result;
import me.meilon.vpnmgr.app.service.UserConfigService;
import me.meilon.vpnmgr.app.service.UserService;
import org.springframework.web.bind.annotation.*;


/**
 * 用户管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;
    private final UserConfigService userConfigService;

    /**
     * 查询用户
     */
    @PostMapping(value = "query")
    public PageResult<UserDto> query(@RequestBody UserPageQry args) {
        return PageResult.ofPath(userService.query(args));
    }

    /**
     * 添加一个用户
     */
    @PostMapping("add")
    public Result<Void> addUser(@RequestBody UserAdd args) {
        userService.create(args);
        return Result.ok();
    }

    /**
     * 修改用户信息
     */
    @PostMapping("mod")
    public Result<Void> mod(@RequestBody UserMod args) {
        userService.update(args);
        return Result.ok();
    }

    /**
     * 重置密码
     */
    @PostMapping("resetPasswd")
    public Result<Void> resetPasswd(ResetPasswdReq args){
        userService.resetPasswd(args);
        return Result.ok("重置密码成功");
    }

    /**
     * 作废一个用户
     * 保留用户的数据, 但是该用户将无法登录和使用VPN
     * @param id 用户唯一标识
     */
    @GetMapping("invalid")
    public Result<Void> invalid(@PathParam("id") String id) {
        userService.invalid(id);
        return Result.ok("用户作废成功");
    }

    /**
     * 激活一个用户
     * 可以将作废的用户重新激活
     * @param id 用户唯一标识
     */
    @GetMapping("active")
    public Result<Void> active(@PathParam("id") String id){
        userService.active(id);
        return Result.ok("用户激活成功");
    }

    /**
     * 删除一个用户
     * 删除用户的所有数据, 包括用户的配置, 删除后无法恢复
     * @param id 用户唯一标识
     */
    @GetMapping("del")
    public Result<Void> del(@PathParam("id") String id){
        userService.delete(id);
        return Result.ok("用户删除成功");
    }

    /**
     * 添加一个用户配置
     * 注: 配置不会立即生效, 通常需要在一段时间后重连VPN才会生效
     */
    @PostMapping("config/add")
    public Result<Void> addUserConfig(@RequestBody UserConfigAdd args) {
        userConfigService.add(args);
        return Result.ok();
    }

    /**
     * 删除一个用户配置
     * 注: 配置不会立即生效, 通常需要在一段时间后重连VPN才会生效
     */
    @GetMapping("config/del")
    public Result<Void> delUserConfig(@PathParam("confId") String confId) {
        userConfigService.del(confId);
        return Result.ok();
    }

    /**
     * 查询用户配置
     */
    @GetMapping("config/query")
    public ListResult<UserConfigEntity> queryUserConfig(@PathParam("userId") String userId) {
        return ListResult.ofList(userConfigService.queryList(userId));
    }

    /**
     * 同步用户配置
     * 手动同步一次指定用户的配置, 使该用户的所有配置立刻生效
     */
    @GetMapping("config/sync")
    public Result<Void> syncUserConfig(@PathParam("userId") String userId) {
        userConfigService.syncUserConfig(userId);
        return Result.ok();
    }

}
