package me.meilon.vpnmgr.facade.management;

import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.service.ClientService;
import me.meilon.vpnmgr.infrastructure.management.Command;
import me.meilon.vpnmgr.infrastructure.management.OVMClientFactory;
import me.meilon.vpnmgr.infrastructure.management.OVMTypeHandler;
import me.meilon.vpnmgr.infrastructure.management.v3.message.ClientEvent;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.ClientActions;
import me.meilon.vpnmgr.infrastructure.management.v3.parsers.ClientNotificationParser;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
public class ClientEventHandler implements OVMTypeHandler<ClientEvent> {

    private final ClientService clientService;

    public ClientEventHandler(ClientService clientService) throws IOException {
        this.clientService = clientService;
        OVMClientFactory.addHandler(this);
    }

    @Override
    public Command handle(@Nonnull ClientEvent client) {
        ClientActions actions = client.getAction();
        return switch (actions) {
            case CONNECT,REAUTH -> clientService.onConnect(client);
            case ESTABLISHED -> {
                clientService.onEstablished(client);
                yield null;
            }
            case ADDRESS -> {
                log.info("client address: {}", client.toStr());
                yield null;
            }
            case DISCONNECT -> {
                clientService.onDisconnect(client);
                yield null;
            }
        };
    }



    @Override
    public Class<ClientNotificationParser> getParserClass() {
        return ClientNotificationParser.class;
    }

}
