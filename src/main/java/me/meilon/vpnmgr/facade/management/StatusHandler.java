package me.meilon.vpnmgr.facade.management;

import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.management.Command;
import me.meilon.vpnmgr.infrastructure.management.OVMTypeHandler;
import me.meilon.vpnmgr.infrastructure.management.Parser;
import me.meilon.vpnmgr.infrastructure.management.v3.message.StatusMessage;
import me.meilon.vpnmgr.infrastructure.management.v3.parsers.StatusParser;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class StatusHandler implements OVMTypeHandler<StatusMessage> {

    private StatusMessage cache;

    @Override
    public Command handle(@NotNull StatusMessage response) {
        this.cache = response;
        log.info("openvpn status: {}", response.toStr());
        return null;
    }

    public StatusMessage getStatus() {
        return cache;
    }

    @Override
    public Class<? extends Parser<StatusMessage>> getParserClass() {
        return StatusParser.class;
    }
}
