package me.meilon.vpnmgr.facade.socket;

import jakarta.websocket.*;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.management.OVMClientFactory;
import me.meilon.vpnmgr.infrastructure.management.OpenvpnManagementClient;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
@ServerEndpoint("/terminal")
public class ManagementWebSocketServer {

    private OpenvpnManagementClient openvpnClient;


    @OnOpen
    public void OnOpen(Session session){
        log.info("[WebSocket] 连接建立");
        try {
            if (openvpnClient == null){
                openvpnClient = OVMClientFactory.create(null);
            }
            openvpnClient.addErrorHandler(e -> session.getAsyncRemote().sendText("err:  "+e.getMessage()));
            openvpnClient.addHandler(response -> {
                try {
                    session.getBasicRemote().sendText(response.toStr());
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
        } catch (IOException e) {
            session.getAsyncRemote().sendText("telnet connect err "+e.getMessage());
        }
        log.info("[WebSocket] 连接成功");
    }

    @OnMessage
    public void OnMessage(String message_str){
        log.info("[WebSocket] 收到消息：{}",message_str);
        try {
            openvpnClient.send(message_str);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @OnError
    public void onError(Session session, Throwable error){
        log.error("发生错误", error);
    }


    @OnClose
    public void OnClose() {
        openvpnClient.addHandler(null);
        openvpnClient.addErrorHandler(null);
        log.info("[WebSocket] 退出成功");
    }


}
