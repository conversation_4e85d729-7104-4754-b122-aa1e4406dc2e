package me.meilon.vpnmgr.facade.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.service.UserConfigService;
import me.meilon.vpnmgr.infrastructure.biz.resource.event.ResourceGroupEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Set;

@Slf4j
@Component
@RequiredArgsConstructor
public class ResourceGroupListener  implements ApplicationListener<ResourceGroupEvent> {

    private final UserConfigService userConfigService;

    /**
     * 事件监听
     * 当资源组添加或删除资源时, 会触发该事件
     * 该事件会将资源组下的所有用户的配置同步到 openvpn
     * @param event the event to respond to
     */
    @Async
    @Override
    public void onApplicationEvent(ResourceGroupEvent event) {
        Set<String> userIds = userConfigService.queryUserIdsByGroupId(event.getGroup().getId());
        for (String userId : userIds) {
            log.info("syncUserConfig userId: {}", userId);
            userConfigService.syncUserConfig(userId);
        }
    }
}
