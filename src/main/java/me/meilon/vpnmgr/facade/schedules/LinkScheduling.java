package me.meilon.vpnmgr.facade.schedules;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.args.base.PageQry;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.ClientLinkRecordDo;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.ClientLinkRecordRepository;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.ClientLinkStatusDo;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.ClientLinkStatusRepository;
import me.meilon.vpnmgr.infrastructure.utils.DateUtil;
import me.meilon.vpnmgr.infrastructure.utils.PageUtils;
import me.meilon.vpnmgr.infrastructure.utils.json.JsonUtil;
import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;


@Slf4j
@Component
@Profile("prd")
@RequiredArgsConstructor
public class LinkScheduling {

    private final ClientLinkStatusRepository linkStatusRepository;
    private final ClientLinkRecordRepository linkRecordRepository;

    /**
     * 每天清理链接记录
     */
    @Scheduled(cron="0 0 1 * * ? ")
    public void test(){
        List<ClientLinkStatusDo> statusList = linkStatusRepository.findAll();
        LocalDateTime now = LocalDateTime.now();
        for (ClientLinkStatusDo statusDo : statusList) {
            if (DateUtil.daysDiff(statusDo.getConnectTime(), now) < 100){
                continue;
            }
            // 删除100天未登录的客户端
            log.info("delete client link status {}", JsonUtil.toJsonString(statusDo));
            linkStatusRepository.delete(statusDo);
        }

        PageQry pageQry =new PageQry();
        pageQry.setPageSize(100);
        pageQry.setPageNumber(0);
        Sort sort = Sort.by(Sort.Direction.ASC, "connectTime");
        Page<ClientLinkRecordDo> res;
        do {
            Pageable pageable = PageUtils.of(pageQry, sort);
            res = linkRecordRepository.findAll(pageable);
            for (ClientLinkRecordDo re : res) {
                if (DateUtil.daysDiff(re.getConnectTime(), now) < 30){
                    continue;
                }
                // 删除30天以前的记录
                log.info("delete client link record {}", JsonUtil.toJsonString(re));
                linkRecordRepository.delete(re);
            }
        }while (res.hasNext());
    }
}
