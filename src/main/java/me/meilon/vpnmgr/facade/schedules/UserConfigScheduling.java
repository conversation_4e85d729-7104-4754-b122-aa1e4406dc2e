package me.meilon.vpnmgr.facade.schedules;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.service.UserConfigService;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserDo;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserRepository;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;


@Slf4j
@Component
@Profile("prd")
@RequiredArgsConstructor
public class UserConfigScheduling {

    private final SystemUserRepository systemUserRepository;
    private final UserConfigService userConfigService;

    /**
     * 定时同步用户配置
     */
    // @Scheduled(timeUnit = TimeUnit.MINUTES, fixedDelay = 60, initialDelay = 1)
    public void syncUserConfig(){
        for (SystemUserDo userDo : systemUserRepository.findAll()) {
            userConfigService.syncUserConfig(userDo);
        }
        // log.info("同步用户配置");
    }
}
