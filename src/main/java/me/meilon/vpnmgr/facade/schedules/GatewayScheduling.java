package me.meilon.vpnmgr.facade.schedules;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.biz.resource.jpa.ResourceGroupRepository;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserRepository;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.UserConfigRepository;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;


@Slf4j
@Component
@Profile("local")
@RequiredArgsConstructor
public class GatewayScheduling {

    private final SystemUserRepository userRepository;
    private final ResourceGroupRepository groupRepository;
    private final UserConfigRepository configRepository;

    /**
     * 定时同步用户配置
     */
    // @Scheduled(timeUnit = TimeUnit.SECONDS, fixedDelay = 6000, initialDelay = 1)
    public void syncUserConfig(){
        // List<SystemUserDo> userDo = userRepository.findByUserType(UserType.Gateway);
        // for (SystemUserDo systemUserDo : userDo) {
        //     ResourceGroupDo groupDo = groupRepository.findByName(systemUserDo.getUserName()).orElse(null);
        //     if (groupDo == null) {
        //         groupDo = new ResourceGroupDo();
        //         groupDo.setName(systemUserDo.getUserName());
        //         groupDo.setGroupType(GroupType.System);
        //         groupDo.setRemark("VPN网关["+systemUserDo.getUserName()+"]默认子网资源组");
        //         groupRepository.save(groupDo);
        //     }
        // }

        // ResourceGroupDo groupDo = groupRepository.findByName("default").orElse(null);
        // String id = groupDo.getId();
        // log.info("id:{}", id);
        // List<SystemUserDo> userDos = userRepository.findByUserType(UserType.Person);
        //
        // for (SystemUserDo userDo : userDos) {
        //     UserConfigDo configDo = new UserConfigDo();
        //     configDo.setUserId(userDo.getId());
        //     configDo.setType(UserConfigType.access_group);
        //     configDo.setObjId(id);
        //     log.info("configDo:{}", JsonUtil.toJsonString(configDo));
        //     configRepository.save(configDo);
        //
        // }

    }


}
