package me.meilon.vpnmgr.facade.schedules;



import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.service.IpLeasesService;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.IpLeasesDo;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.IpLeasesRepository;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.util.List;


/**
 * IP 池定时任务
 *
 */
@Slf4j
@Component
@Profile("prd")
@RequiredArgsConstructor
public class IpPoolScheduling {

  private final IpLeasesRepository leasesRepository;
  private final IpLeasesService leasesService;


  /**
   * 每天检查IP地址的续约情况, 回收过期的IP地址
   */
  @Scheduled(cron="0 0 1 * * ? ")
  public void recycled(){
    List<IpLeasesDo> leasesDos = leasesRepository.findAll();
    if (leasesDos.isEmpty()){
      return;
    }
    LocalDateTime now =LocalDateTime.now();
    for (IpLeasesDo leasesDo : leasesDos) {
      // 判断过期时间是否超过30天
      LocalDateTime expire = leasesDo.getExpireTime();
      if (expire == null){
        continue;
      }
      if (expire.plusDays(30).isBefore(now)){
        // 如果超过30天，则回收该IP地址
        leasesService.recycled(leasesDo);
      }
    }
  }
}
