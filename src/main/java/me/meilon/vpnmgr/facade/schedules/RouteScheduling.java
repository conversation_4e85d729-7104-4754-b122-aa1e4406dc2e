package me.meilon.vpnmgr.facade.schedules;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.dto.GroupDto;
import me.meilon.vpnmgr.app.service.GroupService;
import me.meilon.vpnmgr.infrastructure.biz.resource.cli.RouteCli;
import me.meilon.vpnmgr.infrastructure.biz.resource.jpa.ResourceDo;
import me.meilon.vpnmgr.infrastructure.biz.resource.jpa.ResourceRelationDo;
import me.meilon.vpnmgr.infrastructure.biz.resource.jpa.ResourceRelationRepository;
import me.meilon.vpnmgr.infrastructure.biz.resource.jpa.ResourceRepository;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@Component
@Profile("prd")
@RequiredArgsConstructor
public class RouteScheduling {

    private final ResourceRepository resourceRepository;
    private final ResourceRelationRepository resourceRelationRepository;
    private final GroupService groupService;
    private final RouteCli routeCli;

    /**
     * 检查路由配置
     * 如果失效则重新添加
     */
    @Scheduled(timeUnit = TimeUnit.SECONDS, fixedRate=60, initialDelay = 1)
    public void syncUserConfig() {
        Map<String,String> grouMap = groupService.queryListByNotDefault()
                .stream()
                .collect(Collectors.toMap(GroupDto::getId, GroupDto::getName));

        List<ResourceRelationDo> relations = resourceRelationRepository.findAll();
        List<String> resourceIds = relations.stream()
                .filter(group -> grouMap.containsKey(group.getGroupId()))
                .map(ResourceRelationDo::getResourceId)
                .distinct()
                .collect(Collectors.toList());

        List<ResourceDo> resources = resourceRepository.findAllById(resourceIds);
        for (ResourceDo item : resources) {
            routeCli.addRoute(item);
        }

    }


}
