package me.meilon.vpnmgr.app.args.user;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.UserType;


@Getter
@Setter
public class UserAdd {

    /**
     * 用户编码
     */
    @Nonnull
    private String userCode;
    /**
     * 用户真实姓名
     */
    @Nonnull
    private String userName;
    /**
     * 密码
     */
    private String password;
    /**
     * 邮箱
     */
    private String mail;
    /**
     * 手机号
     */
    private String mobilePhone;
    /**
     * 用户类型
     */
    private UserType type;
    /**
     * 关联证书名
     */
    private String clientName;
    /**
     * 绑定IP
     */
    private String bindIp;

}
