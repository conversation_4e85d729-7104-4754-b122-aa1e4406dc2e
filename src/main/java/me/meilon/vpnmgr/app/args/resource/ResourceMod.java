package me.meilon.vpnmgr.app.args.resource;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ResourceMod {

    /**
     * 资源ID
     */
    @Nonnull
    private String id;
    /**
     * 资源名称
     */
    private String name;
    /**
     * 资源IP地址
     */
    private String ip;
    /**
     * 子网掩码
     * 可选, 不填则默认 ***************
     */
    private String netmask;
}
