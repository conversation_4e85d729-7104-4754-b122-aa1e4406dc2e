package me.meilon.vpnmgr.app.args.resource;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ResourceAdd {

    /**
     * 资源IP地址
     */
    @Nonnull
    private String ip;
    /**
     * 资源名称
     */
    @Nonnull
    private String name;
    /**
     * 子网掩码
     * 可选, 不填则默认 ***************
     */
    private String netmask;
    /**
     * 资源组ID
     * 可选, 如果填了资源组ID, 则新建的资源将同时被添加到指定资源组中
     */
    private String groupId;
}
