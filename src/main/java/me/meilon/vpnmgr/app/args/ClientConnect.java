package me.meilon.vpnmgr.app.args;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ClientConnect {
  /**
   * 会话ID
   */
  private String sessionId;
  /**
   * 客户端ID
   */
  private Long clientId;
  /**
   * 与给定客户端 TLS 会话关联的密钥的数字 ID
   */
  private Long keyId;
  /**
   * 会话状态
   * Initial
   * 客户端未提供令牌
   * Authenticated
   * 令牌有效且未过期。
   * Expired
   * 令牌有效但已过期。
   * Invalid
   * 令牌无效（HMAC 失败或长度错误）。
   * AuthenticatedEmptyUser / ExpiredEmptyUser
   * 令牌与客户端发送的用户名不匹配，但如果我们假设使用空用户名，则令牌将有效（或已过期）。
   * 这两种情况是对 OpenVPN 3 行为的解决方法。
   * 如果不需要此解决方法，则应将这两种情况与 .Invalid 处理方式相同。
   * 警告：仅在每次验证时调用身份验证方法时使用此功能。
   * 由于外部身份验证被调用，因此它还需要指示身份验证的成功或失败。
   * 强烈建议在使用 external-auth 选项时，在 Invalid/Expired auth-token 的情况下返回身份验证失败，
   * 除非客户端可以以另一种可接受的方式进行身份验证（例如客户端证书），否则返回成功将导致身份验证绕过（与从脚本返回错误密码时返回成功一样）。
   */
  private String sessionState;
  /**
   * 用户编码
   */
  private String userCode;
  /**
   * 设备类型
   */
  private String deviceType;
  /**
   * 客户端版本
   */
  private String clientVersion;
  /**
   * 客户端IP地址
   */
  private String trustedAddress;
  /**
   * 客户端虚拟IP
   */
  @Nonnull
  private String clientIp;
  /**
   * 链接的服务端IP
   */
  private String serverIp;
}
