package me.meilon.vpnmgr.app.args.user;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.UserConfigType;

@Getter
@Setter
public class UserConfigAdd {

    /**
     * 用户Id
     */
    @Nonnull
    private String userId;
    /**
     * 配置类型
     * access: 访问
     * access_group: 访问组
     * subnet: 子网
     * subnet_group: 子网组
     */
    @Nonnull
    private UserConfigType type;
    /**
     * 配置值
     */
    private String value;
    /**
     * 资源对象ID
     */
    private String objectId;
}
