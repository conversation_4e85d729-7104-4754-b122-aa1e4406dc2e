package me.meilon.vpnmgr.app.args;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CertificateAdd {
    /**
     * 用户名
     */
    @Nonnull
    private String userName;

    /**
     * 密码(可选)
     * 为空则创建一个没有密码的证书
     */
    private String passwd;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效期(单位天)
     */
    @Nonnull
    private Integer expiration;
}
