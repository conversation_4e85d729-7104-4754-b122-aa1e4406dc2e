package me.meilon.vpnmgr.app.args;

import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.app.args.base.PageQry;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.enums.ConnState;

@Getter
@Setter
public class ConnStatusPageQry extends PageQry {

    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 客户端IP
     */
    private String clientIp;
    /**
     * 连接状态
     */
    private ConnState status;
}
