package me.meilon.vpnmgr.app.args.user;

import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.app.args.base.PageQry;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.UserType;

@Setter
@Getter
public class UserPageQry extends PageQry {

    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 用户类型
     */
    private UserType type;
    /**
     * 真实姓名
     */
    private String userName;
    /**
     * 手机号
     */
    private String mobilePhone;
    /**
     * 状态
     * 0: 失效
     * 1: 生效
     */
    private Integer statusCd;

}
