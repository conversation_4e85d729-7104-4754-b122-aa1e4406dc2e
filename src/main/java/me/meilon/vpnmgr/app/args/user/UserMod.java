package me.meilon.vpnmgr.app.args.user;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.AuthMode;


@Getter
@Setter
public class UserMod {

    /**
     * 用户唯一标识
     */
    @Nonnull
    private String id;
    /**
     * 用户真实姓名
     */
    private String userName;
    /**
     * 邮箱
     */
    private String mail;
    /**
     * 手机号
     */
    private String mobilePhone;
    /**
     * 双因素认证
     * (NONE: 不认证, OTP: 动态码认证, DENY: 禁止链接)
     */
    private AuthMode authMode;
    /**
     * 关联证书名
     */
    private String clientName;
    /**
     * 绑定IP
     */
    private String bindIp;

}
