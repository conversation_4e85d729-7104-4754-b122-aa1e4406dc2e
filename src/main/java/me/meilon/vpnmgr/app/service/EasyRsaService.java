package me.meilon.vpnmgr.app.service;

import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.args.CerPageQry;
import me.meilon.vpnmgr.app.args.CertificateAdd;
import me.meilon.vpnmgr.app.dto.CertificateDto;
import me.meilon.vpnmgr.infrastructure.biz.certificate.files.CertificateVo;
import me.meilon.vpnmgr.infrastructure.biz.certificate.files.EasyRsaDao;
import me.meilon.vpnmgr.infrastructure.biz.certificate.jpa.CertificateDo;
import me.meilon.vpnmgr.infrastructure.biz.certificate.jpa.CertificateRepository;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.files.OpenvpnDao;
import me.meilon.vpnmgr.infrastructure.conf.EasyRsaProperty;
import me.meilon.vpnmgr.infrastructure.conf.OpenvpnProperty;
import me.meilon.vpnmgr.infrastructure.exception.ValidationFailed;
import me.meilon.vpnmgr.infrastructure.utils.FileUtil;
import me.meilon.vpnmgr.infrastructure.utils.StrUtils;
import me.meilon.vpnmgr.infrastructure.utils.shell.ProcessResult;
import me.meilon.vpnmgr.infrastructure.utils.shell.ProcessRunTime;
import me.meilon.vpnmgr.infrastructure.utils.shell.ShellRunTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class EasyRsaService {

    private final OpenvpnProperty openvpnProperty;
    private final EasyRsaProperty easyRsaProperty;
    private final CertificateRepository certificateRepository;
    private final OpenvpnDao openvpnDao;
    private final EasyRsaDao easyRsaDao;
    private static final Pattern pattern = Pattern.compile("^[a-zA-Z]\\w{4,15}$");



    @Transactional(rollbackFor = Throwable.class)
    public CertificateDto create(@Nonnull CertificateAdd args){

        String cn = args.getUserName();
        if (!pattern.matcher(cn).matches()) {
            throw new RuntimeException("客户端名称必须以字母开头，长度在5~16之间，只能包含字母、数字和下划线");
        }
        if (easyRsaDao.existsCert(cn) || certificateRepository.existsByClientName(cn)) {
            throw new RuntimeException("存在重复证书");
        }
        // 保存数据库
        CertificateDo certificateDo = CertificateDo.create(args);
        certificateDo.setStatusCd("V");
        certificateDo.setFileName(args.getUserName() + ".ovpn");
        certificateDo.setCreateTime(LocalDateTime.now());
        CertificateDto newCer = certificateRepository.save(certificateDo).toDto();

        // 构建客户端证书
        buildClientFull(args);
        //创建客户端配置文件
        createClientFile(cn);

        return newCer;
    }

    private void buildClientFull(CertificateAdd args){
        ShellRunTime shell = ShellRunTime.getRuntime();
        String cn = args.getUserName();
        String exp = "--days="+args.getExpiration();
        String easyrsa = easyRsaProperty.getRunFile();
        String password = args.getPasswd();
        ProcessResult out;
        shell.cd(easyRsaProperty.getBaseDir());
        if (StrUtils.isBlank(password)){
            // 没有密码时
            out = shell.execAndWaitResult(easyrsa, "--batch", exp, "build-client-full", cn, "nopass");
        }
        else {
            // 有密码
            try (ProcessRunTime processRunTime = shell.execInteractive(easyrsa, "--batch", exp, "build-client-full", cn)) {
                processRunTime.write(password);
                processRunTime.write(password);
                // processRunTime.waitOnWrite("Enter PEM pass phrase:*", password);
                // processRunTime.waitOnWrite("Verifying - Enter PEM pass phrase:*", password);
                out = processRunTime.waitResult(Duration.ofSeconds(30));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        out.orElseThrow("构建客户端证书失败: \n");
    }

    @Transactional(rollbackFor = Throwable.class)
    public void revokeClient(@Nonnull String cn){
        certificateRepository.findOneByClientName(cn).ifPresent(cer->{
            cer.setStatusCd("R");
            certificateRepository.save(cer);
        });

        List<CertificateVo> certificateVos = easyRsaDao.readClientAll(line-> line.getClientName().equals(cn));
        if (certificateVos.isEmpty()){
            throw new ValidationFailed("客户端证书不存在");
        }
        String easyDir = easyRsaProperty.getBaseDir();
        String easyRsa = easyRsaProperty.getRunFile();
        String serverDir = openvpnProperty.getServerDir();
        String clrPath = FileUtil.unite(serverDir, "crl.pem");
        ProcessResult res;
        ShellRunTime shell = ShellRunTime.getRuntime();
        shell.cd(easyDir);
        res = shell.execAndWaitResult(easyRsa, "--batch", "revoke",cn);
        res.orElseThrow();
        shell.execAndWaitResult(easyRsa, "--batch", "--days=25550", "gen-crl");
        res.orElseThrow();
        shell.execAndWaitResult("rm","-f", clrPath);
        res.orElseThrow();
        shell.execAndWaitResult("cp", FileUtil.unite(easyDir, "pki/crl.pem"), clrPath);
        res.orElseThrow();
        shell.execAndWaitResult("chown", "nobody:nobody", clrPath);
        res.orElseThrow();
    }

    public Page<CertificateDto> query(@Nonnull CerPageQry queryCer) {
        Pageable pageable = PageRequest.of(queryCer.getPageNumber(), queryCer.getPageSize());
        Map<String,CertificateVo> clientMap = readClientMap();
        return certificateRepository.query(pageable, queryCer)
                .map(cer ->{
                    CertificateDto dto = cer.toDto();
                    CertificateVo value = clientMap.get(dto.getClientName());
                    if (value != null){
                        dto.setSerialNumber(value.getSerialNumber());
                        if ("V".equals(value.getStatusCd())){
                            dto.setStatusCd("V");
                            dto.setStatus("有效");
                            return dto;
                        }
                        else if ("R".equals(value.getStatusCd())){
                            dto.setStatusCd("R");
                            dto.setStatus("失效");
                            return dto;
                        }
                    }
                    dto.setStatusCd("U");
                    dto.setStatus("未知");
                    dto.setClientName("未知");
                    return dto;
                });
    }

    public void createClientFile(@Nonnull String cn){
        List<String> temp = openvpnDao.readClientTemplate();

        temp.add("<ca>");
        List<String> ca = easyRsaDao.readCa();
        temp.addAll(ca);
        temp.add("</ca>");

        temp.add("<cert>");
        List<String> cert = easyRsaDao.readCertBody(cn);
        temp.addAll(cert);
        temp.add("</cert>");

        temp.add("<key>");
        List<String> key = easyRsaDao.readKey(cn);
        temp.addAll(key);
        temp.add("</key>");

        temp.add("<tls-crypt>");
        List<String> tls = openvpnDao.readTlsCryptBody();
        temp.addAll(tls);
        temp.add("</tls-crypt>");

        openvpnDao.saveClient(temp, cn);
    }

    private Map<String,CertificateVo> readClientMap(){
        List<CertificateVo> res = easyRsaDao.readClientAll(null);
        return res.stream().collect(Collectors
                .toMap(CertificateVo::getClientName, cer -> cer)
        );
    }

}
