package me.meilon.vpnmgr.app.service;

import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.IpLeasesDo;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.IpLeasesRecordDo;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.IpLeasesRecordRepository;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.IpLeasesRepository;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.UserType;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserDo;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserRepository;
import me.meilon.vpnmgr.infrastructure.conf.OpenvpnProperty;
import me.meilon.vpnmgr.infrastructure.utils.StrUtils;
import me.meilon.vpnmgr.infrastructure.utils.ip.IPAddressPool;
import me.meilon.vpnmgr.infrastructure.utils.ip.IpAddress;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;


@Slf4j
@Service
@RequiredArgsConstructor
public class IpLeasesService {

  private final IpLeasesRepository leasesRepository;
  private final IpLeasesRecordRepository leasesRecordRepository;
  private final SystemUserRepository userRepository;
  private final IPAddressPool ipAddressPool;
  private final OpenvpnProperty property;


  /**
   * 手动指定静态IP
   * 当手动指定了用户静态IP时调用;
   * <br>
   * 此方法应该在创建用户时调用, 如果IP已被占用, 则抛出异常创建用户失败
   * @param userCode 用户编码
   * @param staticIp 静态IP地址
   */
  public void manuallySpecifyStaticIP(@Nonnull String userCode, @Nonnull String staticIp){
    IpAddress ipAddress = new IpAddress(staticIp);
    // 检查IP是否已被使用
    IpLeasesDo leasesDo = leasesRepository.findById(ipAddress.toLong())
        .orElse(null);
    if (leasesDo != null){
      throw new IllegalStateException("IP地址已被占用");
    }
    // IP未被占用, 直接分配
    LocalDateTime now = LocalDateTime.now();
    leasesDo = new IpLeasesDo();
    leasesDo.setId(ipAddress.toLong());
    leasesDo.setUserCode(userCode);
    leasesDo.setRecycled(false);
    leasesDo.setStartTime(now);
    leasesDo.setRenewTime(now);
    leasesDo.setExpireTime(null);
    leasesRepository.save(leasesDo);
  }

  /**
   * 检查用户IP是否即将过期
   *
   * @param userCode 用户编码
   * @return true: IP即将过期; false: IP未过期
   */
  public boolean checkIpAboutToExpire(@Nonnull String userCode) {
    IpLeasesDo lease = leasesRepository.findOneByUserCode(userCode)
        .orElse(null);
    if (lease == null){
      log.info("用户 {} 没有IP租约", userCode);
      return true;
    }
    LocalDateTime expireTime = lease.getExpireTime();
    if (expireTime == null){
      // IP租约未设置过期时间, 说明是静态IP, 不会过期
      return false;
    }
    LocalDateTime now = LocalDateTime.now();
    int days = property.getIpPool().getRenewThreshold();
    return now.isAfter(expireTime.minusDays(days));
  }


  /**
   * 自动分配 IP 地址
   * 如果客户端已分配IP, 且未过期, 则直接使用
   * 如果客户端未分配IP, 则优先从已回收IP中分配一个
   * 如果美哟可用的已回收IP, 则从IP池中分配一个
   * <br>
   * 此方法应该在客户端连接时调用
   */
  public synchronized void allocateIp(@Nonnull SystemUserDo user) {

    // 是否是静态IP, 两种情况 1.客户端类型网关, 2.客户端手动设置了IP地址
    boolean isStatic = user.getUserType() == UserType.Gateway;

    String userCode = user.getUserCode();
    // 查询客户是否已有IP租约
    IpLeasesDo oldLease = leasesRepository.findOneByUserCode(userCode)
        .orElse(null);

    if (oldLease == null){
      IpAddress ipAddress = ipAddressPool.allocateIP();
      if (ipAddress == null){
        throw new IllegalStateException("没有可用的IP地址");
      }
      log.info("为用户 {} 分配IP地址 {}", userCode, ipAddress);
      oldLease = new IpLeasesDo();
      oldLease.setId(ipAddress.toLong());
      oldLease.setUserCode(userCode);
      user.setBindIp(ipAddress.toString());
      userRepository.save(user);
    }
    else {
      // 用户已有IP租约, 可直接使用
      log.info("用户 {} 已有IP租约 {}", userCode, new IpAddress(oldLease.getId()));
    }
    LocalDateTime now = LocalDateTime.now();
    oldLease.setRecycled(false);
    oldLease.setStartTime(now);
    oldLease.setRenewTime(now);
    // 静态IP租约永不过期
    oldLease.setExpireTime(isStatic ? null : now.plusDays(property.getIpPool().getLeaseTime()));
    leasesRepository.save(oldLease);

    ipAddressPool.markAsUsed(oldLease.getId());
  }

  /**
   * 释放IP地址
   */
  public void recycled(@Nonnull String ip){
    IpAddress ipAddress = new IpAddress(ip);
    IpLeasesDo leasesDo = leasesRepository.findById(ipAddress.toLong())
        .orElse(null);
    if (leasesDo == null){
      return;
    }
    recycled(leasesDo);
  }

  public void recycled(@Nonnull IpLeasesDo leasesDo){
    // 如果超过30天，则回收该IP地址
    log.info("recycle ip address {}", leasesDo.getId());
    LocalDateTime now = LocalDateTime.now();
    leasesDo.setRecycled(true);
    leasesDo.setExpireTime(now);
    leasesRepository.save(leasesDo);
    ipAddressPool.markAsRecycled(leasesDo.getId());
    // 添加到IP租约记录
    IpLeasesRecordDo leasesRecordDo = new IpLeasesRecordDo();
    leasesRecordDo.setIpAddr(leasesDo.getId());
    leasesRecordDo.setUserCode(leasesDo.getUserCode());
    leasesRecordDo.setStartTime(leasesDo.getStartTime());
    leasesRecordDo.setExpireTime(now);
    leasesRecordDo.setLastRenewTime(leasesDo.getRenewTime());
    leasesRecordRepository.save(leasesRecordDo);
  }



}
