package me.meilon.vpnmgr.app.service;

import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.args.ResetPasswdReq;
import me.meilon.vpnmgr.app.args.user.UserAdd;
import me.meilon.vpnmgr.app.args.user.UserMod;
import me.meilon.vpnmgr.app.args.user.UserPageQry;
import me.meilon.vpnmgr.app.args.user.UserPasswdMod;
import me.meilon.vpnmgr.app.dto.UserDto;
import me.meilon.vpnmgr.infrastructure.biz.resource.jpa.ResourceGroupDo;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.StatusEnum;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.UserConfigType;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.UserType;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserDo;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserRepository;
import me.meilon.vpnmgr.infrastructure.conf.OpenvpnProperty;
import me.meilon.vpnmgr.infrastructure.exception.ValidationFailed;
import me.meilon.vpnmgr.infrastructure.utils.PageUtils;
import me.meilon.vpnmgr.infrastructure.utils.StrUtils;
import me.meilon.vpnmgr.infrastructure.web.Token;
import me.meilon.vpnmgr.infrastructure.web.TokenCache;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.regex.Pattern;


@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

  private final TokenCache tokenCache;
  private final SystemUserRepository userRepository;
  private final UserConfigService userConfigService;
  private final GroupService groupService;
  private final IpLeasesService leasesService;
  private final OpenvpnProperty property;
  private static final Pattern userPattern = Pattern.compile("^[a-zA-Z]\\w{2,15}$");
  private static final Pattern pwdPattern = Pattern.compile("^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).{8,20}$");


  public Page<UserDto> query(@Nonnull UserPageQry queryUser) {
    Pageable pageable = PageUtils.of(queryUser);
    return userRepository.query(pageable, queryUser)
            .map(SystemUserDo::toDto);
  }

  /**
   * 创建用户
   */
  @Transactional
  public void create(@Nonnull UserAdd args) {
    String userCode = args.getUserCode();
    if (userCode.isEmpty() || !userPattern.matcher(userCode).matches()) {
      throw new ValidationFailed("用户编码必须以字母开头，长度在3~16之间，只能包含字母、数字和下划线");
    }
    String passwd = args.getPassword();
    String bindIP = args.getBindIp();

    checkPassword(passwd);
    checkIp(bindIP);
    userRepository.findOneByUserCode(userCode).ifPresent(userDo -> {
      throw new ValidationFailed("用户名已被使用");
    });

    SystemUserDo userDo = SystemUserDo.create(args);
    if (StrUtils.isNotBlank(userDo.getBindIp())) {
      // 如果手动指定了静态IP, 则立即绑定
      leasesService.manuallySpecifyStaticIP(userCode, bindIP);
    }
    userDo = userRepository.saveAndFlush(userDo);
    if (args.getType() == UserType.Gateway) {
      // 如果是网关，创建并关联网关组
      ResourceGroupDo groupDto = groupService.create(userDo);
      userConfigService.add(userDo.getId(), UserConfigType.subnet_group, null, groupDto.getId());
    } else {
      // 如果是普通用户，关联默认资源组
      ResourceGroupDo defGroup = groupService.findDefault();
      userConfigService.add(userDo.getId(), UserConfigType.access_group, null, defGroup.getId());
    }
    userConfigService.syncUserConfig(userDo);
  }

  private void checkIp(String bindIP) {
    String subNet = property.getSubNetwork();
    if (StrUtils.isNotBlank(bindIP)) {
      if (!bindIP.startsWith(subNet)) {
        throw new ValidationFailed("绑定IP必须在 " + subNet + " 段内");
      }
//      if (userRepository.existsByBindIp(bindIP)) {
//        throw new ValidationFailed("IP 已被占用");
//      }
    }
  }

  /**
   * 作废用户
   * @param userId 用户id
   */
  public void invalid(@Nonnull String userId) {
    SystemUserDo user = userRepository.findById(userId)
            .orElseThrow(() -> new ValidationFailed("用户不存在"));
    if (user.getStatus() == StatusEnum.DISABLED) {
      return;
    }
    String bindIp = user.getBindIp();
    if (StrUtils.isNotBlank(bindIp)) {
      // 如果绑定了静态IP, 则释放该IP
      leasesService.recycled(bindIp);
      user.setBindIp(null);
    }
    user.updateStatus(StatusEnum.DISABLED);
    userRepository.saveAndFlush(user);



  }

  /**
   * 激活用户
   *
   * @param userId 用户id
   */
  public void active(@Nonnull String userId) {
    SystemUserDo user = userRepository.findById(userId)
            .orElseThrow(() -> new ValidationFailed("用户不存在"));
    if (user.getStatus() == StatusEnum.ENABLE) {
      return;
    }
    user.updateStatus(StatusEnum.ENABLE);
    userRepository.saveAndFlush(user);
  }

  /**
   * 删除用户
   *
   * @param userId 用户id
   */
  @Transactional
  public void delete(@Nonnull String userId) {
    SystemUserDo user = userRepository.findById(userId).orElse(null);
    if (user == null) {
      return;
    }
    if (user.getStatus() != StatusEnum.DISABLED) {
      throw new ValidationFailed("只能删除已作废的用户");
    }
    // List<UserConfigEntity> userConfigEntities = userConfigService.queryList(userId);
    // boolean isGateway = user.getUserType() == UserType.Gateway;
    // for (UserConfigEntity userConfigEntity : userConfigEntities) {
    //   // 删除用户配置
    //   userConfigService.del(userConfigEntity.getId());
    //   // 删除网关自动生成的资源组
    //   if (isGateway && userConfigEntity.getType() == UserConfigType.subnet_group) {
    //     groupService.delete(userConfigEntity);
    //   }
    // }
    userConfigService.deleteByUser(user);
    userRepository.delete(user);
  }

  /**
   * 修改用户基本信息
   */
  @Transactional
  public void update(@Nonnull UserMod args) {
    SystemUserDo user = userRepository.findById(args.getId())
            .orElseThrow(() -> new ValidationFailed("用户不存在"));
    String userCode = user.getUserCode();
    String userName = args.getUserName();
    if (StrUtils.notEqualsAndEmpty(userName, user.getUserName())) {
      log.info("用户 {} 修改用户名称 {} -> {}", userCode, user.getUserName(), userName);
      user.setUserName(args.getUserName());
    }
    if (StrUtils.notEquals(args.getMobilePhone(), user.getMobilePhone())) {
      log.info("vpn 用户 {} 修改手机号 {} -> {}", userCode, user.getMobilePhone(), args.getMobilePhone());
      user.setMobilePhone(args.getMobilePhone());
    }
    if (StrUtils.notEquals(args.getMail(), user.getMail())) {
      log.info("vpn 用户 {} 修改邮箱 {} -> {}", userCode, user.getMail(), args.getMail());
      user.setMail(args.getMail());
    }
    if (StrUtils.notEqualsAndEmpty(args.getAuthMode(), user.getAuthMode())) {
      log.info("vpn 用户 {} 修改认证方式 {} -> {}", userCode, user.getAuthMode(), args.getAuthMode());
      user.setAuthMode(args.getAuthMode());
    }
    String cn = args.getClientName();
    if (StrUtils.notEquals(user.getCertificate(), cn)) {
      log.info("vpn 用户 {} 修改绑定证书 {} -> {}", userCode, user.getCertificate(), cn);
      user.setCertificate(args.getClientName());
    }
    String bindIP = args.getBindIp();
    if (StrUtils.notEquals(bindIP, user.getBindIp())){
      log.info("vpn 用户 {} 修改绑定IP {} -> {}", userCode, user.getBindIp(), args.getBindIp());
      user.setBindIp(bindIP);
      if (StrUtils.isNotBlank(bindIP)){
        if (!bindIP.startsWith(property.getSubNetwork())) {
          throw new ValidationFailed("绑定IP必须在 " + property.getSubNetwork() + " 段内");
        }
        leasesService.manuallySpecifyStaticIP(userCode, bindIP);
      }

    }
//    String subNet = property.getSubNetwork();
//    boolean ipNotEquals = StrUtils.notEquals(user.getBindIp(), bindIP);
//    boolean ipNotBlank = StrUtils.isNotBlank(bindIP);
//    if (ipNotEquals) {
//      if (ipNotBlank) {
//        if (!bindIP.startsWith(subNet)) {
//          throw new ValidationFailed("绑定IP必须在 " + subNet + " 段内");
//        }
//        if (userRepository.existsByBindIp(bindIP)) {
//          throw new ValidationFailed("IP 已被占用");
//        }
//      }
//      log.info("vpn 用户 {} 修改绑定IP {} -> {}", user.getUserCode(), user.getBindIp(), args.getBindIp());
//      user.setBindIp(args.getBindIp());
//    }
    userRepository.save(user);
    userConfigService.syncUserConfig(user);
  }

  /**
   * 修改密码
   *
   * @param id   用户id
   * @param args 密码参数
   */
  public void updatePasswd(@Nonnull String id, @Nonnull UserPasswdMod args) {
    String newPwd = args.getPwd();
    checkPassword(newPwd);
    if (newPwd.equals(args.getOldPwd())) {
      throw new ValidationFailed("新密码不能与旧密码相同");
    }
    SystemUserDo user = userRepository.findById(id)
            .orElseThrow(() -> new ValidationFailed("用户不存在"));
    if (!user.comparePassword(args.getOldPwd())) {
      throw new ValidationFailed("旧密码不一致!");
    }
    updatePasswd(user, newPwd);
  }

  /**
   * 修改密码
   *
   * @param user   用户
   * @param newPwd 新密码
   */
  public void updatePasswd(@Nonnull SystemUserDo user, @Nonnull String newPwd) {
    log.info("用户 {} 修改密码", user.getUserName());
    user.updatePassword(newPwd);
    user.setTempPwd(false);
    userRepository.save(user);
  }

  /**
   * 重置密码
   */
  public void resetPasswd(@Nonnull ResetPasswdReq args) {
    String newPwd = args.getPwd();
    checkPassword(newPwd);
    SystemUserDo user = userRepository.findById(args.getUserId())
            .orElseThrow(() -> new ValidationFailed("用户不存在"));
    log.info("用户 {} 重置密码", user.getUserName());
    user.updatePassword(args.getPwd());
    user.setTempPwd(true);
    userRepository.save(user);
  }

  /**
   * 检查密码是否符合规范
   *
   * @param pwd 密码
   * @return true 不符合规则, false 符合规则
   */
  public boolean matchPassword(String pwd) {
    return pwd == null || pwd.isEmpty() || !pwdPattern.matcher(pwd).matches();
  }


  public String login(String userCode, String pwd) {
    SystemUserDo userDo = userRepository.findOneByUserCode(userCode)
            .orElse(null);
    if (userDo == null || userDo.getStatus() == StatusEnum.DISABLED
            || !userDo.comparePassword(pwd)) {

      throw new ValidationFailed("用户名或密码错误");
    }
    Token token = tokenCache.create(userDo);
    if ("admin".equals(userCode)) {
      token.addPermissionAll();
    } else {
      token.addPermissions(
              "/api/current/queryUser"
              , "/api/current/createQrCode"
              , "/api/current/createQrCode"
              , "/api/current/verifyQrCode"
              , "/api/current/connRecord"
              , "/api/current/downConf"
              , "/api/vpn/status"
              , "/api/vpn/statusDetail"
      );
    }
    return token.getId();
  }


  private void checkPassword(@Nonnull String pwd) {
    if (matchPassword(pwd)) {
      throw new ValidationFailed("密码必须包含大小写字母和数字的组合，不能使用特殊字符，长度在8-20之间");
    }
  }

}
