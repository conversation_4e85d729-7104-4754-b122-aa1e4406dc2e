package me.meilon.vpnmgr.app.service;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.files.OpenvpnDao;
import me.meilon.vpnmgr.infrastructure.management.OpenvpnManagementClient;
import me.meilon.vpnmgr.infrastructure.management.v3.commands.State;
import me.meilon.vpnmgr.infrastructure.management.v3.commands.Status;
import me.meilon.vpnmgr.infrastructure.management.v3.message.RoutingTable;
import me.meilon.vpnmgr.infrastructure.management.v3.message.StateMessage;
import me.meilon.vpnmgr.infrastructure.management.v3.message.StatusMessage;
import org.springframework.stereotype.Service;
import java.io.IOException;
import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class OpenvpnService {

    private final OpenvpnDao openvpnDao;
    private final OpenvpnManagementClient client;


    public String state(){
        try {
            StateMessage msg = client.sendAndWait(State.of(), StateMessage.class);
          return msg.getState();
        } catch (IOException e) {
            log.error("", e);
            return "UNKNOWN";
        }
    }

    public StateMessage stateDetail(){
        try {
            return client.sendAndWait(State.of(), StateMessage.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public List<RoutingTable> routeTable(){
        try {
            return client.sendAndWait(Status.of(3), StatusMessage.class).getRoutingTable();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 读取 openvpn 配置文件
     * @return 配置文件内容
     */
    public List<String> readServerConf() {
        return openvpnDao.readServerConf();
    }

    /**
     * 推送 token 给客户端
     */
    public void pushToken(String userCode, String token) {
        openvpnDao.pushToken(userCode, token);
    }
}
