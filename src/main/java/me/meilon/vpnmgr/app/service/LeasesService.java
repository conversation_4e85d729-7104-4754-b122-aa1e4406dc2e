package me.meilon.vpnmgr.app.service;

import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import me.meilon.vpnmgr.app.args.ClientConnect;
import me.meilon.vpnmgr.app.dto.LeasesDto;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.ClientLinkStatusDo;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.LeasesDo;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.LeasesRepository;
import me.meilon.vpnmgr.infrastructure.utils.StrUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class LeasesService {

    private final LeasesRepository leasesRepository;

    /**
     * 查询用户租约记录
     * @param userCode 用户编码
     */
    public List<LeasesDto> queryLeasesByUser(@Nonnull String userCode){
        return leasesRepository.findAllByUserCode(userCode).stream()
                .map(LeasesDo::toDto)
                .collect(Collectors.toList());
    }

    /**
     * 重新分配 IP
     */
    public synchronized void reallocate(@Nonnull ClientConnect args, @Nonnull ClientLinkStatusDo statusDo){
        if (StrUtils.notEquals(args.getClientIp(), statusDo.getClientIp())) {
            // 租约表
            LeasesDo leases = this.queryUserCurrent(args.getUserCode());
            if (leases == null){
                // 没有租约记录则创建新的
                leases = LeasesDo.create(args);
            }
            else if (leases.getBindIp().equals(args.getClientIp())){
                // 有记录并且IP一致更新续约时间
                leases.setRenewDate(LocalDateTime.now());
            }
            else {
                // 有记录但IP不一致, 说明分配了新的IP
                // 解绑旧IP, 绑定新IP
                leases.setUnbindDate(LocalDateTime.now());
                LeasesDo newLeases = LeasesDo.create(args);
                this.save(newLeases);
            }
            this.save(leases);
        }
    }


    public List<LeasesDto> queryUsed(){
        return leasesRepository.findAllByUnbindDateIsNull().stream()
                .map(LeasesDo::toDto)
                .collect(Collectors.toList());
    }

    public LeasesDo queryUserCurrent(@Nonnull String userCode){
        return leasesRepository.findAllByUserCodeAndUnbindDateIsNull(userCode).stream()
                .findFirst()
                .orElse(null);
    }

    public void save(@Nonnull LeasesDo args){
        leasesRepository.save(args);
    }


}
