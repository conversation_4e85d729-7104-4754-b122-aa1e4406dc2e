package me.meilon.vpnmgr.app.service;

import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import me.meilon.vpnmgr.app.args.resource.ResourceAdd;
import me.meilon.vpnmgr.app.args.resource.ResourceMod;
import me.meilon.vpnmgr.app.args.resource.ResourcePageQry;
import me.meilon.vpnmgr.app.dto.ResourceDto;
import me.meilon.vpnmgr.infrastructure.biz.resource.jpa.ResourceDo;
import me.meilon.vpnmgr.infrastructure.biz.resource.jpa.ResourceRelationDo;
import me.meilon.vpnmgr.infrastructure.biz.resource.jpa.ResourceRelationRepository;
import me.meilon.vpnmgr.infrastructure.biz.resource.jpa.ResourceRepository;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.UserConfigDo;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.UserConfigRepository;
import me.meilon.vpnmgr.infrastructure.exception.ValidationFailed;
import me.meilon.vpnmgr.infrastructure.utils.PageUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ResourceService {

    private final ResourceRepository resourceRepository;
    private final ResourceRelationRepository resourceRelationRepository;
    private final UserConfigRepository userConfigRepository;
    private final GroupService groupService;

    @Transactional
    public ResourceDto create(ResourceAdd args){

        ResourceDo resourceDo = new ResourceDo();
        resourceDo.setIp(args.getIp());
        resourceDo.setName(args.getName());
        resourceDo.setNetmask(args.getNetmask());
        resourceDo = resourceRepository.save(resourceDo);
        // 添加资源的同时指定了资源组
        // 则同时将该资源加入到资源组中
        String groupId = args.getGroupId();
        if (groupId != null && !groupId.isEmpty()){
            groupService.appendResource(groupId, resourceDo);
        }
        return resourceDo.toDto();
    }

    public void delete(String id){
        if (!resourceRelationRepository.findByResourceId(id).isEmpty()) {
            throw new ValidationFailed("资源已被使用，无法删除");
        }
        List<UserConfigDo> conf = userConfigRepository.findByObjId(id);
        if (!conf.isEmpty()){
            throw new ValidationFailed("资源已被使用，无法删除");
        }
        resourceRepository.findById(id)
                .ifPresent(resourceRepository::delete);
    }

    public void update(ResourceMod args){
        ResourceDo resourceDo = resourceRepository.findById(args.getId())
                .orElseThrow(()->new ValidationFailed("资源不存在"));
        if (args.getName() != null){
            resourceDo.setName(args.getName());
        }
        if (args.getIp() != null){
            resourceDo.setIp(args.getIp());
        }
        if (args.getNetmask() != null){
            resourceDo.setNetmask(args.getNetmask());
        }
        resourceRepository.save(resourceDo);
    }

    public List<ResourceDto> queryList(){
        return resourceRepository.findAll()
        .stream()
        .map(ResourceDo::toDto)
        .collect(Collectors.toList());

    }

    public Page<ResourceDto> queryPage(ResourcePageQry args){
        String groupId = args.getGroupId();
        List<String> ids = null;
        if (groupId != null && !groupId.isEmpty()){
            List<ResourceRelationDo> relationDos = resourceRelationRepository.findAllByGroupId(groupId);
            ids = relationDos.stream()
                    .map(ResourceRelationDo::getResourceId)
                    .collect(Collectors.toList());
        }
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageUtils.of(args, sort);
        return resourceRepository.findAll(query(ids, args.getIp(), args.getName()), pageable)
                .map(ResourceDo::toDto);
    }

    private Specification<ResourceDo> query(List<String> ids, String ip, String name){
        return (root, query, builder)->{
            List<Predicate> predicates = new ArrayList<>();
            if (ids != null){
                predicates.add(root.get("id").in(ids));
            }
            if (ip != null && !ip.isEmpty()) {
                predicates.add(builder.like(root.get("ip"), "%"+ip+"%"));
            }
            if (name != null && !name.isEmpty()) {
                predicates.add(builder.like(root.get("name"), "%"+name+"%"));
            }
            return query.where(predicates.toArray(new Predicate[0])).getRestriction();
        };
    }
}
