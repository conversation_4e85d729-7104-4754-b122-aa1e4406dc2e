package me.meilon.vpnmgr.app.service;

import jakarta.annotation.Nonnull;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import me.meilon.vpnmgr.app.args.resource.GroupAdd;
import me.meilon.vpnmgr.app.args.resource.GroupMod;
import me.meilon.vpnmgr.app.args.resource.GroupPageQry;
import me.meilon.vpnmgr.app.dto.GroupDto;
import me.meilon.vpnmgr.app.dto.ResourceDto;
import me.meilon.vpnmgr.infrastructure.biz.resource.cli.RouteCli;
import me.meilon.vpnmgr.infrastructure.biz.resource.enums.GroupType;
import me.meilon.vpnmgr.infrastructure.biz.resource.event.ResourceGroupEvent;
import me.meilon.vpnmgr.infrastructure.biz.resource.jpa.*;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserDo;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.UserConfigDo;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.UserConfigRepository;
import me.meilon.vpnmgr.infrastructure.exception.ValidationFailed;
import me.meilon.vpnmgr.infrastructure.utils.PageUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class GroupService {

  private final ResourceRepository resourceRepository;
  private final ResourceGroupRepository groupRepository;
  private final ResourceRelationRepository relationRepository;

  private final UserConfigRepository userConfigRepository;
  private final ApplicationEventPublisher applicationEventPublisher;
  private final RouteCli routeCli;
  private final String DEF_NAME = "default";

  public GroupDto create(@Nonnull GroupAdd args) {
    ResourceGroupDo resourceGroupDo = new ResourceGroupDo();
    groupRepository.findByName(args.getName()).ifPresent(g -> {
      throw new ValidationFailed("资源组名称已存在");
    });
    resourceGroupDo.setName(args.getName());
    resourceGroupDo.setGroupType(GroupType.User);
    resourceGroupDo.setOwnerUserId("-1");
    resourceGroupDo.setRemark(args.getRemark());
    return groupRepository.save(resourceGroupDo).toDto();
  }

  public ResourceGroupDo create(@Nonnull SystemUserDo user) {
    String userCode = user.getUserCode();
    groupRepository.findByName(userCode).ifPresent(g -> {
      throw new ValidationFailed("资源组名称被占用");
    });
    ResourceGroupDo resourceGroupDo = new ResourceGroupDo();
    resourceGroupDo.setName(user.getUserName());
    resourceGroupDo.setGroupType(GroupType.System);
    resourceGroupDo.setOwnerUserId(user.getId());
    resourceGroupDo.setRemark("VPN网关[" + userCode + "]默认子网资源组");
    return groupRepository.save(resourceGroupDo);
  }

  public void update(@Nonnull GroupMod args) {
    ResourceGroupDo groupDo = groupRepository.findById(args.getId())
            .orElseThrow(() -> new ValidationFailed("资源组不存在"));

    String oldName = groupDo.getName();
    if (DEF_NAME.equals(oldName)) {
      throw new ValidationFailed("默认资源组禁止修改");
    }
    String name = args.getName();
    if (name != null && !name.equals(oldName)) {
      groupRepository.findByName(name).ifPresent(g -> {
        throw new ValidationFailed("资源组名称已存在");
      });
      groupDo.setName(name);
    }
    if (args.getDesc() != null) {
      groupDo.setRemark(args.getDesc());
    }
    groupRepository.save(groupDo);
  }

  public void delete(@Nonnull String id) {
    ResourceGroupDo groupDo = groupRepository.findById(id)
            .orElseThrow(() -> new ValidationFailed("资源组不存在"));
    checkGroupUsed(id);
    if (DEF_NAME.equals(groupDo.getName())
            || !groupDo.getOwnerUserId().equals("-1")
            || groupDo.getGroupType() == GroupType.System) {
      throw new ValidationFailed("系统内部资源组不允许删除");
    }
    List<ResourceRelationDo> relationDos = relationRepository.findAllByGroupId(id);
    relationRepository.deleteAll(relationDos);
    groupRepository.delete(groupDo);
  }


  public void deleteByUser(@Nonnull SystemUserDo user) {
    for (ResourceGroupDo item : groupRepository.findByOwnerUserId(user.getId())) {
      checkGroupUsed(item.getId());
      groupRepository.delete(item);
    }
  }

  // public void delete(UserConfigEntity entity) {
  //   String groupId = entity.getObjId();
  //   ResourceGroupDo groupDo = groupRepository.findById(groupId)
  //           .orElse(null);
  //   if (groupDo == null || groupDo.getGroupType() == GroupType.User
  //           || DEF_NAME.equals(groupDo.getName())) {
  //     return;
  //   }
  //   checkGroupUsed(groupId);
  //   groupRepository.delete(groupDo);
  // }

  /**
   * 检查资源组是否被使用
   * @param groupId 资源组id
   */
  public void checkGroupUsed(@Nonnull String groupId) {
    List<UserConfigDo> configDos = userConfigRepository.findByObjId(groupId);
    if (!configDos.isEmpty()) {
      throw new ValidationFailed("资源组已被使用");
    }
  }

  /**
   * 查询非默认资源组
   */
  public List<GroupDto> queryListByNotDefault() {
    return groupRepository.findAll()
            .stream()
            .filter(g -> !DEF_NAME.equals(g.getName()))
            .map(ResourceGroupDo::toDto)
            .collect(Collectors.toList());
  }

  /**
   * 查询所有资源组
   */
  public List<GroupDto> queryList() {
    return groupRepository.findAll()
            .stream()
            .map(ResourceGroupDo::toDto)
            .collect(Collectors.toList());
  }

  /**
   * 分页查询资源组
   */
  public Page<GroupDto> queryPage(@Nonnull GroupPageQry args) {
    Pageable pageable = PageUtils.of(args);
    return groupRepository.findAll((root, query, builder) -> {
              List<Predicate> predicates = new ArrayList<>();
              String name = args.getName();
              if (name != null && !name.isEmpty()) {
                predicates.add(builder.like(root.get("name"), "%" + name + "%"));
              }
              return query.where(predicates.toArray(new Predicate[0])).getRestriction();
            }, pageable)
            .map(ResourceGroupDo::toDto);
  }

  /**
   * 添加资源到资源组
   * @param groupId 资源组ID
   * @param resourceId 资源ID
   */
  public void appendResource(@Nonnull String groupId, @Nonnull String resourceId) {
    ResourceDo resourceDo = resourceRepository.findById(resourceId)
            .orElseThrow(() -> new ValidationFailed("资源不存在"));
    appendResource(groupId, resourceDo);
  }

  /**
   * 添加资源到资源组
   * @param groupId 资源组ID
   * @param resource 资源实例
   */
  public void appendResource(@Nonnull String groupId, @Nonnull ResourceDo resource) {
    ResourceGroupDo groupDo = groupRepository.findById(groupId)
            .orElseThrow(() -> new ValidationFailed("资源组不存在"));

    String resourceId = resource.getId();
    if (relationRepository.findByGroupIdAndResourceId(groupId, resourceId).isPresent()) {
      throw new ValidationFailed("资源已关联");
    }
    ResourceRelationDo relationDo = new ResourceRelationDo();
    relationDo.setGroupId(groupDo.getId());
    relationDo.setResourceId(resourceId);
    relationRepository.save(relationDo);
    if (!DEF_NAME.equals(groupDo.getName())) {
      // 不是默认组，需要添加路由
      routeCli.addRoute(resource);
    }
    // 发布事件
    ResourceGroupEvent event = new ResourceGroupEvent(groupDo, ResourceGroupEvent.Type.APPEND);
    applicationEventPublisher.publishEvent(event);
  }

  /**
   * 从资源组移除资源
   */
  public void removeResource(String groupId, String resourceId) {
    ResourceGroupDo groupDo = groupRepository.findById(groupId)
            .orElseThrow(() -> new ValidationFailed("资源组不存在"));
    ResourceRelationDo relationDo = relationRepository.findByGroupIdAndResourceId(groupId, resourceId)
            .orElseThrow(() -> new ValidationFailed("资源组中不存在该资源"));
    relationRepository.delete(relationDo);

    if (!DEF_NAME.equals(groupDo.getName())) {
      // 不是默认组，需要删除路由
      resourceRepository.findById(resourceId)
              .ifPresent(routeCli::delRoute);
    }
    // 发布事件
    ResourceGroupEvent event = new ResourceGroupEvent(groupDo, ResourceGroupEvent.Type.REMOVE);
    applicationEventPublisher.publishEvent(event);

  }

  public List<ResourceDto> queryResourceList(String groupId) {
    groupRepository.findById(groupId)
            .orElseThrow(() -> new ValidationFailed("资源组不存在"));
    List<ResourceRelationDo> relationDos = relationRepository.findAllByGroupId(groupId);
    List<String> resourceIds = relationDos.stream()
            .map(ResourceRelationDo::getResourceId)
            .collect(Collectors.toList());
    return resourceRepository.findAllById(resourceIds)
            .stream()
            .map(ResourceDo::toDto)
            .collect(Collectors.toList());
  }


  public ResourceGroupDo findDefault() {
    return groupRepository.findByName(DEF_NAME)
            .orElse(null);
  }

}
