package me.meilon.vpnmgr.app.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.OperationLogDo;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.OperationLogRepository;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserRepository;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;


@Slf4j
@Service
@RequiredArgsConstructor
public class OperationLogService {

    private final SystemUserRepository userRepository;
    private final OperationLogRepository authRepository;

    public String login(String userCode){
        userRepository.findOneByUserCode(userCode).ifPresent(userDo -> {
            userDo.setLastLoginTime(LocalDateTime.now());
            userRepository.save(userDo);
        });
        return authRepository.findOneByUserCode(userCode).orElseGet(()->{
            OperationLogDo operationLogDo = new OperationLogDo();
            operationLogDo.setUserCode(userCode);
            return authRepository.save(operationLogDo);
        }).getId();
    }


}
