package me.meilon.vpnmgr.app.service;

import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.args.user.UserConfigAdd;
import me.meilon.vpnmgr.app.dto.UserConfigDto;
import me.meilon.vpnmgr.app.dto.UserConfigEntity;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.files.OpenvpnDao;
import me.meilon.vpnmgr.infrastructure.biz.resource.jpa.ResourceRepository;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.ConfigType;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.StatusEnum;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.UserConfigType;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserDo;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserRepository;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.UserConfigDo;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.UserConfigRepository;
import me.meilon.vpnmgr.infrastructure.exception.ValidationFailed;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class UserConfigService {

  private final SystemUserRepository userRepository;
  private final UserConfigRepository userConfigRepository;
  private final ResourceRepository resourceRepository;
  private final GroupService groupService;
  private final OpenvpnDao openvpnDao;

  public void add(UserConfigAdd args) {
    add(args.getUserId(), args.getType(), args.getValue(), args.getObjectId());
  }

  public void add(String userId, UserConfigType type, String value, String objId) {
    UserConfigDo userConfigDo = new UserConfigDo();
    userConfigDo.setUserId(userId);
    userConfigDo.setType(type);
    if (value != null) {
      userConfigDo.setValue(value);
    } else if (objId != null) {
      userConfigDo.setObjId(objId);
    } else {
      throw new ValidationFailed("value 和 objectId 不能同时为空");
    }
    userConfigRepository.saveAndFlush(userConfigDo);
    syncUserConfig(userId);
  }


  public void del(String id) {
    UserConfigDo configDo = userConfigRepository.findById(id)
            .orElseThrow(() -> new ValidationFailed("配置不存在"));
    userConfigRepository.delete(configDo);
    syncUserConfig(configDo.getUserId());
  }

  public void deleteByUser(@Nonnull SystemUserDo user){
    List<UserConfigDo> list = userConfigRepository.findByUserId(user.getId());
    userConfigRepository.deleteAll(list);
    groupService.deleteByUser(user);
    openvpnDao.deleteClientConfig(user.getUserCode());
  }

  /**
   * 同步用户配置
   * 将数据库中的用户配置同步到openvpn配置文件中
   */
  public void syncUserConfig(@Nonnull String userId) {
    userRepository.findById(userId)
            .ifPresent(this::syncUserConfig);
  }

  public void syncUserConfig(SystemUserDo user) {
    if (user.getUserCode().equals("admin")) {
      return;
    }
    synchronized (user.getId()) {
      if (user.getStatus() == StatusEnum.DISABLED) {
        // 用户被禁用，删除配置文件
        openvpnDao.deleteClientConfig(user.getUserCode());
        return;
      }
      // 用户配置列表
      List<UserConfigDo> configDos = userConfigRepository.findByUserId(user.getId());
      List<UserConfigDto> configs = toDto(configDos);
      // 绑定静态IP
      String staticIp = user.getBindIp();
      if (staticIp != null && !staticIp.isEmpty()) {
        String value = staticIp + " *************";
        configs.add(create(ConfigType.ifconfig_push, value));
      }
      if (!configs.isEmpty()) {
        openvpnDao.saveClientConfig(user.getUserCode(), configs);
      }
    }
  }

  public List<String> getUserConnentConfig(SystemUserDo user){

    // 用户配置列表
    List<UserConfigDo> configDos = userConfigRepository.findByUserId(user.getId());
    List<UserConfigDto> configs = toDto(configDos);
    // 绑定静态IP
    String staticIp = user.getBindIp();
    if (staticIp != null && !staticIp.isEmpty()) {
      String value = staticIp + " *************";
      configs.add(create(ConfigType.ifconfig_push, value));
    }

    if (!configs.isEmpty()) {
      return configs.stream()
          .map(c -> {
            ConfigType type = c.getType();
            return String.format(type.getCmd(), c.getValue());
          })
          .collect(Collectors.toList());
    }
    return Collections.emptyList();
  }


  private List<UserConfigDto> toDto(List<UserConfigDo> configDos) {
    List<UserConfigDto> configs = new ArrayList<>();
    for (UserConfigDo conf : configDos) {
      UserConfigType type = conf.getType();
      String objId = conf.getObjId();
      switch (type) {
        case access:
          resourceRepository.findById(objId).ifPresent(resource -> {
            String value = resource.getIp() + " " + resource.getNetmask();
            configs.add(create(ConfigType.push_route, value));
          });
          break;
        case access_group:
          groupService.queryResourceList(objId).forEach(resource -> {
            String value = resource.getIp() + " " + resource.getNetmask();
            configs.add(create(ConfigType.push_route, value));
          });
          break;
        case subnet:
          resourceRepository.findById(objId).ifPresent(resource -> {
            String value = resource.getIp() + " " + resource.getNetmask();
            configs.add(create(ConfigType.iroute, value));
          });
          break;
        case subnet_group:
          groupService.queryResourceList(objId).forEach(resource -> {
            String value = resource.getIp() + " " + resource.getNetmask();
            configs.add(create(ConfigType.iroute, value));
          });
        default:
      }
    }
    return configs;
  }

  private UserConfigDto create(ConfigType type, String value) {
    UserConfigDto dto = new UserConfigDto();
    dto.setType(type);
    dto.setValue(value);
    return dto;
  }

  public List<UserConfigEntity> queryList(@Nonnull String userId) {
    List<UserConfigDo> configDos = userConfigRepository.findByUserId(userId);
    return configDos.stream().map(UserConfigDo::toEntity).collect(Collectors.toList());
  }

  public Set<String> queryUserIdsByGroupId(String id) {
    HashSet<String> userIds = new HashSet<>();
    for (UserConfigDo configDo : userConfigRepository.findByObjId(id)) {
      userIds.add(configDo.getUserId());
    }
    return userIds;
  }
}
