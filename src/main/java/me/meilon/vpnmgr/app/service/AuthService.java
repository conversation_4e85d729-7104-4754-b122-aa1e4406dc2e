package me.meilon.vpnmgr.app.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.dto.AuthResult;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.AuthMode;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.StatusEnum;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.UserType;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserDo;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserRepository;
import me.meilon.vpnmgr.infrastructure.exception.NeedSecondValidation;
import me.meilon.vpnmgr.infrastructure.exception.VerifyFailed;
import me.meilon.vpnmgr.infrastructure.management.v3.commands.Challenge;
import me.meilon.vpnmgr.infrastructure.management.v3.message.ClientEvent;
import me.meilon.vpnmgr.infrastructure.management.v3.message.Response;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 认证服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

  private final TotpService totpService;
  private final UserService userService;
  private final Cache<String, String> cache = Caffeine.newBuilder()
          .expireAfterWrite(2, TimeUnit.MINUTES)
          .build();

  /**
   * 验证用户
   *
   * @param userCode  用户名
   * @param cn        证书
   * @param pwd       密码
   * @param sessionId 会话id
   */
  public AuthResult verify(SystemUserDo userDo, ClientEvent client) {
    String userCode = client.getUsername();
    String cn = client.getCommonName();
    String pwd = client.getPassword();
    String sessionId = client.getSessionId();
    try {
      commonVerify(userDo, userCode, cn);
      log.info("user [{}] name [{}] auth sessionId [{}]", userCode, userDo.getUserName(),sessionId);
      Response response = Response.match(pwd) ? Response.of(pwd) : null;
      if(response == null){
        pwdVerify(userDo, pwd);
      }
      pwdExpiredVerify(userDo, response);
      multiFactorVerify(userDo, response);
      return AuthResult.success();

    } catch (VerifyFailed e) {

      return AuthResult.failed(e.getMessage());
    } catch (NeedSecondValidation e) {

      Challenge challenge = Challenge.of(userCode, e.getMessage());
      cache.put(challenge.getStateId(), e.getType());
      return AuthResult.challenge(challenge);
    }
  }

  /**
   * 通用校验逻辑
   */
  public void commonVerify(SystemUserDo userDo, String userCode, String cn) {
    if (userDo == null) {
      log.error("user [{}] not found", userCode);
      throw new VerifyFailed("用户名或密码错误");
    }
    String userName = userDo.getUserName();
    // 判断用户是否被禁用
    if (userDo.getStatus() == StatusEnum.DISABLED) {
      log.error("user [{}] name [{}] is disabled", userCode, userDo.getUserName());
      throw new VerifyFailed("用户已被冻结");
    }
    // 判断使用的证书是否是已绑定的
    String userCer = userDo.getCertificate();
    if (userCer != null && !cn.equals(userCer)) {
      log.error("User [{}] is inconsistent with certificate [{}] and bind certificate [{}]", userName, cn, userDo.getCertificate());
      throw new VerifyFailed("非法的证书");
    }

  }

  /**
   * 密码校验逻辑
   * @param userDo 用户
   * @param pwd 用户提交的密码
   */
  private void pwdVerify(@Nonnull SystemUserDo userDo, String pwd) {

    // 判断密码是否正确
    if(userDo.comparePassword(pwd)){
      log.info("user [{}] name [{}] verify password success", userDo.getUserCode(), userDo.getUserName());
      return;
    }
    log.error("user [{}] name [{}] verify password fail", userDo.getUserCode(), userDo.getUserName());
    throw new VerifyFailed("用户名或密码错误");
  }

  /**
   * 密码过期校验逻辑
   */
  private void pwdExpiredVerify(@Nonnull SystemUserDo user, Response response) {
    // vpn 网关不需要校验密码过期
    if (user.getUserType() == UserType.Gateway) {
      return;
    }
    final String type = "UP_PWD_EXPIRED:";
    if (response == null) {
      long days = user.getPwdUpdateDays();
      // 超过半年未登录的禁止登录
      if (days > 180) {
        log.error("user [{}] name [{}] the password is locked", user.getUserCode(), user.getUserName());
        throw new VerifyFailed("长期未登录,已被锁定");
      }
      // 超过60天未修改密码或临时密码需要重新设置密码
      if (days > 60 || user.getTempPwd()) {
        log.warn("user [{}] name [{}] the password expires and needs to be reset", user.getUserCode(), user.getUserName());
        throw new NeedSecondValidation(type,"密码过期,请重新设置密码");
      }
    }
    else {
      String t = cache.getIfPresent(response.getStateId());
      // 判断是否是修改密码的二次验证
      if (type.equals(t)) {
        cache.invalidate(response.getStateId());
        updatePassword(user, response.getText());
      }

    }
  }

  /**
   * 多因素认证校验
   * @param user 用户
   * @param response 二次验证响应
   */
  private void multiFactorVerify(SystemUserDo user, Response response) {
    AuthMode mode = user.getAuthMode();
    final String type = "MULTI_FACTORY:";
    if (response == null) {
      if (mode != AuthMode.NONE) {
        throw new NeedSecondValidation(type,"请输入口令");
      }
      return;
    }
    String t = cache.getIfPresent(response.getStateId());
    if (type.equals(t)){
      cache.invalidate(response.getStateId());
      switch (mode) {
        case NONE -> noneVerify(user);
        case OTP -> totpVerify(user, response.getText());
//        case DENY -> throw new VerifyFailed("连接断开");
        default -> throw new VerifyFailed("用户名或密码错误");
      }
    }
  }


  private void noneVerify(SystemUserDo user){
    if (user.getUserType() == UserType.Gateway) {
      return;
    }
    // 非网关用户未开启多因素验证只能使用3天
    long days = user.getPwdUpdateDays();
    if (days > 3){
      throw new VerifyFailed("密码过期,请联系管理员");
    }
  }

  /**
   * 验证动态口令
   */
  private void totpVerify(SystemUserDo user, String pwd) {
    if (isNumeric(pwd)) {
      Integer auth = Integer.valueOf(pwd);
      if (totpService.authorize(user.getUserCode(), auth)) {
        log.info("user [{}] name [{}] verify totp success", user.getUserCode(), user.getUserName());
        return;
      }
    }
    log.error("user [{}] name [{}] verify totp fail", user.getUserCode(), user.getUserName());
    throw new VerifyFailed("口令错误");
  }

  private void updatePassword(SystemUserDo user, String newPwd){
    if (user.comparePassword(newPwd)) {
      // 新密码不能和旧密码相同
      throw new VerifyFailed("新密码不能与旧密码相同");
    }
    if (userService.matchPassword(newPwd)) {
      throw new VerifyFailed("密码强度不够");
    }
    userService.updatePasswd(user, newPwd);
    throw new VerifyFailed("完成,使用新密码重新登录");
  }

  private static boolean isNumeric(String str) {
    for (char c : str.toCharArray()) {
      if (!Character.isDigit(c)) {
        return false;
      }
    }
    return true;
  }

}
