package me.meilon.vpnmgr.app.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.warrenstrange.googleauth.GoogleAuthenticator;
import com.warrenstrange.googleauth.GoogleAuthenticatorKey;
import com.warrenstrange.googleauth.GoogleAuthenticatorQRGenerator;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.AuthMode;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserDo;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserRepository;
import me.meilon.vpnmgr.infrastructure.biz.totp.jpa.TotpDo;
import me.meilon.vpnmgr.infrastructure.biz.totp.jpa.TotpRepository;
import me.meilon.vpnmgr.infrastructure.exception.ValidationFailed;
import me.meilon.vpnmgr.infrastructure.utils.DesUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;


@Service
@RequiredArgsConstructor
public class TotpService {

  private final TotpRepository totpRepository;
  private final SystemUserRepository userRepository;
  private final GoogleAuthenticator authenticator;
  private final Cache<String, String> secretCache = Caffeine.newBuilder()
          .expireAfterWrite(2, TimeUnit.MINUTES)
          .build();

  /**
   * 生成客户端动态码
   */
  public Integer generate(@Nonnull String userCode) {
    TotpDo userDo = totpRepository.findOneByUserCode(userCode)
        .orElse(null);
    if (userDo == null) {
      return null;
    }
    String key = userDo.getSecretKey();
    String skey = DesUtil.decrypt(key);
    if (skey == null) {
      return null;
    }
    return authenticator.getTotpPassword(skey);
  }

  /**
   * 验证动态码
   *
   * @param userCode 用户编码
   * @param otp      动态码
   * @return 验证结果 true: 成功, false: 失败
   */
  public boolean authorize(@Nonnull String userCode, @Nonnull Integer otp) {
    TotpDo userDo = totpRepository.findOneByUserCode(userCode)
            .orElse(null);
    if (userDo == null) {
      return false;
    }
    String key = userDo.getSecretKey();
    String skey = DesUtil.decrypt(key);
    if (skey == null) {
      return false;
    }
    return authenticator.authorize(skey, otp);
  }

  /**
   * 生成 QR 码
   *
   * @param user 用户
   * @return 生成的 QR 码字串, 前端显示为二维码图片
   */
  public String createQrCode(String user) {
    GoogleAuthenticatorKey key = authenticator.createCredentials();
    secretCache.put(user, key.getKey());
    return GoogleAuthenticatorQRGenerator.getOtpAuthTotpURL("ideal.nj.vpn", user, key);
  }

  /**
   * QR 码验证, 验证完成后 QR 码才正式生效
   *
   * @param user 用户
   * @param otp  动态码
   * @return 验证结果 true: 成功, false: 失败
   */
  @Transactional(rollbackFor = Throwable.class)
  public boolean verifyQrCode(@Nonnull String user, @Nonnull Integer otp) {
    if (user.isEmpty()) {
      return false;
    }
    String key = secretCache.getIfPresent(user);
    if (key == null) {
      return false;
    }
    SystemUserDo userDo = userRepository.findOneByUserCode(user)
            .orElseThrow(() -> new ValidationFailed("用户不存在"));
    if (authenticator.authorize(key, otp)) {
      TotpDo totpDo = totpRepository.findOneByUserCode(user).orElse(null);
      if (totpDo == null) {
        totpDo = new TotpDo();
        totpDo.setUserCode(user);
      }
      String skey = DesUtil.encrypt(key);
      if (skey == null) {
        return false;
      }
      totpDo.setSecretKey(skey);
      totpRepository.saveAndFlush(totpDo);
      // 开通成功后要修改认证方式为动态码认证
      userDo.setAuthMode(AuthMode.OTP);
      userRepository.saveAndFlush(userDo);
      return true;
    }
    return false;
  }

  /**
   * 关闭动态码认证
   *
   * @param userCode 用户编码
   * @param otp      动态码
   */
  @Transactional(rollbackFor = Throwable.class)
  public void close(@Nonnull String userCode, @Nonnull Integer otp) {
    SystemUserDo user = userRepository.findOneByUserCode(userCode)
            .orElseThrow(() -> new ValidationFailed("用户不存在"));
    if (user.getAuthMode() != AuthMode.OTP) {
      return;
    }
    if (authorize(userCode, otp)) {
      throw new ValidationFailed("验证码错误");
    }
    totpRepository.findOneByUserCode(userCode)
            .ifPresent(totpRepository::delete);
    user.setAuthMode(AuthMode.NONE);
    userRepository.save(user);
  }
}
