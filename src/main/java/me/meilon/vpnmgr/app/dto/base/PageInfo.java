package me.meilon.vpnmgr.app.dto.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.annotation.Nonnull;

import java.util.List;


public class PageInfo<T> {

    private final List<T> content;
    private final int number;
    private final int size;
    @JsonIgnore
    private final long total;
    @JsonIgnore
    private final int totalPages;
    @JsonIgnore
    private final boolean isFirst;
    @JsonIgnore
    private final boolean isLast;
    @JsonIgnore
    private final boolean isEmpty;

    public PageInfo(@Nonnull List<T> content, int number, int size,
                    long total, int totalPages,
                    boolean isFirst, boolean isLast, boolean isEmpty) {
        this.content = content;
        this.number = number;
        this.size = size;
        this.total = total;
        this.totalPages = totalPages;
        this.isFirst = isFirst;
        this.isLast = isLast;
        this.isEmpty = isEmpty;
    }

    public int getNumber() {
        return number;
    }

    public int getSize() {
        return size;
    }

    public int getNumberOfElements() {
        return content.size();
    }

    public int getTotalPages() {
        return totalPages;
    }

    public long getTotalElements() {
        return total;
    }

    public boolean isFirst() {
        return isFirst;
    }

    public boolean isLast() {
        return isLast;
    }

    public boolean isEmpty() {
        return isEmpty;
    }

    public List<T> getContent() {
        return content;
    }


}
