package me.meilon.vpnmgr.app.dto.client;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.enums.ConnState;

import java.time.LocalDateTime;

@Setter
@Getter
public class ConnStatus {

    private String id;
    /**
     * 状态
     * CONNECT(链接)
     * DISCONNECT(断开链接)
     */
    @Nonnull
    private ConnState status;
    /**
     * 用户编码
     */
    @Nonnull
    private String userCode;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 设备类型
     */
    private String deviceType;
    /**
     * 出访地址
     */
    private String trustedAddress;
    /**
     * 客户端虚拟IP
     */
    private String clientIp;
    /**
     * 链接的服务端IP
     */
    private String serverIp;
    /**
     * 最后一次链接时间
     */
    private LocalDateTime connectTime;
    /**
     * 断开时间
     */
    private LocalDateTime disconnectTime;


}
