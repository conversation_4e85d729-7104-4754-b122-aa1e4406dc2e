package me.meilon.vpnmgr.app.dto.client;


import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.enums.ConnRecordType;
import org.jetbrains.annotations.NotNull;

import java.time.LocalDateTime;

@Setter
@Getter
public class ConnRecord {

    private String sessionId;
    /**
     * 记录类型
     */
    private ConnRecordType type;
    /**
     * 用户编码
     */
    @NotNull
    private String userCode;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 设备类型
     */
    private String deviceType;
    /**
     * 客户端本地地址
     */
    private String trustedAddress;
    /**
     * 客户端虚拟IP
     */
    private String clientIp;
    /**
     * 链接的服务端IP
     */
    private String serverIp;
    /**
     * 链接时长
     */
    private String duration;
    /**
     * 链接时间
     */
    private LocalDateTime connectTime;
    /**
     * 断开时间
     */
    private LocalDateTime disconnectTime;


}
