package me.meilon.vpnmgr.app.dto;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.AuthMode;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.UserType;

import java.time.LocalDateTime;


@Getter
@Setter
public class UserDto {

    /**
     * vpn用户唯一标识
     */
    @Nonnull
    private String id;
    /**
     * 用户编码
     */
    @Nonnull
    private String userCode;
    /**
     * 用户姓名
     */
    @Nonnull
    private String userName;
    /**
     * 用户类型
     */
    @Nonnull
    private UserType type;
    /**
     * 创建时间
     */
    @Nonnull
    private LocalDateTime createTime;
    /**
     * 最后一次更新密码的时间
     */
    private LocalDateTime lastUpdatePwdTime;
    /**
     * 最后一次登录时间
     */
    private LocalDateTime lastLoginTime;
    /**
     * 邮箱
     */
    private String mail;
    /**
     * 手机号
     */
    private String mobilePhone;
    /**
     * 绑定证书名
     */
    private String cn;
    /**
     * 状态 0: 失效 1: 生效
     */
    @Nonnull
    private Integer statusCd;
    /**
     * 认证方式(NONE: 不认证, PWD: 密码认证, OTP: 动态码认证)
     */
    @Nonnull
    private AuthMode authMode;
    /**
     * 关联证书名
     */
    private String clientName;
    /**
     * 绑定IP
     */
    private String bindIp;

}
