package me.meilon.vpnmgr.app.dto.base;



import java.util.List;


public class Result<T> {

    /**
     * 返回编码
     * 0: 正常
     * 非0: 异常
     */

    private int code;

    /**
     * 返回消息
     */
    private String msg;

    /**
     * 返回结果
     */
    private T body;

    public Result(int code, String msg, T body) {
        this.code = code;
        this.msg = msg;
        this.body = body;
    }

    public Result(int code, T body) {
        this.code = code;
        this.body = body;
        this.msg = "ok";
    }

    public static <T> Result<T> ok(){
        return new Result<>(0, "ok", null);
    }

    public static <T> Result<T> ok(String msg){
        return new Result<>(0, msg, null);
    }

    public static <T> Result<T> withData(T result){
        return new Result<>(0, result);
    }

    public static  <T> Result<T> err(int code, String msg){
        return new Result<>(code, msg, null);
    }

    public static  <T> Result<T> err(String msg){
        return new Result<>(-1, msg, null);
    }

    public static  <T> Result<T> err(List<String> msg){
        StringBuilder m = new StringBuilder();
        for (String s : msg) {
            m.append(s).append("\n");
        }
        return new Result<>(-1, m.toString(), null);
    }


    public int getCode() {
        return code;
    }

    public Result<T> setCode(int code) {
        this.code = code;
        return this;
    }

    public String getMsg() {
        return msg;
    }

    public Result<T> setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public T getBody() {
        return body;
    }

    public Result<T> setBody(T body) {
        this.body = body;
        return this;
    }
}
