package me.meilon.vpnmgr.app.dto.base;

import org.springframework.data.domain.Page;


public class PageResult<T> extends Result<Page<T>> {

    public PageResult(int code, String msg, Page<T> body) {
        super(code, msg, body);
    }

    public PageResult(int code, Page<T> body) {
        super(code, body);
    }

    public static <R> PageResult<R> ofPath(Page<R> page){
        // PageInfo<R> pageInfo = new PageInfo<>(page.getContent(),page.getNumber(),page.getSize(),
        //         page.getTotalElements(),page.getTotalPages(),
        //         page.isFirst(), page.isLast(), page.isEmpty());
        //

        return new PageResult<>(0, page);
    }
}
