package me.meilon.vpnmgr.app.dto;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 证书
 */
@Getter
@Setter
public class CertificateDto {

    /**
     * ID
     */
    private String id;
    /**
     * 证书名
     */
    @Nonnull
    private String clientName;
    /**
     * 证书密码
     * (可选)
     */
    private String password;
    /**
     * 证书文件名
     */
    @Nonnull
    private String fileName;
    /**
     * 证书状态
     * R: 注销
     * V: 有效
     * U: 未知
     */
    @Nonnull
    private String statusCd;
    /**
     * 状态中文名
     */
    private String status;
    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 证书创建时间
     */
    private LocalDateTime createTime;
    /**
     * 有效期
     */
    private Integer expiration;
    /**
     * 备注
     */
    private String remark;

}
