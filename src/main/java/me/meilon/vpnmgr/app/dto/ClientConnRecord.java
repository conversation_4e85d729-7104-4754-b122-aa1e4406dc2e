package me.meilon.vpnmgr.app.dto;

import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.app.args.ClientDisconnect;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.enums.ConnRecordType;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.enums.ConnState;

import java.time.LocalDateTime;

@Getter
@Setter
public class ClientConnRecord extends ClientDisconnect {

    /**
     * 记录时间
     */
    private LocalDateTime recordTime;

    /**
     * 记录类型
     */
    private ConnRecordType type;
}
