package me.meilon.vpnmgr.app.dto;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 用户租约表
 */
@Getter
@Setter
public class LeasesDto {

    @Nonnull
    private String id;
    /**
     * 用户编码
     */
    @Nonnull
    private String userCode;
    /**
     * 绑定的IP
     */
    @Nonnull
    private String ip;
    /**
     * 绑定时间
     * 首次将IP分配给用户的时间
     */
    @Nonnull
    private LocalDateTime bindDate;
    /**
     * 释放日期
     */
    private LocalDateTime releaseDate;
    /**
     * 续约时间
     */
    private LocalDateTime renewDate;
    /**
     * 解绑时间
     */
    private LocalDateTime unbindDate;



}
