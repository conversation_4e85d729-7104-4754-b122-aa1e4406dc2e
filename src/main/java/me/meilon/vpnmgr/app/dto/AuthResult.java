package me.meilon.vpnmgr.app.dto;

import lombok.Getter;
import me.meilon.vpnmgr.infrastructure.management.v3.commands.Challenge;
import org.jetbrains.annotations.NotNull;

@Getter
public class AuthResult {

    @NotNull
    private final AuthState state;
    private final String message;

    private AuthResult(@NotNull AuthState state, String message){
        this.state = state;
        this.message = message;
    }

    public static AuthResult success(){
        return new AuthResult(AuthState.AUTH_SUCCESS, null);
    }

    public static AuthResult challenge(@NotNull Challenge challenge){
        return new AuthResult(AuthState.AUTH_CHALLENGE, challenge.toString());
    }

    public static AuthResult failed(@NotNull String msg){
        return new AuthResult(AuthState.AUTH_FAILED, msg);
    }

}
