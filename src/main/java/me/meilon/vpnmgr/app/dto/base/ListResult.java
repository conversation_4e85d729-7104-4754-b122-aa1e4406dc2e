package me.meilon.vpnmgr.app.dto.base;


import java.util.List;

public class ListResult<T> extends Result<List<T>> {


    public ListResult(int code, String msg, List<T> body) {
        super(code, msg, body);
    }

    public ListResult(int code, List<T> body) {
        super(code, body);
    }

    public static <R> ListResult<R> ofList(List<R> page){
        return new ListResult<>(0, page);
    }

    public static <R> ListResult<R> ofList(int code, List<R> page){
        return new ListResult<>(code, page);
    }

}
