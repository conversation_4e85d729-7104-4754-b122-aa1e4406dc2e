package me.meilon.vpnmgr.infrastructure.web;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserDo;
import me.meilon.vpnmgr.infrastructure.utils.NanoIdGenerator;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
public class Token {

    @Nonnull
    private String id;
    @Nonnull
    private SystemUserDo user;
    private Set<String> roles;
    private Set<String> permissions;
    private static final String ALL = "all";

    public static Token create(){
        Token token = new Token();
        token.id = NanoIdGenerator.generate();
        token.permissions = new HashSet<>();
        return token;
    }

    @SuppressWarnings("all")
    public Token addPermissions(@Nonnull String... pers){
        Collections.addAll(this.permissions, pers);
        return this;
    }

    public Token addPermission(@Nonnull String per){
        this.permissions.add(per);
        return this;
    }

    public void addPermissionAll(){
        this.permissions.add(ALL);
    }

    public boolean checkPermission(String per){
        return this.permissions.contains(ALL) || this.permissions.contains(per);
    }
}
