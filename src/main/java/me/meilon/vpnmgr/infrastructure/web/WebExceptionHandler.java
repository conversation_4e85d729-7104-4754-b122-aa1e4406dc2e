package me.meilon.vpnmgr.infrastructure.web;


import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.dto.base.Result;
import me.meilon.vpnmgr.infrastructure.exception.BaseException;
import me.meilon.vpnmgr.infrastructure.exception.ExceptionUtil;
import me.meilon.vpnmgr.infrastructure.exception.ShellExecException;
import me.meilon.vpnmgr.infrastructure.exception.ValidationFailed;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.sql.SQLException;
import java.util.List;


/**
 * controller 层统一异常处理
 *
 * <AUTHOR>
 */
@Slf4j
@ControllerAdvice(basePackages = "me.meilon.vpnmgr.facade.http")
public class WebExceptionHandler {

    /**
     * 请求方法不支持异常
     *
     * @param request   http请求对象
     * @param exception 异常对象
     * @return 转换成统一响应对象
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public Result<Void> methodArgumentNotValidExceptionHandler(HttpServletRequest request,
                                                               MethodArgumentNotValidException exception) {
        BindingResult bindingResult = exception.getBindingResult();
        String msg = getBindingResultErrorMessage(bindingResult);
        printRequestException(request, exception);
        return Result.err(msg);
    }

    /**
     * 绑定异常
     *
     * @param request   http请求对象
     * @param exception 异常对象
     * @return 转换成统一响应对象
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(value = BindException.class)
    public Result<Void> bindExceptionHandler(HttpServletRequest request,
                                                 BindException exception) {
        String msg = getBindingResultErrorMessage(exception);
        printRequestException(request, exception);
        return Result.err(msg);
    }

    /**
     * http请求方法错误
     *
     * @param request   http请求对象
     * @param exception 异常对象
     * @return 转换成统一响应对象
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public Result<Void> httpExceptionHandler(HttpServletRequest request,
                                                 HttpRequestMethodNotSupportedException exception) {
        printRequestException(request, exception);
        return Result.err(exception.getMessage());
    }

    /**
     * 数据库异常
     *
     * @param request   http请求对象
     * @param exception 异常对象
     * @return 转换成统一响应对象
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(value = SQLException.class)
    public Result<Void> sqlExceptionHandler(HttpServletRequest request,
                                                SQLException exception) {
        printRequestException(request, exception);
        return Result.err("调用数据服务出错");
    }

    /**
     * 自定义的业务校验异常
     * 不打印异常栈
     * @param request   http请求对象
     * @param exception 异常对象
     * @return 转换成统一响应对象
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(value = ValidationFailed.class)
    public Result<Void> siExceptionHandler(HttpServletRequest request,
                                                 ValidationFailed exception) {
        printRequestWarn(request, exception);
        return Result.err(exception.getErrorCode(), exception.getMessage());
    }


    /**
     * 自定义的基础异常
     *
     * @param request   http请求对象
     * @param exception 异常对象
     * @return 转换成统一响应对象
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(value = BaseException.class)
    public Result<Void> baseExceptionHandler(HttpServletRequest request,
                                                 BaseException exception) {

        printRequestException(request, exception);
        return Result.err(exception.getErrorCode(), exception.getMessage());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(value = ShellExecException.class)
    public Result<Void> baseExceptionHandler(HttpServletRequest request,
                                             ShellExecException exception) {

        printRequestException(request, exception);
        return Result.err(exception.getCode(), exception.getMessage());
    }

    /**
     * 基础异常
     *
     * @param request   http请求对象
     * @param exception 异常对象
     * @return 转换成统一响应对象
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(value = Exception.class)
    public Result<Void> exceptionHandler(HttpServletRequest request,
                                             Exception exception) {
        printRequestException(request, exception);
        return Result.err(exception.getMessage());
    }


    /**
     * 打印异常请求信息
     *
     * @param request   http请求对象
     * @param exception 异常对象
     */
    private void printRequestException(HttpServletRequest request,
                                       Exception exception) {
        String method = request.getMethod();
        String reqUrl = request.getRequestURI();
        log.error("{} 请求 {} {}", method, reqUrl, ExceptionUtil.getStackMsg(exception));
    }


    private void printRequestWarn(HttpServletRequest request,
                                       Exception exception) {
        String method = request.getMethod();
        String reqUrl = request.getRequestURI();
        log.error("{} 请求 {} {}", method, reqUrl, exception.getMessage());
    }


    /**
     * 前端传来参数校验,获取错误信息
     *
     */
    private String getBindingResultErrorMessage(BindingResult bindingResult) {

        StringBuilder sb = new StringBuilder();
        if (bindingResult.hasErrors()) {
            List<ObjectError> ls = bindingResult.getAllErrors();
            for (ObjectError objectError : ls) {
                if (objectError instanceof FieldError fieldError) {
                    sb.append(fieldError.getDefaultMessage());
                } else {
                    sb.append(objectError.getDefaultMessage());
                }
                sb.append(" ");
            }
        }
        return sb.toString();
    }

}
