package me.meilon.vpnmgr.infrastructure.web;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/28 11:05
 */
@Profile("!local")
@Configuration
@RequiredArgsConstructor
public class WebAppConfigurer implements WebMvcConfigurer {

    private final AuthInterceptor authInterceptor;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authInterceptor)
                .excludePathPatterns(
                        "/favicon.ico"
                        ,"/api/system/login")
                .addPathPatterns("/inner/**")
                .addPathPatterns("/api/**");
                // .addPathPatterns("/**");

    }

}
