// package me.meilon.vpnmgr.infrastructure.web;
//
// import org.springframework.context.annotation.Configuration;
//
// import jakarta.servlet.*;
// import jakarta.servlet.annotation.WebFilter;
// import jakarta.servlet.http.HttpServletResponse;
// import java.io.IOException;
//
//
// @WebFilter(filterName = "CorsFilter ")
// @Configuration
// public class CorsFilter  implements Filter {
//
//     @Override
//     public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {
//         HttpServletResponse response = (HttpServletResponse) res;
//         response.setHeader("Access-Control-Allow-Origin","*");
//         response.setHeader("Access-Control-Allow-Credentials", "true");
//         response.setHeader("Access-Control-Allow-Methods", "POST, GET, PATCH, DELETE, PUT");
//         response.setHeader("Access-Control-Max-Age", "1800");
//         response.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
//         chain.doFilter(req, res);
//     }
// }
