package me.meilon.vpnmgr.infrastructure.web;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.NonNull;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserDo;
import me.meilon.vpnmgr.infrastructure.exception.ValidationFailed;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Component
public class TokenCache {

    private final Cache<String,Token> tokenCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(30))
            .expireAfterAccess(Duration.ofMinutes(10))
            .build();


    public Token create(@Nonnull SystemUserDo user){
        Token token = Token.create();
        token.setUser(user);
        tokenCache.put(token.getId(), token);
        return token;
    }

    @NonNull
    public Token getToken(@Nullable String tokenId){
        if (tokenId == null){
            throw new ValidationFailed(401, "没有登录");
        }
        Token token = tokenCache.getIfPresent(tokenId);
        if (token == null){
            throw new ValidationFailed(401, "没有登录");
        }
        return token;
    }


}
