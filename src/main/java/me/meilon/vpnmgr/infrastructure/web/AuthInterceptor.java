package me.meilon.vpnmgr.infrastructure.web;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.dto.base.Result;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserDo;
import me.meilon.vpnmgr.infrastructure.exception.ValidationFailed;
import me.meilon.vpnmgr.infrastructure.utils.json.JsonUtil;
import me.meilon.vpnmgr.infrastructure.web.utils.ServletUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 *
 * 操作日志拦截器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthInterceptor implements HandlerInterceptor {

    // private final OperationLogRepository operationLogRepository;
    private final TokenCache tokenCache;
    public static final String AUTH_HEADER = "X-Auth-Metadata";
    private final List<String> ips = Arrays.asList("[0:0:0:0:0:0:0:1]","127.0.0.1");

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request,
                             @NonNull HttpServletResponse response,
                             @NonNull Object handler) throws IOException {

        String path = request.getServletPath();
        String method = request.getMethod();
        String tokenId = request.getHeader(AUTH_HEADER);
        String ip = ServletUtils.getOriginalIp(request);

        if (path.startsWith("/inner")) {
            // 本地调用内部地址直接放行
            boolean res = ips.contains(ip);
            String exit = res ? "allow" : "deny";
            log.info("remote ip {} request [{}:{}] {}", ip, path, method, exit);
            return res;
        }
        try {
            // 检查 token
            Token token = tokenCache.getToken(tokenId);
            if (!token.checkPermission(path)) {
                return res(response, Result.err(403, "没有权限"));
            }
            SystemUserDo userDo = token.getUser();
            log.info("user {} request [{}:{}]", userDo.getUserCode(), path, method);
            return true;
        }catch (ValidationFailed e){
            log.warn("request [{}:{}] reject {}", path, method, e.getMessage());
            return res(response, Result.err(e.getErrorCode(), e.getMessage()));
        }
    }


    private boolean res(HttpServletResponse response, @NonNull Result<?> result) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(JsonUtil.toJsonString(result));
        return false;
    }

    @Override
    public void postHandle(@NonNull HttpServletRequest request,
                           @NonNull HttpServletResponse response,
                           @NonNull Object handler,
                           @Nullable ModelAndView modelAndView) {
        // String userAgent = request.getHeader("user-agent");
        // OperationLogDo operationLog = new OperationLogDo();
        // operationLog.setUserCode(userDo.getUserCode());
        // operationLog.setUserName(userDo.getUserName());
        // operationLog.setPath(path);
        // operationLog.setUserAgent(userAgent);
        // operationLogRepository.save(operationLog);
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request,
                                @NonNull HttpServletResponse response,
                                @NonNull Object handler, Exception ex) {

    }


    // private String getBody(String method, HttpServletRequest request){
    //     if ("GET".equalsIgnoreCase(method)){
    //         String params = request.getQueryString();
    //         if (params != null){
    //             JSONObject json = new JSONObject();
    //             for (String part : params.split("&")) {
    //                 String[] keyVal = part.split("=");
    //                 json.put(keyVal[0], keyVal[1].trim());
    //             }
    //             params = json.toJSONString();
    //         }
    //         return params;
    //     }
    //     if (request instanceof ContentCachingRequestWrapper){
    //         ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) request;
    //         byte[] br = wrapper.getContentAsByteArray();
    //         return new String(br);
    //     }
    //     String contentType = request.getContentType();
    //     if (contentType != null && contentType.contains(TraceConstants.FORM_CONTENT_TYPE)) {
    //         Map<String, String[]> params = request.getParameterMap();
    //         return JSONObject.toJSONString(params);
    //     }
    //     return null;
    // }


}
