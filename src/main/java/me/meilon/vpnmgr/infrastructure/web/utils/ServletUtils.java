package me.meilon.vpnmgr.infrastructure.web.utils;


import jakarta.annotation.Nonnull;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.utils.StrUtils;

import java.util.*;

@Slf4j
public class ServletUtils {
  private static final String UNKNOWN = "UNKNOWN";

  /**
   * 获取原始的请求ip地址
   *
   * @param request 请求信息
   * @return 原始ip
   * <AUTHOR> on 2021/3/8
   */
  public static String getOriginalIp(@Nonnull HttpServletRequest request) {
    String ip = request.getHeader("X-Real-IP");
    if (StrUtils.isNotBlank(ip) && StrUtils.notEqualsIgnoreCase(ip, UNKNOWN)) {
      return ip;
    }
    ip = request.getHeader("X-Forwarded-For");
    if (StrUtils.isNotBlank(ip) && StrUtils.notEqualsIgnoreCase(ip, UNKNOWN)) {
      int index = ip.indexOf(',');
      if (index > -1) {
        return ip.substring(0, index);
      } else {
        return ip;
      }
    } else {
      return request.getRemoteAddr();
    }
  }

  /**
   * 获取请求头
   *
   * @param request 请求信息
   * @return 请求头
   * <AUTHOR> on 2021/9/17
   */
  @Nonnull
  public static Map<String, List<String>> getHeaders(@Nonnull HttpServletRequest request) {
    Map<String, List<String>> headers = new LinkedHashMap<>();
    Enumeration<String> names = request.getHeaderNames();
    while (names.hasMoreElements()) {
      String name = names.nextElement();
      Enumeration<String> values = request.getHeaders(name);
      List<String> list = headers.computeIfAbsent(name, k -> new ArrayList<>());
      while (values.hasMoreElements()) {
        list.add(values.nextElement());
      }
    }
    return headers;
  }


  private ServletUtils() {
  }
}
