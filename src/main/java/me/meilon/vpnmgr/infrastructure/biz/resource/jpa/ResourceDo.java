package me.meilon.vpnmgr.infrastructure.biz.resource.jpa;

import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.app.dto.ResourceDto;
import me.meilon.vpnmgr.infrastructure.db.jpa.NanoJpaIDGenerator;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 资源表
 * 可访问的网络地址
 */
@Getter
@Setter
@Entity
@Table(name = ResourceDo.ENTITY_NAME,
        indexes = {
                @Index(name = ResourceDo.ENTITY_NAME+"_ip_idx", columnList = "ip"),
        })
@Comment("资源表")
@EntityListeners(AuditingEntityListener.class)
public class ResourceDo {
    public static final String ENTITY_NAME = "resource";

    @Id
    @GeneratedValue(generator = ENTITY_NAME)
    @GenericGenerator(name = ENTITY_NAME, type = NanoJpaIDGenerator.class)
    @Column(name = "id", nullable = false, length = 21)
    private String id;
    /**
     * 资源名称
     */
    @Nonnull
    @Column(name = "name", nullable = false, length = 32)
    private String name;
    /**
     * 资源地址
     */
    @Nonnull
    @Column(name = "ip", nullable = false, length = 16)
    private String ip;
    /**
     * 子网掩码
     * ***************
     */
    @Nonnull
    @Column(name = "netmask", nullable = false, length = 16)
    private String netmask = "***************";
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "create_time")
    private LocalDateTime createTime;

    public ResourceDto toDto(){
        ResourceDto dto = new ResourceDto();
        dto.setId(this.getId());
        dto.setIp(this.getIp());
        dto.setName(this.getName());
        dto.setNetmask(this.getNetmask());
        return dto;
    }
}
