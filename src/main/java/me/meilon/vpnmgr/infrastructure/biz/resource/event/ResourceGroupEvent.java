package me.meilon.vpnmgr.infrastructure.biz.resource.event;

import me.meilon.vpnmgr.infrastructure.biz.resource.jpa.ResourceGroupDo;
import org.springframework.context.ApplicationEvent;


public class ResourceGroupEvent extends ApplicationEvent {

    private final Type type;


    public ResourceGroupEvent(ResourceGroupDo source, Type type) {
        super(source);
        this.type = type;
    }

    public ResourceGroupDo getGroup(){
        return (ResourceGroupDo) super.getSource();
    }

    public Type getType(){
        return type;
    }

    public enum Type {
        APPEND,
        REMOVE
    }


}
