package me.meilon.vpnmgr.infrastructure.biz.resource.jpa;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;


public interface ResourceRelationRepository extends JpaRepository<ResourceRelationDo, String>,
        JpaSpecificationExecutor<ResourceRelationDo> {

    Optional<ResourceRelationDo> findByGroupIdAndResourceId(String id, String id1);

    List<ResourceRelationDo> findAllByGroupId(String groupId);

    List<ResourceRelationDo> findByResourceId(String resourceId);
}
