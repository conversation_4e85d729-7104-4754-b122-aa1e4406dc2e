package me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa;

import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.app.dto.UserConfigEntity;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.UserConfigType;
import me.meilon.vpnmgr.infrastructure.db.jpa.NanoJpaIDGenerator;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * 用户配置表
 */
@Getter
@Setter
@Entity
@Table(name = UserConfigDo.ENTITY_NAME,
        indexes = {
                @Index(name = UserConfigDo.ENTITY_NAME+"_user_id_idx", columnList = "user_id"),
                @Index(name = UserConfigDo.ENTITY_NAME+"_obj_id_idx", columnList = "obj_id"),
        })
@Comment("用户配置表")
@EntityListeners(AuditingEntityListener.class)
public class UserConfigDo {
    public static final String ENTITY_NAME = "user_config";

    @Id
    @GeneratedValue(generator = ENTITY_NAME)
    @GenericGenerator(name = ENTITY_NAME, type = NanoJpaIDGenerator.class)
    @Column(name = "id", nullable = false, length = 21)
    private String id;
    /**
     * 用户Id
     */
    @Nonnull
    @Column(name = "user_id", length = 21)
    private String userId;
    /**
     * 配置类型
     */
    @Nonnull
    @Column(name = "type", length = 15)
    @Enumerated(EnumType.STRING)
    private UserConfigType type;
    /**
     * 配置值
     * object_id 和 value 至少有一个不为空
     */
    @Column(name = "value", length = 32)
    private String value;
    /**
     * 关联对象Id
     * obj_id 和 value 至少有一个不为空
     */
    @Column(name = "obj_id", length = 21)
    private String objId;

    public UserConfigEntity toEntity(){
        UserConfigEntity entity = new UserConfigEntity();
        entity.setId(id);
        entity.setType(type);
        entity.setValue(value);
        entity.setObjId(objId);
        return entity;
    }

}
