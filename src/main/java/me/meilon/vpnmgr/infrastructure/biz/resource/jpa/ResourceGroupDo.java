package me.meilon.vpnmgr.infrastructure.biz.resource.jpa;

import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.app.dto.GroupDto;
import me.meilon.vpnmgr.infrastructure.biz.resource.enums.GroupType;
import me.meilon.vpnmgr.infrastructure.db.jpa.NanoJpaIDGenerator;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * 资源组
 * 将一组资源地址组合成一个资源组
 */
@Getter
@Setter
@Entity
@Table(name = ResourceGroupDo.ENTITY_NAME,
        indexes = {
                @Index(name = ResourceGroupDo.ENTITY_NAME+"_name_idx", columnList = "name"),
                @Index(name = ResourceGroupDo.ENTITY_NAME+"_owner_user_id_idx", columnList = "owner_user_id")
        })
@Comment("资源组")
@EntityListeners(AuditingEntityListener.class)
public class ResourceGroupDo {
    public static final String ENTITY_NAME = "resource_group";

    @Id
    @GeneratedValue(generator = ENTITY_NAME)
    @GenericGenerator(name = ENTITY_NAME, type = NanoJpaIDGenerator.class)
    @Column(name = "id", nullable = false, length = 21)
    private String id;
    /**
     * 资源组类型
     */
    @Nonnull
    @Column(name = "group_type", updatable = false, length = 10)
    @Enumerated(EnumType.STRING)
    private GroupType groupType;
    /**
     * 资源组所属用户
     */
    @Nonnull
    @Column(name = "owner_user_id", nullable = false, length = 21)
    private String ownerUserId;
    /**
     * 资源组名称
     */
    @Nonnull
    @Column(name = "name", nullable = false, length = 32)
    private String name;
    /**
     * 资源组描述
     */
    @Column(name = "remark", length = 500)
    private String remark;

    public GroupDto toDto() {
        GroupDto dto = new GroupDto();
        dto.setId(this.id);
        dto.setName(this.name);
        dto.setDesc(this.remark);
        return dto;
    }
}
