package me.meilon.vpnmgr.infrastructure.biz.openvpn.files;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import me.meilon.vpnmgr.app.dto.UserConfigDto;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.ConfigType;
import me.meilon.vpnmgr.infrastructure.conf.OpenvpnProperty;
import me.meilon.vpnmgr.infrastructure.db.files.LocalFileBaseDao;
import me.meilon.vpnmgr.infrastructure.utils.FileUtil;
import me.meilon.vpnmgr.infrastructure.utils.StrUtils;
import org.springframework.stereotype.Component;

import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class OpenvpnDao implements LocalFileBaseDao {

    private final OpenvpnProperty property;


    public List<String> readServerConf() {
        String path = property.getServerConfPath();
        return readByPath(Path.of(path));
    }

    public IpPoolVo readIppByIp(@Nonnull String ip) {
        List<IpPoolVo> ipPoolVos = readIpp(ippVo -> ippVo.getIp().equals(ip));
        if (ipPoolVos == null || ipPoolVos.isEmpty()) {
            return null;
        }
        return ipPoolVos.get(0);
    }

    public List<IpPoolVo> readIppAll(@Nullable String userCode) {
        if (StrUtils.isBlank(userCode)) {
            return readIpp(null);
        } else {
            return readIpp(ippVo -> ippVo.getUserCode().equals(userCode));
        }
    }

    public List<IpPoolVo> readIpp(Predicate<IpPoolVo> filter) {
        String path = FileUtil.unite(property.getServerDir(), "ipp.txt");
        return readByPath(Path.of(path)).stream()
                .map(line -> {
                    if (StrUtils.isBlank(line)) {
                        return null;
                    }
                    String[] lines = line.split(",");
                    IpPoolVo ipPoolVo = new IpPoolVo();
                    ipPoolVo.setUserCode(lines[0]);
                    ipPoolVo.setIp(lines[1]);
                    return ipPoolVo;
                }).filter(ippVo -> ippVo != null && (filter == null || filter.test(ippVo)))
                .collect(Collectors.toList());
    }


    public void saveIpp(@Nonnull IpPoolVo ipPoolVo) {
        String userCode = ipPoolVo.getUserCode();
        String ip = ipPoolVo.getIp();
        List<IpPoolVo> oldIpp = readIpp(ipp -> ipp.getUserCode().equals(userCode) || ipp.getIp().equals(ip));
    }

    public List<String> readClientTemplate() {
        String templatePath = property.getClientTemplatePath();
        return readByPath(Path.of(templatePath));
    }

    public List<String> readTlsCryptBody() {
        return readTlsCryptByLimit("BEGIN OpenVPN Static key", "END OpenVPN Static key");
    }

    public List<String> readTlsCryptByLimit(@Nonnull String begin, @Nonnull String end) {
        //sed -ne '/BEGIN OpenVPN Static key/,$ p' /etc/openvpn/server/tc.key
        String path = property.getServerDir() + "/tc.key";
        return readByLimit(Path.of(path), begin, end);
    }


    public void saveClient(@Nonnull List<String> lines, @Nonnull String cn) {
        String path = property.getClientSaveDir() + "/" + cn + ".ovpn";
        save(Path.of(path), lines, StandardOpenOption.CREATE_NEW);
    }


    /**
     * 保存客户端配置到文件
     * @param userCode 用户编码
     * @param configs 用户配置列表
     */
    public void saveClientConfig(@Nonnull String userCode, @Nonnull List<UserConfigDto> configs) {

        Path path = FileUtil.unitePath(property.getClientConfigDir(), userCode);
        List<String> lines = configs.stream()
            .map(c -> {
                ConfigType type = c.getType();
                return String.format(type.getCmd(), c.getValue());
            })
            .collect(Collectors.toList());
        lines = lines.stream().filter(Objects::nonNull).sorted().collect(Collectors.toList());
        save(path, lines);
    }


    public void deleteClientConfig(@Nonnull String userCode) {
        Path path = FileUtil.unitePath(property.getClientConfigDir(), userCode);
        delete(path);
    }

    public List<String> readClientConfig(@Nonnull String userCode) {
        Path path = FileUtil.unitePath(property.getClientConfigDir(), userCode);
        if (path.toFile().isFile()) {
            return readByPath(path);
        }
        return null;
    }

    public void pushToken(@Nonnull String userCode, @Nonnull String token) {
        Path path = FileUtil.unitePath(property.getClientConfigDir(), userCode);
        String line = ConfigType.push_auth.getCmd().formatted(token);
        List<String> lines;
        if (path.toFile().isFile()) {
            lines = readByPath(path);
            lines = lines.stream()
                    .filter(l-> !l.contains("auth-token "))
                    .collect(Collectors.toList());
            lines.add(0, line);
        }
        else {
            lines = Collections.singletonList(line);
        }
        save(path, lines);
    }

}
