package me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.db.jpa.NanoJpaIDGenerator;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;

import java.time.LocalDateTime;


@Getter
@Setter
@Entity
@Table(name = IpLeasesRecordDo.ENTITY_NAME,
        indexes = {
                @Index(name = IpLeasesRecordDo.ENTITY_NAME+"_user_code_idx", columnList = "user_code"),
        })
@Comment("IP租约记录")
public class IpLeasesRecordDo {
    public static final String ENTITY_NAME = "ip_leases_record";

    @Id
    @GeneratedValue(generator = ENTITY_NAME)
    @GenericGenerator(name = ENTITY_NAME, type = NanoJpaIDGenerator.class)
    @Column(name = "id", nullable = false, length = 21)
    private String id;
    /**
     * ip地址的Long型表示
     */
    @Column(name = "ip_addr", nullable = false, length = 21)
    private Long ipAddr;
    /**
     * 用户编码
     */
    @Column(name = "user_code", nullable = false, updatable = false, length = 16)
    private String userCode;
    /**
     * 租约开始时间
     */
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;
    /**
     * 租约到期时间
     */
    @Column(name = "expire_time")
    private LocalDateTime expireTime;
    /**
     * 最后一次续约时间
     */
    @Column(name = "last_renew_time")
    private LocalDateTime lastRenewTime;

}
