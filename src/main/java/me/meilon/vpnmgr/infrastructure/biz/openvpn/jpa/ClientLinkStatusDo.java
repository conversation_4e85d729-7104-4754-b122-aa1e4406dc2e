package me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.app.args.ClientConnect;
import me.meilon.vpnmgr.app.dto.client.ConnStatus;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.enums.ConnState;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserDo;
import me.meilon.vpnmgr.infrastructure.db.jpa.NanoJpaIDGenerator;
import me.meilon.vpnmgr.infrastructure.management.v3.message.ClientEvent;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 客户端链接状态
 */
@Getter
@Setter
@Entity
@Table(name = ClientLinkStatusDo.ENTITY_NAME,
        indexes = {
                @Index(name = ClientLinkStatusDo.ENTITY_NAME + "_session_idx", columnList = "session_id"),
                @Index(name = ClientLinkStatusDo.ENTITY_NAME + "_user_code_idx", columnList = "user_code", unique = true),
        })
@Comment("客户端链接状态")
@EntityListeners(AuditingEntityListener.class)
public class ClientLinkStatusDo {
    public static final String ENTITY_NAME = "client_link_status";

    @Id
    @GeneratedValue(generator = ENTITY_NAME)
    @GenericGenerator(name = ENTITY_NAME,
            type = NanoJpaIDGenerator.class)
    @Column(name = "id", nullable = false, length = 21)
    private String id;

    /**
     * 客户端ID
     */
    @Nonnull
    @Column(name = "client_id", nullable = false, length = 20)
    private Long clientId;
    /**
     * 会话ID
     */
    @Column(name = "session_id", nullable = false, length = 32)
    private String sessionId;
    /**
     * 用户编码
     */
    @Nonnull
    @Column(name = "user_code", nullable = false, length = 16)
    private String userCode;
    /**
     * 用户名
     */
    @Column(name = "user_name", length = 32)
    private String userName;
    /**
     * 状态
     */
    @Nonnull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 10)
    private ConnState status;
    /**
     * 设备类型
     */
    @Column(name = "device_type", length = 35)
    private String deviceType;
    /**
     * 客户端版本
     */
    @Column(name = "client_version", length = 35)
    private String clientVersion;
    /**
     * 客户端本地地址
     */
    @Column(name = "trusted_address", nullable = false, length = 25)
    private String trustedAddress;
    /**
     * 客户端虚拟IP
     */
    @Column(name = "client_ip", nullable = false, length = 16)
    private String clientIp;
    /**
     * 链接的服务端IP
     */
    @Column(name = "server_ip", nullable = false, length = 16)
    private String serverIp;
    /**
     * 最后一次链接时间
     */
    @Column(name = "connect_time", nullable = false)
    private LocalDateTime connectTime;
    /**
     * 链接断开时间
     */
    @Column(name = "disconnect_time")
    private LocalDateTime disconnectTime;

    @JsonIgnore
    @Nonnull
    public static ClientLinkStatusDo create(ClientEvent client, String userName) {
        ClientLinkStatusDo statusDo = new ClientLinkStatusDo();
        statusDo.setStatus(ConnState.CONNECT);
        statusDo.setUserName(userName);
        statusDo.setClientId(client.getCid());
        statusDo.setSessionId(client.getSessionId());
        statusDo.setUserCode(client.getUsername());
        statusDo.setConnectTime(client.getTime());
        statusDo.setClientVersion(client.getIvVer());
        statusDo.setDeviceType(client.getDeviceType());
        statusDo.setTrustedAddress(client.getTrustedAddress());
        statusDo.setClientIp(client.getIfconfigPoolRemoteIp());
        statusDo.setServerIp(client.getIfconfigLocal());
        return statusDo;
    }

    public void update(ClientEvent client, ConnState status){
        this.setSessionId(client.getSessionId());
        this.setClientId(client.getCid());
        this.setClientVersion(client.getIvVer());
        this.setConnectTime(client.getTime());
        this.setDisconnectTime(null);
        this.setDeviceType(client.getDeviceType());
        this.setTrustedAddress(client.getTrustedAddress());
        this.setClientIp(client.getIfconfigPoolRemoteIp());
        this.setServerIp(client.getIfconfigLocal());
        this.setStatus(status);
    }



//    public static ClientLinkStatusDo create(@Nonnull ClientConnect args) {
//        ClientLinkStatusDo recordDo = new ClientLinkStatusDo();
//        recordDo.setUserCode(args.getUserCode());
//        recordDo.setDeviceType(args.getDeviceType());
//        recordDo.setTrustedAddress(args.getTrustedAddress());
//        recordDo.setClientIp(args.getClientIp());
//        recordDo.setServerIp(args.getServerIp());
//        recordDo.setStatus(ConnState.CONNECT);
//        recordDo.setConnectTime(LocalDateTime.now());
//        return recordDo;
//    }

//    public void updateConnect(@Nonnull ClientConnect args) {
//        this.setDeviceType(args.getDeviceType());
//        this.setTrustedAddress(args.getTrustedAddress());
//        this.setClientIp(args.getClientIp());
//        this.setServerIp(args.getServerIp());
//        this.setStatus(ConnState.CONNECT);
//        this.setConnectTime(LocalDateTime.now());
//        this.setDisconnectTime(null);
//    }

    public ConnStatus toDto() {
        ConnStatus dto = new ConnStatus();
        dto.setId(this.getId());
        dto.setUserCode(this.getUserCode());
        dto.setUserName(this.getUserName());
        dto.setStatus(this.getStatus());
        dto.setDeviceType(this.getDeviceType());
        dto.setTrustedAddress(this.getTrustedAddress());
        dto.setClientIp(this.getClientIp());
        dto.setServerIp(this.getServerIp());
        dto.setConnectTime(this.getConnectTime());
        dto.setDisconnectTime(this.getDisconnectTime());
        return dto;
    }


}
