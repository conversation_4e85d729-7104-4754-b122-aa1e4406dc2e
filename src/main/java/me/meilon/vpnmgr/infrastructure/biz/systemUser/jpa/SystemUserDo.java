package me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.app.args.user.UserAdd;
import me.meilon.vpnmgr.app.args.user.UserMod;
import me.meilon.vpnmgr.app.dto.UserDto;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.AuthMode;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.LoginMode;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.StatusEnum;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.UserType;
import me.meilon.vpnmgr.infrastructure.db.jpa.NanoJpaIDGenerator;
import me.meilon.vpnmgr.infrastructure.utils.MD5;
import me.meilon.vpnmgr.infrastructure.utils.StrUtils;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Duration;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = SystemUserDo.ENTITY_NAME,
        indexes = {
                @Index(name = SystemUserDo.ENTITY_NAME+"_user_code_idx", columnList = "user_code", unique = true),
                @Index(name = SystemUserDo.ENTITY_NAME+"_user_name_idx", columnList = "user_name"),
                @Index(name = SystemUserDo.ENTITY_NAME+"_bind_ip_idx", columnList = "bind_ip"),
        })
@Comment("系统用户表")
@EntityListeners(AuditingEntityListener.class)
public class SystemUserDo {
    public static final String ENTITY_NAME = "system_user";

    @Id
    @GeneratedValue(generator = ENTITY_NAME)
    @GenericGenerator(name = ENTITY_NAME, type = NanoJpaIDGenerator.class)
    @Column(name = "id", nullable = false, length = 21)
    private String id;
    /**
     * 系统用户名
     * 登录用
     */
    @Nonnull
    @Column(name = "user_code", nullable = false, updatable = false, length = 16)
    private String userCode;
    /**
     * 密码
     * md5 加密
     */
    @Nonnull
    @Column(name = "password", nullable = false, length = 512)
    private String password;
    /**
     * 是否是临时密码
     */
    @Nonnull
    @Column(name = "temp_pwd", nullable = false, length = 1)
    private Boolean tempPwd = false;
    /**
     * 密码错误次数
     */
    @Nonnull
    @Column(name = "pwd_err_cnt", nullable = false, length = 2)
    private Integer pwdErrCnt;
    /**
     * 用户真实姓名
     */
    @Nonnull
    @Column(name = "user_name", nullable = false, length = 32)
    private String userName;
    /**
     * 邮箱
     */
    @Column(name = "mail", length = 32)
    private String mail;
    /**
     * 手机号
     */
    @Column(name = "mobile_phone", length = 20)
    private String mobilePhone;
    /**
     * 用户类型
     */
    @Nonnull
    @Column(name = "user_type", length = 10)
    @Enumerated(EnumType.STRING)
    private UserType userType;
    /**
     * 状态
     */
    @Nonnull
    @Column(name = "status", nullable = false, length = 10)
    @Enumerated(EnumType.STRING)
    private StatusEnum status;
    /**
     * 多因素认证
     */
    @Nonnull
    @Column(name = "auth_mode", nullable = false, length = 10)
    @Enumerated(EnumType.STRING)
    private AuthMode authMode;
    /**
     * 登录模式
     */
    @Nonnull
    @Column(name = "login_mode", nullable = false, length = 10)
    @Enumerated(EnumType.STRING)
    private LoginMode loginMode = LoginMode.PWD;
    /**
     * 状态更新时间
     */
    @Column(name = "status_time")
    private LocalDateTime statusTime;
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "create_time")
    private LocalDateTime createTime;
    /**
     * 最后一次更新密码的时间
     */
    @LastModifiedDate
    @Column(name = "last_update_pwd_time")
    private LocalDateTime lastUpdatePwdTime;
    /**
     * 最后一次登录时间
     */
    @Column(name = "last_login_time")
    private LocalDateTime lastLoginTime;
    /**
     * 绑定证书
     */
    @Column(name = "certificate", length = 16)
    private String certificate;
    /**
     * 绑定IP
     */
    @Column(name = "bind_ip", length = 16)
    private String bindIp;

    public static SystemUserDo create(@Nonnull UserAdd args) {
        SystemUserDo user = new SystemUserDo();
        user.setUserCode(args.getUserCode());
        String pwd = args.getPassword();
        if (pwd == null || pwd.isBlank()){
            pwd = args.getUserCode() + "S123456";
        }
        user.setPassword(MD5.encode(pwd));
        user.setUserName(args.getUserName());
        user.setMail(args.getMail());
        user.setMobilePhone(args.getMobilePhone());
        user.setUserType(args.getType());
        user.setStatus(StatusEnum.ENABLE);
        user.setStatusTime(LocalDateTime.now());
        user.setPwdErrCnt(0);
        user.setAuthMode(AuthMode.NONE);
        user.setTempPwd(true);
        user.setLoginMode(LoginMode.PWD);
        user.setCertificate(args.getClientName());
        user.setBindIp(args.getBindIp());
        return user;
    }

    /**
     * 查看多少天未更新密码
     */
    @JsonIgnore
    public long getPwdUpdateDays() {
        return Duration.between(this.lastUpdatePwdTime, LocalDateTime.now()).toDays();
    }

    public UserDto toDto(SystemUserDo this){
        UserDto dto = new UserDto();
        dto.setId(this.getId());
        dto.setUserCode(this.getUserCode());
        dto.setUserName(this.getUserName());
        dto.setType(this.getUserType());
        dto.setMail(this.getMail());
        dto.setMobilePhone(this.getMobilePhone());
        dto.setStatusCd(this.getStatus().code);
        dto.setAuthMode(this.getAuthMode());
        dto.setClientName(this.getCertificate());
        dto.setBindIp(this.getBindIp());
        dto.setLastLoginTime(this.getLastLoginTime());
        dto.setCreateTime(this.getCreateTime());
        dto.setLastUpdatePwdTime(this.getLastUpdatePwdTime());
        return dto;
    }

    public void update(UserMod args) {
        this.setUserName(args.getUserName());
        this.setMail(args.getMail());
        this.setMobilePhone(args.getMobilePhone());
        this.setAuthMode(args.getAuthMode());
        this.setCertificate(args.getClientName());
        this.setBindIp(args.getBindIp());
    }

    public boolean comparePassword(String pwd){
        if (StrUtils.isBlank(pwd)){
            return false;
        }
        return this.password.equals(MD5.encode(pwd));
    }

    public void updatePassword(String pwd){
        this.setPassword(MD5.encode(pwd));
        this.setLastUpdatePwdTime(LocalDateTime.now());
    }

    public void updateStatus(@Nonnull StatusEnum status){
        this.setStatus(status);
        this.setStatusTime(LocalDateTime.now());
    }
}
