package me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa;

import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.app.args.ClientConnect;
import me.meilon.vpnmgr.app.dto.LeasesDto;
import me.meilon.vpnmgr.infrastructure.db.jpa.NanoJpaIDGenerator;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;

import java.time.LocalDateTime;

/**
 * 用户租约表
 */
@Getter
@Setter
@Entity
@Table(name = LeasesDo.ENTITY_NAME,
        indexes = {
                @Index(name = LeasesDo.ENTITY_NAME+"_user_code_idx", columnList = "user_code"),
        })
@Comment("用户租约表")
public class LeasesDo {
    public static final String ENTITY_NAME = "client_leases";

    @Id
    @GeneratedValue(generator = ENTITY_NAME)
    @GenericGenerator(name = ENTITY_NAME, type = NanoJpaIDGenerator.class)
    @Column(name = "id", nullable = false, length = 21)
    private String id;
    /**
     * 用户编码
     */
    @Column(name = "user_code", nullable = false, updatable = false, length = 16)
    private String userCode;

    /**
     * 绑定的IP
     */
    @Nonnull
    @Column(name = "bind_ip", nullable = false, updatable = false, length = 16)
    private String bindIp;
    /**
     * 绑定时间
     * 首次将IP分配给用户的时间
     */
    @Column(name = "bind_date", nullable = false, updatable = false)
    private LocalDateTime bindDate;
    /**
     * 释放日期
     */
    @Column(name = "release_date")
    private LocalDateTime releaseDate;
    /**
     * 续约时间
     */
    @Column(name = "renew_date")
    private LocalDateTime renewDate;
    /**
     * 解绑时间
     */
    @Column(name = "unbind_date", updatable = false)
    private LocalDateTime unbindDate;

    @Nonnull
    public static LeasesDo create(@Nonnull ClientConnect args) {
        LeasesDo leasesDo = new LeasesDo();
        leasesDo.setUserCode(args.getUserCode());
        leasesDo.setBindIp(args.getClientIp());
        leasesDo.setBindDate(LocalDateTime.now());
        return leasesDo;
    }


    public LeasesDto toDto(LeasesDo this) {
        LeasesDto dto = new LeasesDto();
        dto.setId(this.getId());
        dto.setUserCode(this.getUserCode());
        dto.setIp(this.getBindIp());
        dto.setBindDate(this.getBindDate());
        dto.setReleaseDate(this.getReleaseDate());
        dto.setRenewDate(this.getRenewDate());
        dto.setUnbindDate(this.getUnbindDate());
        return dto;
    }
}
