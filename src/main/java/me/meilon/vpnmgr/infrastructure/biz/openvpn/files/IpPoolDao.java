package me.meilon.vpnmgr.infrastructure.biz.openvpn.files;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.conf.OpenvpnProperty;
import me.meilon.vpnmgr.infrastructure.db.files.LocalFileBaseDao;
import me.meilon.vpnmgr.infrastructure.utils.FileUtil;
import me.meilon.vpnmgr.infrastructure.utils.StrUtils;
import org.springframework.stereotype.Component;

import java.nio.file.Path;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class IpPoolDao implements LocalFileBaseDao {

    private final OpenvpnProperty property;


    public IpPoolVo readIppByIp(@Nonnull String ip){
        List<IpPoolVo> ipPoolVos = readIppAll(ippVo -> ippVo.getIp().equals(ip));
        if (ipPoolVos == null || ipPoolVos.isEmpty()){
            return null;
        }
        return ipPoolVos.get(0);
    }

    public List<IpPoolVo> readIppAllByUserCode(@Nullable String userCode){
        if (StrUtils.isBlank(userCode)){
            return readIppAll(null);
        }
        else {
            return readIppAll(ippVo -> ippVo.getUserCode().equals(userCode));
        }
    }

    public List<IpPoolVo> readIppAll(){
        return readIppAll(null);
    }

    public List<IpPoolVo> readIppAll(Predicate<IpPoolVo> filter) {
        String path = FileUtil.unite(property.getServerDir(), "ipp.txt");
        return readByPath(Path.of(path)).stream()
                .map(line->{
                    if (StrUtils.isBlank(line)){
                        return null;
                    }
                    String[] lines = line.split(",");
                    IpPoolVo ipPoolVo = new IpPoolVo();
                    ipPoolVo.setUserCode(lines[0]);
                    ipPoolVo.setIp(lines[1]);
                    return ipPoolVo;
                }).filter(ippVo -> ippVo != null && (filter == null || filter.test(ippVo)))
                .collect(Collectors.toList());
    }


    public void saveAll(@Nonnull List<IpPoolVo> ipPoolVoList){
        List<String> ipps = ipPoolVoList.stream().distinct()
                .map(ippVo -> ippVo.getUserCode() +","+ippVo.getIp()+",")
                .collect(Collectors.toList());

        String path = FileUtil.unite(property.getServerDir(), "ipp.txt");
        save(Path.of(path), ipps);
    }


}
