package me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;


public interface UserConfigRepository extends JpaRepository<UserConfigDo, String>,
        JpaSpecificationExecutor<UserConfigDo> {

    List<UserConfigDo> findByObjId(String id);

    List<UserConfigDo> findByUserId(String userId);
}
