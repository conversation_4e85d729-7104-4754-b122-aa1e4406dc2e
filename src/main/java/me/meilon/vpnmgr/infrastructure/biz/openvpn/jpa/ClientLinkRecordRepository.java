package me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa;


import jakarta.persistence.criteria.Predicate;
import me.meilon.vpnmgr.app.args.ClientLinkRecordPageQry;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public interface ClientLinkRecordRepository extends JpaRepository<ClientLinkRecordDo, String>,
        JpaSpecificationExecutor<ClientLinkRecordDo> {


    default Page<ClientLinkRecordDo> query(Pageable pageable, ClientLinkRecordPageQry args){
        return findAll((root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            String userCode = args.getUserCode();
            if (userCode != null && !userCode.isEmpty()) {
                predicates.add(builder.equal(root.get("userCode"), userCode));
            }
            LocalDateTime connTime = args.getConnTime();
            if (connTime != null ) {
                predicates.add(builder.equal(root.get("connectTime"), connTime));
            }
            return query.where(predicates.toArray(Predicate[]::new)).getRestriction();
        }, pageable);
    }

}
