package me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa;


import jakarta.annotation.Nonnull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;


public interface LeasesRepository extends JpaRepository<LeasesDo, String>,
        JpaSpecificationExecutor<LeasesDo> {


    List<LeasesDo> findAllByUserCode(@Nonnull String userCode);

    List<LeasesDo> findAllByUnbindDateIsNull();

    List<LeasesDo> findAllByUserCodeAndUnbindDateIsNull(@Nonnull String userCode);

}
