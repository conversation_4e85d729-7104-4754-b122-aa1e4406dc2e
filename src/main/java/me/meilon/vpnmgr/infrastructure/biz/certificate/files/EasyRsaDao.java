package me.meilon.vpnmgr.infrastructure.biz.certificate.files;

import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import me.meilon.vpnmgr.infrastructure.conf.EasyRsaProperty;
import me.meilon.vpnmgr.infrastructure.db.files.LocalFileBaseDao;
import me.meilon.vpnmgr.infrastructure.utils.shell.ProcessResult;
import me.meilon.vpnmgr.infrastructure.utils.shell.ShellRunTime;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.Path;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Component
@RequiredArgsConstructor
public class EasyRsaDao implements LocalFileBaseDao {

    private final EasyRsaProperty property;

    /**
     * 读取客户端列表
     *
     * @param filter 过滤器
     * @return 每行一条数据
     *  样例 : V	20921106055435Z		EED61C02EFB41A1BC91AF6F21E802D9C	unknown	/CN=LX_test
     * 0 V
     * 1 20921106055435Z
     * 2
     * 3 EED61C02EFB41A1BC91AF6F21E802D9C
     * 4 unknown
     * 5 /CN=LX_test
     */
    public List<String> readClientList(Predicate<String> filter){
        ShellRunTime shell = ShellRunTime.getRuntime();
        String indexFile = property.getBaseDir() + "/pki/index.txt";
        ProcessResult out = shell.execAndWaitResult("tail","-n", "+2", indexFile);
        out.orElseThrow("读取证书列表失败");
        if (filter != null){
            return out.getExecuteOut().stream().filter(filter).collect(Collectors.toList());
        }
        return out.getExecuteOut();
    }

    public List<CertificateVo> readClientAll(Predicate<CertificateVo> filter){
        List<String> clientList = readClientList(null);
        Stream<CertificateVo> stream = clientList.stream().map(line-> {
            String[] lines = line.split("\\t");
            CertificateVo certificateVo = new CertificateVo();
            certificateVo.setStatusCd(lines[0]);
            certificateVo.setKey(lines[1]);
            certificateVo.setSerialNumber(lines[3]);
            String name = lines[5];
            certificateVo.setClientName(name.replace("/CN=", ""));
            return certificateVo;
        });
        if (filter != null){
            stream = stream.filter(filter);
        }
        return stream.collect(Collectors.toList());
    }

    public List<String> readCa(){
        //cat /etc/openvpn/server/easy-rsa/pki/ca.crt
        String path = property.getBaseDir() + "/pki/ca.crt";
        return readByPath(Path.of(path));
    }

    public boolean existsCert(@Nonnull String cn){
        String path = property.getBaseDir() + "/pki/issued/"+cn+".crt";
        File certFile = new File(path);
        return certFile.exists();
    }

    public List<String> readCertBody(@Nonnull String cn){
        //sed -ne '/BEGIN CERTIFICATE/,$ p' /etc/openvpn/server/easy-rsa/pki/issued/"$client".crt
        String path = property.getBaseDir() + "/pki/issued/"+cn+".crt";
        return readByLimit(Path.of(path), "BEGIN CERTIFICATE", "END CERTIFICATE");
    }

    public List<String> readCertByLimit(@Nonnull String cn, 
                                        @Nonnull String begin, @Nonnull String end){
        String path = property.getBaseDir() + "/pki/issued/"+cn+".crt";
        return readByLimit(Path.of(path), begin, end);
    }

    public List<String> readKey(@Nonnull String cn){
        //cat /etc/openvpn/server/easy-rsa/pki/private/"$client".key
        String path = property.getBaseDir() + "/pki/private/"+cn+".key";
        return readByPath(Path.of(path));
    }


}
