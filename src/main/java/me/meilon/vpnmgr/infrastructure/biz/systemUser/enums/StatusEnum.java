package me.meilon.vpnmgr.infrastructure.biz.systemUser.enums;



public enum StatusEnum {

    /**
     * 启用
     */
    ENABLE(1)
    ,

    /**
     * 禁用
     */
    DISABLED(0);

    public final int code;

    StatusEnum(int code) {
        this.code = code;
    }

    public static StatusEnum codeOf(Integer code){
        if (code == null){
            return null;
        }
        for (StatusEnum value : StatusEnum.values()) {
            if (value.code == code){
                return value;
            }
        }
        return null;
    }

}
