package me.meilon.vpnmgr.infrastructure.biz.resource.cli;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.biz.resource.jpa.ResourceDo;
import me.meilon.vpnmgr.infrastructure.conf.OpenvpnProperty;
import me.meilon.vpnmgr.infrastructure.utils.shell.ProcessResult;
import me.meilon.vpnmgr.infrastructure.utils.shell.ShellRunTime;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;

@Slf4j
@Component
@RequiredArgsConstructor
public class RouteCli {

    private final OpenvpnProperty openvpnProperty;
    private final String cmdTemplate = "-net {0} netmask {1} gw {2} dev tun0";

    public void addRoute(ResourceDo resourceDo){
        if (checkRoute(resourceDo)){
            return;
        }
        // 执行 route add 命令
        // route add -net {IP} netmask {mask} gw {gw} dev tun0
        String gw = getGateway();
        ShellRunTime shellRunTime = ShellRunTime.getRuntime();
        // cmd 生成命令
        String cmd = MessageFormat.format(cmdTemplate, resourceDo.getIp(), resourceDo.getNetmask(), gw);
        ProcessResult result = shellRunTime.execAndWaitResult("route add "+cmd);
        result.orElseThrow("添加资源路由失败");
    }

    public void delRoute(ResourceDo resourceDo){
        if (!checkRoute(resourceDo)){
            return;
        }
        // 执行 route del 命令
        // route del -net {IP} netmask {mask} gw {gw} dev tun0
        String gw = getGateway();
        String cmd = MessageFormat.format(cmdTemplate, resourceDo.getIp(), resourceDo.getNetmask(), gw);
        ShellRunTime shellRunTime = ShellRunTime.getRuntime();
        ProcessResult result = shellRunTime.execAndWaitResult("route del "+cmd);
        result.orElseThrow("删除资源路由失败");
    }

    /**
     * 查询路由
     * Kernel IP routing table
     * Destination     Gateway         Genmask         Flags Metric Ref    Use Iface
     * 0.0.0.0         ***********     0.0.0.0         UG    100    0        0 eno1
     * ************    ********        *************** UGH   0      0        0 tun0
     * ************    ********        *************** UGH   0      0        0 tun0
     * ************    ********        *************** UGH   0      0        0 tun0
     * ************    ********        *************** UGH   0      0        0 tun0
     * ********        0.0.0.0         *************   U     0      0        0 tun0
     * @param resourceDo 资源实例
     */
    public boolean checkRoute(ResourceDo resourceDo){
        String ip = convert(resourceDo.getIp());
        String gw = convert(getGateway());
        String netmask = convert(resourceDo.getNetmask());
        String param = MessageFormat.format("\"^{0} *{1} *{2} .*tun0$\"", ip, gw, netmask);

        ShellRunTime shellRunTime = ShellRunTime.getRuntime();
        ProcessResult result = shellRunTime.execAndWaitResult(false,"bash", "-c", "route -n | grep "+ param);
        String res = result.orElseGetOutString();
        return res != null && res.startsWith(resourceDo.getIp());
    }

    private String getGateway(){
        return openvpnProperty.getSubNetwork() + ".1";
    }

    private String convert(String string){
        return string.replace(".", "\\.");
    }

    private boolean isPositiveInteger(String str){
        return str.matches("^\\d+$");
    }

}
