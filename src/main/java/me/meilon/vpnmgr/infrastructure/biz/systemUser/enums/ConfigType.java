package me.meilon.vpnmgr.infrastructure.biz.systemUser.enums;

/**
 * 配置类型
 * <p>
 * The following options are legal in a client-specific context:
 * --push, --push-reset, --push-remove, --iroute, --ifconfig-push, --vlan-pvid and --config.
 */
public enum ConfigType {

    /**
     * --push option
     * Push a config file option back to the client for remote execution.
     * Note that option must be enclosed in double quotes ().
     * The client must specify --pull in its config file.
     * The set of options which can be pushed is limited by both feasibility and security.
     * Some options such as those which would execute scripts are banned, since they would effectively allow a compromised server to execute arbitrary code on the client.
     * Other options such as TLS or MTU parameters cannot be pushed because the client needs to know them before the connection to the server can be initiated.""
     * This is a partial list of options which can currently be pushed: --route, --route-gateway, --route-delay, --redirect-gateway, --ip-win32, --dhcp-option, --inactive, --ping, --ping-exit, --ping-restart, --setenv, --auth-token, --persist-key, --persist-tun, --echo, --comp-lzo, --socket-flags, --sndbuf, --rcvbuf
     */
    push("push"),
    /**
     * 同上
     * 推送路由的专用配置
     * --push "route {network} {netmask} {gateway} {metric}"
     */
    push_route("push \"route %s\""),

    push_auth("push \"auth-token %s\""),
    /**
     * Don't inherit the global push list for a specific client instance.
     * Specify this option in a client-specific context such as with a --client-config-dir configuration file.
     * This option will ignore --push options at the global config file level.
     * NOTE: --push-reset is very thorough: it will remove almost all options from the list of to-be-pushed options.
     * In many cases, some of these options will need to be re-configured afterwards - specifically, --topology subnet and --route-gateway will get lost and this will break client configs in many cases.
     * Thus, for most purposes, --push-remove is better suited to selectively remove push options for individual clients.
     */
    push_reset("push-reset"),
    /**
     * --push-remove opt
     * <p>
     * Selectively remove all --push options matching "opt" from the option list for a client. opt is matched as a substring against the whole option string to-be-pushed to the client, so --push-remove route would remove all --push route ... and --push route-ipv6 ... statements, while --push-remove "route-ipv6 2001:" would only remove IPv6 routes for networks.2001:...
     * <p>
     * --push-remove can only be used in a client-specific context, like in a --client-config-dir file, or --client-connect script or plugin -- similar to --push-reset, just more selective.
     * <p>
     * NOTE: to change an option, --push-remove can be used to first remove the old value, and then add a new --push option with the new value.
     * <p>
     * NOTE 2: due to implementation details, 'ifconfig' and 'ifconfig-ipv6' can only be removed with an exact match on the option ( ), no substring matching and no matching on the IPv4/IPv6 address argument is possible.push-remove ifconfig
     */
    push_remove("push-remove"),
    /**
     * --iroute args
     * Generate an internal route to a specific client. The netmask parameter, if omitted, defaults to .***************
     * <p>
     * Valid syntax:
     * <p>
     * iroute network [netmask]
     * This directive can be used to route a fixed subnet from the server to a particular client, regardless of where the client is connecting from. Remember that you must also add the route to the system routing table as well (such as by using the --route directive). The reason why two routes are needed is that the --route directive routes the packet from the kernel to OpenVPN. Once in OpenVPN, the --iroute directive routes to the specific client.
     * <p>
     * This option must be specified either in a client instance config file using --client-config-dir or dynamically generated using a --client-connect script.
     * <p>
     * The --iroute directive also has an important interaction with --push "route ...". --iroute essentially defines a subnet which is owned by a particular client (we will call this client A). If you would like other clients to be able to reach A's subnet, you can use --push "route ..." together with --client-to-client to effect this. In order for all clients to see A's subnet, OpenVPN must push this route to all clients EXCEPT for A, since the subnet is already owned by A. OpenVPN accomplishes this by not not pushing a route to a client if it matches one of the client's iroutes.
     */
    iroute("iroute %s"),
    /**
     * --ifconfig-push args
     * <p>
     * Push virtual IP endpoints for client tunnel, overriding the --ifconfig-pool dynamic allocation.
     * <p>
     * Valid syntax:
     * <p>
     * ifconfig-push local remote-netmask [alias]
     * The parameters local and remote-netmask are set according to the --ifconfig directive which you want to execute on the client machine to configure the remote end of the tunnel. Note that the parameters local and remote-netmask are from the perspective of the client, not the server. They may be DNS names rather than IP addresses, in which case they will be resolved on the server at the time of client connection.
     * <p>
     * The optional alias parameter may be used in cases where NAT causes the client view of its local endpoint to differ from the server view. In this case local/remote-netmask will refer to the server view while alias/remote-netmask will refer to the client view.
     * <p>
     * This option must be associated with a specific client instance, which means that it must be specified either in a client instance config file using --client-config-dir or dynamically generated using a --client-connect script.
     * <p>
     * Remember also to include a --route directive in the main OpenVPN config file which encloses local, so that the kernel will know to route it to the server's TUN/TAP interface.
     * <p>
     * OpenVPN's internal client IP address selection algorithm works as follows:
     * <p>
     * 1. Use --client-connect script generated file for static IP (first choice).
     * <p>
     * 2. Use --client-config-dir file for static IP (next choice).
     * <p>
     * 3. Use --ifconfig-pool allocation for dynamic IP (last choice).
     */
    ifconfig_push("ifconfig-push %s"),
    /**
     * --vlan-pvid v
     * <p>
     * Specifies which VLAN identifier a "port" is associated with. Only valid when --vlan-tagging is speficied.
     *<p>
     * In the client context, the setting specifies which VLAN ID a client is associated with. In the global context, the VLAN ID of the server TAP device is set. The latter only makes sense for --vlan-accept untagged and --vlan-accept all modes.
     *<p>
     * Valid values for v go from through to . The global value defaults to . If no --vlan-pvid is specified in the client context, the global value is inherited.140941
     *<p>
     * In some switch implementations, the PVID is also referred to as "Native VLAN".
     */
    vlan_pvid("vlan-pvid"),
    /**
     * --config file
     * <p>
     * Load additional config options from file where each line corresponds to one command line option, but with the leading '--' removed.
     *<p>
     * If --config file is the only option to the openvpn command, the --config can be removed, and the command can be given as openvpn file
     *<p>
     * Note that configuration files can be nested to a reasonable depth.
     *<p>
     * Double quotation or single quotation characters ("", '') can be used to enclose single parameters containing whitespace, and "#" or ";" characters in the first column can be used to denote comments.
     *<p>
     * Note that OpenVPN 2.0 and higher performs backslash-based shell escaping for characters not in single quotations, so the following mappings should be observed:
     *<p>
     * \\       Maps to a single backslash character (\).
     * \"       Pass a literal doublequote character ("), don't
     *          interpret it as enclosing a parameter.
     * \[SPACE] Pass a literal space or tab character, don't
     *          interpret it as a parameter delimiter.
     * For example on Windows, use double backslashes to represent pathnames:
     *<p>
     * secret "c:\\OpenVPN\\secret.key"
     * For examples of configuration files, see <a href="https://openvpn.net/community-resources/how-to/">https://openvpn.net/community-resources/how-to/</a>
     *<p>
     * Here is an example configuration file:
     *<p>
     * #
     * # Sample OpenVPN configuration file for
     * # using a pre-shared static key.
     * #
     * # '#' or ';' may be used to delimit comments.
     *<p>
     * # Use a dynamic tun device.
     * dev tun
     *<p>
     * # Our remote peer
     * remote mypeer.mydomain
     *<p>
     * # 10.1.0.1 is our local VPN endpoint
     * # 10.1.0.2 is our remote VPN endpoint
     * ifconfig 10.1.0.1 10.1.0.2
     *<p>
     * # Our pre-shared static key
     * secret static.key
     */
    config("config")
    ;

    private final String cmd;

    ConfigType(String cmd) {
        this.cmd = cmd;
    }

    public String getCmd() {
        return cmd;
    }
}
