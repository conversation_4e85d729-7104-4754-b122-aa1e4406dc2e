package me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa;

import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.app.args.ClientConnect;
import me.meilon.vpnmgr.app.dto.ClientConnRecord;
import me.meilon.vpnmgr.app.dto.client.ConnRecord;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.enums.ConnRecordType;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.enums.ConnState;
import me.meilon.vpnmgr.infrastructure.db.jpa.NanoJpaIDGenerator;
import me.meilon.vpnmgr.infrastructure.management.v3.message.ClientEvent;
import me.meilon.vpnmgr.infrastructure.utils.DateUtil;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 客户端链接记录
 */
@Getter
@Setter
@Entity
@Table(name = ClientLinkRecordDo.ENTITY_NAME,
        indexes = {
                @Index(name = ClientLinkRecordDo.ENTITY_NAME+"_user_code_idx", columnList = "user_code"),
                @Index(name = ClientLinkRecordDo.ENTITY_NAME+"_session_id", columnList = "session_id")
        })
@Comment("客户端链接记录")
@EntityListeners(AuditingEntityListener.class)
public class ClientLinkRecordDo {
    public static final String ENTITY_NAME = "client_link_record";

    @Id
    @GeneratedValue(generator = ENTITY_NAME)
    @GenericGenerator(name = ENTITY_NAME, type = NanoJpaIDGenerator.class)
    @Column(name = "id", nullable = false, length = 21)
    private String id;

    /**
     * 会话ID
     */
    @Nonnull
    @Column(name = "session_id", updatable = false, nullable=false, length = 32)
    private String sessionId;
    /**
     * 用户编码
     */
    @Column(name = "user_code", updatable = false, nullable=false, length = 16)
    private String userCode;
    /**
     * 用户名称
     */
    @Column(name = "user_name", updatable = false, length = 16)
    private String userName;
    /**
     * 记录类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "type", length = 15)
    private ConnRecordType type;
    /**
     * 设备类型
     */
    @Column(name = "device_type", updatable = false, length = 35)
    private String deviceType;
    /**
     * 客户端版本
     */
    @Column(name = "client_version", updatable = false, length = 35)
    private String clientVersion;
    /**
     * 出访地址
     */
    @Column(name = "trusted_address", updatable = false, length = 25)
    private String trustedAddress;
    /**
     * 客户端虚拟IP
     */
    @Column(name = "client_ip", updatable = false, length = 16)
    private String clientIp;
    /**
     * 链接的服务端IP
     */
    @Column(name = "server_ip", updatable = false, length = 16)
    private String serverIp;
    /**
     * 链接时长
     */
    @Column(name = "duration", length = 20)
    private String duration;
    /**
     * 链接时间
     */
    @Nonnull
    @Column(name = "connect_time", nullable = false, updatable = false)
    private LocalDateTime connectTime;
    /**
     * 断开时间
     */
    @Column(name = "disconnect_time")
    private LocalDateTime disconnectTime;

    public static ClientLinkRecordDo create(@Nonnull ClientEvent client, String name, @Nonnull ConnRecordType type){
        ClientLinkRecordDo recordDo = new ClientLinkRecordDo();
        recordDo.setSessionId(client.getSessionId());
        recordDo.setUserCode(client.getUsername());
        recordDo.setUserName(name);
        recordDo.setClientVersion(client.getIvVer());
        recordDo.setDeviceType(client.getDeviceType());
        recordDo.setTrustedAddress(client.getTrustedAddress());
        recordDo.setClientIp(client.getIfconfigPoolRemoteIp());
        recordDo.setServerIp(client.getIfconfigLocal());
        recordDo.setConnectTime(client.getTime());
        recordDo.setDuration(client.getTimeDuration());
        recordDo.setType(type);
        return recordDo;
    }

    public static ClientLinkRecordDo create(@Nonnull ClientLinkStatusDo args, @Nonnull ConnRecordType type) {
        ClientLinkRecordDo recordDo = new ClientLinkRecordDo();
        recordDo.setSessionId(args.getSessionId());
        recordDo.setUserCode(args.getUserCode());
        recordDo.setUserName(args.getUserName());
        recordDo.setClientVersion(args.getClientVersion());
        recordDo.setDeviceType(args.getDeviceType());
        recordDo.setTrustedAddress(args.getTrustedAddress());
        recordDo.setClientIp(args.getClientIp());
        recordDo.setServerIp(args.getServerIp());
        recordDo.setConnectTime(args.getConnectTime());
        recordDo.setType(type);
        return recordDo;
    }

    public ConnRecord toEntity(){
        ConnRecord entity = new ConnRecord();
        entity.setSessionId(this.getSessionId());
        entity.setType(this.getType());
        entity.setUserCode(this.getUserCode());
        entity.setUserName(this.getUserName());
        entity.setDeviceType(this.getDeviceType());
        entity.setTrustedAddress(this.getTrustedAddress());
        entity.setClientIp(this.getClientIp());
        entity.setServerIp(this.getServerIp());
        entity.setDuration(this.getDuration());
        entity.setConnectTime(this.getConnectTime());
        entity.setDisconnectTime(this.getDisconnectTime());
        return entity;
    }
}
