package me.meilon.vpnmgr.infrastructure.biz.totp.jpa;

import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.db.jpa.NanoJpaIDGenerator;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = TotpDo.ENTITY_NAME,
        indexes = {
                @Index(name = TotpDo.ENTITY_NAME+"_user_code_idx", columnList = "user_code"),
        })
@Comment("动态码表")
@EntityListeners(AuditingEntityListener.class)
public class TotpDo {
    public static final String ENTITY_NAME = "totp";

    @Id
    @GeneratedValue(generator = ENTITY_NAME)
    @GenericGenerator(name = ENTITY_NAME, type = NanoJpaIDGenerator.class)
    @Column(name = "id", nullable = false, length = 21)
    private String id;
    /**
     * 系统用户名
     * 登录用
     */
    @Column(name = "user_code", nullable = false, updatable = false, length = 16)
    private String userCode;
    /**
     * 密钥
     * 动态码验证用
     */
    @Nonnull
    @Column(name = "secret_key", nullable = false, length = 512)
    private String secretKey;
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "create_time")
    private LocalDateTime createTime;
    /**
     * 最后一次更新时间
     */
    @LastModifiedDate
    @Column(name = "last_update_time")
    private LocalDateTime lastUpdateTime;

}
