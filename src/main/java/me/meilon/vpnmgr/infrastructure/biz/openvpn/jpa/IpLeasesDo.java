package me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import java.time.LocalDateTime;


@Getter
@Setter
@Entity
@Table(name = IpLeasesDo.ENTITY_NAME,
        indexes = {
                @Index(name = IpLeasesDo.ENTITY_NAME+"_user_code_idx", columnList = "user_code", unique = true),
        })
@Comment("IP租约表")
public class IpLeasesDo {
    public static final String ENTITY_NAME = "ip_leases";

    /**
     * 使用ip地址的Long型表示作为ID
     */
    @Id
    @Column(name = "id", nullable = false, length = 21)
    private Long id;
    /**
     * 用户编码
     */
    @Column(name = "user_code", nullable = false, length = 16)
    private String userCode;
    /**
     * 租约开始时间
     */
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;
    /**
     * 租约到期时间
     * 如果租约到期时间为空, 则表示租约无限期
     */
    @Column(name = "expire_time")
    private LocalDateTime expireTime;
    /**
     * 续约时间
     */
    @Column(name = "renew_time")
    private LocalDateTime renewTime;
    /**
     * 回收
     * true: 已回收
     * false: 未回收
     */
    @Column(name = "recycled", nullable = false)
    private Boolean recycled = false;


}
