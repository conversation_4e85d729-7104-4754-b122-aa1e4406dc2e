package me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa;


import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = OperationLogDo.ENTITY_NAME,
        indexes = {
                @Index(name = OperationLogDo.ENTITY_NAME+"_user_code_idx", columnList = "user_code"),
        })
@Comment("操作记录")
@EntityListeners(AuditingEntityListener.class)
public class OperationLogDo {
    public static final String ENTITY_NAME = "operation_log";

    @Id
    @Column(name = "id", nullable = false, length = 20)
    private String id;
    /**
     * 系统用户名
     * 登录用
     */
    @Column(name = "user_code", nullable = false, length = 16)
    private String userCode;
    /**
     * 用户真实姓名
     */
    @Column(name = "user_name", length = 15)
    private String userName;
    /**
     * 路径
     */
    @Column(name = "path", length = 100)
    private String path;
    /**
     * 浏览器信息
     */
    @Column(name = "user_agent", length = 100)
    private String userAgent;
    /**
     * 登录时间
     */
    @CreatedDate
    @Column(name = "op_time")
    private LocalDateTime opTime;
}
