package me.meilon.vpnmgr.infrastructure.biz.resource.jpa;

import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.db.jpa.NanoJpaIDGenerator;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * 资源关系表
 */
@Getter
@Setter
@Entity
@Table(name = ResourceRelationDo.ENTITY_NAME,
        indexes = {
                @Index(name = ResourceRelationDo.ENTITY_NAME+"_group_idx", columnList = "group_id"),
                @Index(name = ResourceRelationDo.ENTITY_NAME+"_resource_idx", columnList = "resource_id"),
        })
@Comment("资源关系表")
@EntityListeners(AuditingEntityListener.class)
public class ResourceRelationDo {
    public static final String ENTITY_NAME = "resource_relation";

    @Id
    @GeneratedValue(generator = ENTITY_NAME)
    @GenericGenerator(name = ENTITY_NAME, type = NanoJpaIDGenerator.class)
    @Column(name = "id", nullable = false, length = 21)
    private String id;
    /**
     * 资源组ID
     */
    @Nonnull
    @Column(name = "group_id", nullable = false, updatable = false, length = 21)
    private String groupId;
    /**
     * 资源Id
     */
    @Nonnull
    @Column(name = "resource_id", nullable = false, updatable = false, length = 21)
    private String resourceId;

}
