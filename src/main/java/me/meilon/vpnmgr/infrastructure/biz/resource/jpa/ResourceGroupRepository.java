package me.meilon.vpnmgr.infrastructure.biz.resource.jpa;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;


public interface ResourceGroupRepository extends JpaRepository<ResourceGroupDo, String>,
        JpaSpecificationExecutor<ResourceGroupDo> {

  Optional<ResourceGroupDo> findByName(String name);

  List<ResourceGroupDo> findByOwnerUserId(String id);
}
