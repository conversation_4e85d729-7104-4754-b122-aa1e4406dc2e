package me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa;


import jakarta.annotation.Nonnull;
import jakarta.persistence.criteria.Predicate;
import me.meilon.vpnmgr.app.args.ConnStatusPageQry;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.enums.ConnState;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public interface ClientLinkStatusRepository extends JpaRepository<ClientLinkStatusDo, String>,
        JpaSpecificationExecutor<ClientLinkStatusDo> {

    Optional<ClientLinkStatusDo> findOneByUserCode(@Nonnull String userCode);


    default Page<ClientLinkStatusDo> queryPage(Pageable pageable, ConnStatusPageQry args){
        return findAll((root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            String userCode = args.getUserCode();
            if (userCode != null && !userCode.isEmpty()) {
                predicates.add(builder.equal(root.get("userCode"), userCode));
            }
            ConnState state = args.getStatus();
            if (state != null) {
                predicates.add(builder.equal(root.get("status"), state));
            }
            String clientIp = args.getClientIp();
            if (clientIp != null && !clientIp.isEmpty()) {
                predicates.add(builder.like(root.get("clientIp"), "%" + clientIp + "%"));
            }
            return query.where(predicates.toArray(new Predicate[0])).getRestriction();
        }, pageable);
    }

}
