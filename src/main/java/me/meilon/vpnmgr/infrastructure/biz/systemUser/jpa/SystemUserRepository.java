package me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa;


import jakarta.annotation.Nonnull;
import jakarta.persistence.criteria.Predicate;
import me.meilon.vpnmgr.app.args.user.UserPageQry;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.StatusEnum;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.UserType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public interface SystemUserRepository extends JpaRepository<SystemUserDo, String>,
        JpaSpecificationExecutor<SystemUserDo> {

    Optional<SystemUserDo> findOneByUserCode(String userCode);

    boolean existsByUserCode(String userCode);

    boolean existsByBindIp(String bindIp);


    @Nonnull
    default Page<SystemUserDo> query(@Nonnull Pageable pageable, @Nonnull UserPageQry args) {
        return findAll((root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            String userCode = args.getUserCode();
            if (userCode != null && !userCode.isEmpty()) {
                predicates.add(builder.equal(root.get("userCode"), userCode));
            }
            String userName = args.getUserName();
            if (userName != null && !userName.isEmpty()) {
                predicates.add(builder.like(root.get("userName"), "%" + userName + "%"));
            }
            UserType type = args.getType();
            if (type != null) {
                predicates.add(builder.equal(root.get("userType"), type));
            }
            String mobile = args.getMobilePhone();
            if (mobile != null && !mobile.isEmpty()) {
                predicates.add(builder.equal(root.get("mobilePhone"), mobile));
            }
            if (args.getStatusCd() != null) {
                StatusEnum status = StatusEnum.codeOf(args.getStatusCd());
                predicates.add(builder.equal(root.get("status"), status));
            }
            return query.where(predicates.toArray(new Predicate[0])).getRestriction();
        }, pageable);
    }

}
