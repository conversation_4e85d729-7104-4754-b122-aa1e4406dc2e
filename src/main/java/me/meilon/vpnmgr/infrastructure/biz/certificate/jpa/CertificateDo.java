package me.meilon.vpnmgr.infrastructure.biz.certificate.jpa;

import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.app.args.CertificateAdd;
import me.meilon.vpnmgr.app.dto.CertificateDto;
import me.meilon.vpnmgr.infrastructure.db.jpa.NanoJpaIDGenerator;
import me.meilon.vpnmgr.infrastructure.utils.DesUtil;
import me.meilon.vpnmgr.infrastructure.utils.StrUtils;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedDate;

import java.time.LocalDateTime;

/**
 * 证书
 */
@Getter
@Setter
@Entity
@Table(name = CertificateDo.ENTITY_NAME,
        indexes = {
                @Index(name = CertificateDo.ENTITY_NAME+"_client_name_idx", columnList = "client_name"),
        })
@Comment("证书表")
public class CertificateDo {
    public static final String ENTITY_NAME = "certificate";

    @Id
    @GeneratedValue(generator = ENTITY_NAME)
    @GenericGenerator(name = ENTITY_NAME, type = NanoJpaIDGenerator.class)
    @Column(name = "id", nullable = false, length = 21)
    private String id;
    /**
     * 证书名
     */
    @Nonnull
    @Column(name = "client_name", nullable = false, length = 16)
    private String clientName;
    /**
     * 证书密码
     * (可选)
     */
    @Column(name = "password", length = 512)
    private String password;
    /**
     * 证书文件名
     */
    @Nonnull
    @Column(name = "file_name", nullable = false, length = 21)
    private String fileName;
    /**
     * 证书状态
     * R: 注销
     * V: 有效
     */
    @Nonnull
    @Column(name = "status_cd", nullable = false, length = 2)
    private String statusCd;
    /**
     * 证书创建时间
     */
    @CreatedDate
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    /**
     * 有效期(单位天)
     */
    @Nonnull
    @Column(name = "expiration", nullable = false, length = 11)
    private Integer expiration;
    /**
     * 备注
     */
    @Column(name = "remark", length = 256)
    private String remark;

    public static CertificateDo create(@Nonnull CertificateAdd args){
        CertificateDo certificateDo = new CertificateDo();
        certificateDo.setClientName(args.getUserName());
        String pwd = args.getPasswd();
        if (StrUtils.isBlank(pwd)){
            certificateDo.setPassword(DesUtil.encrypt(pwd));
        }
        certificateDo.setRemark(args.getRemark());
        certificateDo.setExpiration(args.getExpiration());
        return certificateDo;
    }

    @Nonnull
    public CertificateDto toDto() {
        CertificateDto dto = new CertificateDto();
        dto.setId(this.getId());
        dto.setClientName(this.getClientName());
        dto.setFileName(this.getFileName());
        dto.setStatusCd(this.getStatusCd());
        dto.setCreateTime(this.getCreateTime());
        dto.setExpiration(this.getExpiration());
        dto.setRemark(this.getRemark());
        return dto;

    }
}
