package me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa;


import org.springframework.data.domain.Limit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;


public interface IpLeasesRepository extends JpaRepository<IpLeasesDo, Long>,
        JpaSpecificationExecutor<IpLeasesDo> {


    Optional<IpLeasesDo> findOneByUserCode(String userCode);

    List<IpLeasesDo> findAllByRecycled(Boolean recycled, Limit limit);

    Optional<IpLeasesDo> findOneByRecycled(Boolean recycled, Limit limit);
}
