package me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa;


import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface OperationLogRepository extends JpaRepository<OperationLogDo, String> {

    Optional<OperationLogDo> findOneByUserCode(String userCode);

    boolean existsByUserCode(String userCode);

    void deleteAllByUserCode(String userCode);
}
