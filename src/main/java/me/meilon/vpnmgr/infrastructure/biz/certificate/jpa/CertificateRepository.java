package me.meilon.vpnmgr.infrastructure.biz.certificate.jpa;


import jakarta.annotation.Nonnull;
import jakarta.persistence.criteria.Predicate;
import me.meilon.vpnmgr.app.args.CerPageQry;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


public interface CertificateRepository extends JpaRepository<CertificateDo, String>,
        JpaSpecificationExecutor<CertificateDo> {

    Optional<CertificateDo> findOneByClientName(@Nonnull String userName);

    boolean existsByClientName(@Nonnull String clientName);

    @Nonnull
    default CertificateDo findRequiredById(String id) {
        return findById(id)
                .orElseThrow(() -> new IllegalArgumentException("证书不存在"));
    }


    default Page<CertificateDo> query(Pageable pageable, CerPageQry args){
        return findAll((root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            String clientName = args.getCn();
            if (clientName != null && !clientName.isEmpty()) {
                predicates.add(builder.equal(root.get("client_name"), clientName));
            }
            String statusCd = args.getStatusCd();
            if (statusCd != null && !statusCd.isEmpty()) {
                predicates.add(builder.equal(root.get("status_cd"), statusCd));
            }
            LocalDateTime createTime = args.getCreateTime();
            if (createTime != null ) {
                predicates.add(builder.equal(root.get("create_time"), createTime));
            }
            return query.where(predicates.toArray(new Predicate[0])).getRestriction();
        }, pageable);
    }
}
