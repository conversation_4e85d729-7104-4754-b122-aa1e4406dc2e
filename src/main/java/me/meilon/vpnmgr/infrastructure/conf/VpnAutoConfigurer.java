package me.meilon.vpnmgr.infrastructure.conf;

import com.warrenstrange.googleauth.GoogleAuthenticator;
import com.warrenstrange.googleauth.GoogleAuthenticatorConfig;
import lombok.RequiredArgsConstructor;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.IpLeasesDo;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.IpLeasesRepository;
import me.meilon.vpnmgr.infrastructure.management.OVMClientFactory;
import me.meilon.vpnmgr.infrastructure.management.OpenvpnManagementClient;
import me.meilon.vpnmgr.infrastructure.utils.ip.IPAddressPool;
import me.meilon.vpnmgr.infrastructure.utils.ip.IpAddress;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/28 11:05
 */
@Configuration
@EnableWebSocket
@EnableScheduling
@EnableJpaAuditing
@RequiredArgsConstructor
@EnableTransactionManagement
@EntityScan(basePackages = {"me.meilon.vpnmgr.infrastructure.biz"})
@EnableJpaRepositories(basePackages = {"me.meilon.vpnmgr.infrastructure.biz"})
@EnableConfigurationProperties(OpenvpnProperty.class)
public class VpnAutoConfigurer {

    private final OpenvpnProperty openvpnProperty;


    @Bean
    OpenVpnManagementProperty openVpnManagementProperty(){
        return openvpnProperty.getManagement();
    }

    @Bean
    EasyRsaProperty easyRsaProperty(){
        return openvpnProperty.getEasyRsa();
    }


    @Bean
    GoogleAuthenticator authenticator(){
        OVMClientFactory.setProperty(openVpnManagementProperty());
        return new GoogleAuthenticator(new GoogleAuthenticatorConfig
                .GoogleAuthenticatorConfigBuilder()
                .setSecretBits(320)
                .build()
        );
    }

    @Bean
    OpenvpnManagementClient openvpnManagementClient() throws IOException {
        OVMClientFactory.setProperty(openVpnManagementProperty());
        return OVMClientFactory.create(null);
    }

    @Bean
    public IPAddressPool ipAddressPool(@NotNull IpLeasesRepository leasesRepository){
        // 初始化IP地址池
        IpPoolProperty poolProperty = openvpnProperty.getIpPool();
        IpAddress min = new IpAddress(poolProperty.getStartIp());
        IpAddress max = new IpAddress(poolProperty.getEndIp());
        IPAddressPool ipAddressPool = new IPAddressPool(min, max);
        // 查询ip租约表
        List<IpLeasesDo> leasesDos = leasesRepository.findAll();
        if (leasesDos.isEmpty()){
            return ipAddressPool;
        }
        // 缓存已回收/已使用的IP地址
        for (IpLeasesDo leasesDo : leasesDos) {
            if (leasesDo.getRecycled()){
                ipAddressPool.markAsRecycled(leasesDo.getId());
            }
            else {
                ipAddressPool.markAsUsed(leasesDo.getId());
            }
        }
        return ipAddressPool;
    }


}
