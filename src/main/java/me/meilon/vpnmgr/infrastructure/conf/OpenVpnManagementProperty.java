package me.meilon.vpnmgr.infrastructure.conf;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OpenVpnManagementProperty {

    /**
     * openvpn 管理接口的类型
     * tcp 或者 unix
     */
    private Type type;

    /**
     * openvpn 管理接口的地址
     * type 为 tcp 时，必填
     */
    private String host = "localhost";
    /**
     * openvpn 管理接口的端口
     * type 为 tcp 时，必填
     */
    private Integer port;

    /**
     * openvpn 管理接口的 unix sock 文件路径
     * type 为 unix 时，必填
     */
    private String sockFile;

    /**
     * 是否启动读取线程
     * 默认为 true
     */
    private Boolean readThreadDaemon = true;


    public enum Type {
        /**
         * 不使用管理接口
         */
        NONE,
        /**
         * tcp
         */
        TCP,
        /**
         * unix socket
         */
        UNIX
    }

}
