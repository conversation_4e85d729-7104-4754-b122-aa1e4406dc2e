package me.meilon.vpnmgr.infrastructure.conf;

import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;


@Slf4j
@Configuration
public class CustomSchedulingConfigure implements SchedulingConfigurer {
  private final ThreadPoolTaskScheduler threadPoolTaskScheduler;

  public CustomSchedulingConfigure() {
    this.threadPoolTaskScheduler = new ThreadPoolTaskScheduler();
    // 线程池大小, 根据程序中任务数自行配置
    this.threadPoolTaskScheduler.setPoolSize(16);
    // 线程销毁前等待新任务的时间
    this.threadPoolTaskScheduler.setAwaitTerminationSeconds(20);
    // 线程池关闭前等待所有任务执行完成
    this.threadPoolTaskScheduler.setWaitForTasksToCompleteOnShutdown(true);
    this.threadPoolTaskScheduler.setThreadNamePrefix("@schedule-");
    this.threadPoolTaskScheduler.setErrorHandler(throwable -> log.error("Unexpected error occurred in scheduled task.", throwable));
    this.threadPoolTaskScheduler.initialize();
  }


  @Override
  public void configureTasks(@Nonnull ScheduledTaskRegistrar taskRegistrar) {
    taskRegistrar.setTaskScheduler(this.threadPoolTaskScheduler);
  }
}
