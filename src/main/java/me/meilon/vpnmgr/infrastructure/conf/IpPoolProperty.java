package me.meilon.vpnmgr.infrastructure.conf;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class IpPoolProperty {

    /**
     * 起始IP地址
     */
    private String startIp;

    /**
     * 结束IP地址
     */
    private String endIp;

    /**
     * 租约时间（单位：天）
     */
    private int leaseTime;

    /**
     * 续约阈值（单位：天）
     */
    private int renewThreshold;


}
