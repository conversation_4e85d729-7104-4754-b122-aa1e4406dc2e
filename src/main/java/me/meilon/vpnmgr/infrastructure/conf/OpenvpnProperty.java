package me.meilon.vpnmgr.infrastructure.conf;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

@Getter
@Setter
@ConfigurationProperties("openvpn")
public class OpenvpnProperty {

    /**
     * service 服务名
     */
    private String serviceName;
    /**
     * openvpn的基础目录
     */
    @Nonnull
    private String baseDir;
    /**
     * 服务端目录
     */
    @Nonnull
    private String serverDir;
    /**
     * 客户端证书模板路径
     */
    @Nonnull
    private String clientTemplatePath;
    /**
     * 生成的客户端文件存放目录
     */
    @Nonnull
    private String clientSaveDir;
    /**
     * 服务端配置文件路径
     * 不填则默认为 {serverDir} + /server.conf
     */
    @Nonnull
    private String serverConfPath;
    /**
     * 客户端IP配置目录
     */
    @Nonnull
    private String clientConfigDir;
    /**
     * 子网 IP 地址段
     * 默认 10.8.0
     */
    @Nonnull
    private String subNetwork = "10.8.0";
    /**
     * 证书管理配置
     */
    @NestedConfigurationProperty
    private EasyRsaProperty easyRsa;
    /**
     * 管理接口配置
     */
    @NestedConfigurationProperty
    private OpenVpnManagementProperty management;
    /**
     * ip池配置
     */
    @NestedConfigurationProperty
    private IpPoolProperty ipPool;

}
