package me.meilon.vpnmgr.infrastructure.utils.ip;

import java.util.*;

public class IPAddressPool {

  //最小地址
  private final IpAddress minAddress;
  // 最大地址
  private final IpAddress maxAddress;
  // 已使用的地址
  private final PriorityQueue<Long> usedAddresses = new PriorityQueue<>(Collections.reverseOrder());
  // 已回收的地址
  private final Queue<IpAddress> recycledAddresses = new PriorityQueue<>();


  public IPAddressPool(IpAddress minAddress, IpAddress maxAddress) {
    this.minAddress = minAddress;
    this.maxAddress = maxAddress;
  }

  /**
   * 分配IP
   */
  public IpAddress allocateIP() {
    // 判断回收列表是否有可用地址
    // 有则直接返回
    if (!recycledAddresses.isEmpty()) {
      return recycledAddresses.poll();
    }
    IpAddress next = minAddress.next(maxAddress);
    while (next != null){
      next = next.next(maxAddress);
      if (next != null && !usedAddresses.contains(next.toLong())) {
        return next;
      }
    }
    return null;
    // 从已使用列表中获取最大IP地址
    // 如果没有已使用的地址, 则取 minAddress
//    IpAddress current = usedAddresses.isEmpty() ? minAddress : usedAddresses.peek();
//    return current.next(maxAddress);
  }

  public void markAsUsed(Long ip) {
    usedAddresses.add(ip);
  }

  public void markAsRecycled(Long ip) {
    recycledAddresses.add(new IpAddress(ip));
  }

}
