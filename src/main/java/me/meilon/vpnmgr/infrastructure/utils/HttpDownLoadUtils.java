package me.meilon.vpnmgr.infrastructure.utils;

import jakarta.annotation.Nonnull;
import jakarta.servlet.http.HttpServletResponse;

import java.io.*;
import java.nio.charset.StandardCharsets;

public class HttpDownLoadUtils {


    @SuppressWarnings("all")
    public static int copy(@Nonnull String fileDir, @Nonnull String fileName,
                           @Nonnull HttpServletResponse response){
        String filePath = FileUtil.unite(fileDir, fileName);
        File file = new File(filePath);
        return copy(file, response);
    }

    public static int copy(@Nonnull File file, @Nonnull HttpServletResponse response) {
        response.reset();
        // 设置相关格式
        response.setContentType("application/force-download;charset=utf-8");
        // 设置下载后的文件名以及header
        response.addHeader("Content-disposition",
                "attachment;fileName=" + new String(file.getName().getBytes(),
                        StandardCharsets.ISO_8859_1));

        int byteCount = 0;
        try (FileInputStream in = new FileInputStream(file)) {
            OutputStream out = response.getOutputStream();
            int bytesRead;
            for (byte[] buffer = new byte[1024]; (bytesRead = in.read(buffer)) != -1; byteCount += bytesRead) {
                out.write(buffer, 0, bytesRead);
            }

            out.flush();
            return byteCount;
        } catch (FileNotFoundException e) {
            response.setStatus(404);
            throw new RuntimeException("要下载的文件不存在", e);
        } catch (IOException e) {
            response.setStatus(500);
            throw new RuntimeException("文件下载失败", e);
        }

    }

}
