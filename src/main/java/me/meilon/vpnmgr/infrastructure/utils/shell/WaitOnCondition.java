package me.meilon.vpnmgr.infrastructure.utils.shell;

import jakarta.annotation.Nonnull;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

public class WaitOnCondition {

    private final Map<Condition, Consumer<String>> map;

    public WaitOnCondition() {
        this.map = new HashMap<>();
    }

    public void accept(@Nonnull String line){
        Condition tmp = null;
        for (Map.Entry<Condition, Consumer<String>> entry : map.entrySet()) {
            Condition condition = entry.getKey();
            if (condition.match(line)) {
                tmp = condition;
                entry.getValue().accept(line);
            }
        }
        if (tmp != null){
            map.remove(tmp);
        }
    }

    public void add(@Nonnull String key, @Nonnull Consumer<String> value){
        Condition condition = new Condition(key);
        map.put(condition, value);
    }

    public void add(@Nonnull String[] key, @Nonnull Consumer<String> value){
        for (String s : key) {
            add(s, value);
        }
    }




}
