package me.meilon.vpnmgr.infrastructure.utils.shell;


import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.exception.ShellExecException;

import java.io.BufferedWriter;
import java.io.Closeable;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.Charset;
import java.time.Duration;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * shell 交互指令运行时
 * <AUTHOR>
 */
@Slf4j
public class ProcessRunTime implements Closeable {

    private final Process process;
    private final BufferedWriter buff;
    private final ProcessOutput errOutput;
    private final ProcessOutput norOutput;
    private final WaitOnCondition waitOnCondition;
    private boolean isClosed = false;

    public ProcessRunTime(@Nonnull Process process, @Nonnull Charset charset) {
        this.process = process;
        this.waitOnCondition = new WaitOnCondition();
        this.buff = new BufferedWriter(new OutputStreamWriter(process.getOutputStream(), charset));
        this.norOutput = new ProcessOutput(ProcessOutput.Type.NORMAL, process, charset, waitOnCondition);
        this.errOutput = new ProcessOutput(ProcessOutput.Type.ERROR, process, charset, waitOnCondition);
        // 创建两个异步线程获取执行结果
        // TODO 后面改成线程池
        new Thread(this.norOutput).start();
        new Thread(this.errOutput).start();
    }

    public Process getProcess() {
        return process;
    }

    /**
     * 写入一个命令
     * @param cmd 要输入的命令
     * @throws IOException 写入流失败抛出异常
     * 注意: 命令执行失败不会抛出异常, 而是输出到 process#ErrorStream
     */
    public void write(@Nonnull String cmd) throws IOException {
        if (isClosed){
            throw new ShellExecException("不能执行命令: " + cmd + " IO已关闭", -1);
        }
        buff.write(cmd);
        buff.newLine();
        buff.flush();
    }

    /**
     * 达到条件时, 写入一个命令
     * @param waitOn 检测到执行结果满足 waitOn 时写入命令;
     *               支持在首尾加 "*" 通配
     * @param cmd cmd 要输入的命令
     */
    public void waitOnWrite(@Nonnull String waitOn, @Nonnull String cmd){
        waitOnCondition.add(waitOn, line->{
            try {
                this.write(cmd);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
    }


    /**
     * 等待执行结果
     * @param duration 等待的超时时间
     * @param waitOn 检测到执行结果满足 waitOn 时关闭 IO;
     *               支持在首尾加 "*" 通配
     * @return 执行结果
     * @throws IOException 关闭 process#OutputStream 失败时抛出异常
     */
    public ProcessResult waitResult(@Nonnull Duration duration, String... waitOn) throws IOException {
        int code = -1;
        try {
            if (waitOn != null && waitOn.length != 0){
                waitOnCondition.add(waitOn, line->{
                    try {
                        this.close();
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            if (process.waitFor(duration.getSeconds(), TimeUnit.SECONDS)) {
                this.close();
                code = process.exitValue();
            }
            List<String> nor = norOutput.getOutput();
            List<String> err = errOutput.getOutput();
            nor.addAll(err);
            return new ProcessResult(code, nor);
        } catch (InterruptedException e) {
            throw new ShellExecException(e, code);
        }
    }


    @Override
    public void close() throws IOException {
        if (!isClosed){
            log.info("Process run time close");
            buff.close();
            isClosed = true;
        }
    }

    public ProcessOutput getErrOutput() {
        return errOutput;
    }

    public ProcessOutput getNorOutput() {
        return norOutput;
    }
}
