package me.meilon.vpnmgr.infrastructure.utils.shell;

import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.infrastructure.exception.ShellExecException;

import java.util.List;

public class ProcessResult implements ListOut {

    private final int exitCode;
    private final List<String> executeOut;

    public ProcessResult(int exitCode, @Nonnull List<String> executeOut) {
        this.exitCode = exitCode;
        this.executeOut = executeOut;
    }

    public int getExitCode() {
        return exitCode;
    }

    @Nonnull
    public List<String> getExecuteOut() {
        return executeOut;
    }

    @Nonnull
    public String getOutString(){
        return toStr(executeOut);
    }

    public List<String> orElseGetOut(){
        if (exitCode != 0){
            return null;
        }
        return executeOut;
    }

    public String orElseGetOutString(){
        if (exitCode != 0){
            return null;
        }
        return getOutString();
    }

    public void orElseThrow() throws ShellExecException{
        orElseThrow(null);
    }

    public void orElseThrow(String msg) throws ShellExecException {
        if (exitCode != 0){
            String errMsg;
            if (msg != null){
                errMsg = msg + getOutString();
            }
            else {
                errMsg = getOutString();
            }
            throw new ShellExecException(errMsg, exitCode);
        }
    }
}
