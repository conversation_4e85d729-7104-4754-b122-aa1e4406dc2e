package me.meilon.vpnmgr.infrastructure.utils.shell;

import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.exception.ShellExecException;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class ShellRunTime {

    private final Runtime runtime;
    private final Charset cherset;

    private File workDirectory;


    public ShellRunTime(@Nonnull Runtime runtime) {
        this(runtime, Charset.defaultCharset());
    }

    private ShellRunTime(@Nonnull Runtime runtime, @Nonnull String cherset) {
        this.runtime = runtime;
        if (Charset.isSupported(cherset)) {
            this.cherset = Charset.forName(cherset);
        }
        else {
            this.cherset = Charset.defaultCharset();
        }
    }

    public ShellRunTime(@Nonnull Runtime runtime, @Nonnull Charset cherset) {
        this.runtime = runtime;
        this.cherset = cherset;
    }

    public static ShellRunTime getRuntime() {
        return new ShellRunTime(Runtime.getRuntime());
    }

    public static ShellRunTime getRuntime(@Nonnull String cherset) {
        return new ShellRunTime(Runtime.getRuntime(), cherset);
    }

    public static ShellRunTime getRuntime(@Nonnull Charset cherset) {
        return new ShellRunTime(Runtime.getRuntime(), cherset);
    }

    public void cd(@Nonnull String dir)  {
        this.workDirectory = new File(dir);
        if (!workDirectory.isDirectory()){
            throw new RuntimeException("目录 "+dir+" 不存在");
        }
        log.info("cd {}", dir);
    }

    /**
     * 执行命令并等待执行结果
     * @param print 是否打印执行的命令
     * @param command 要执行的命令
     * @return 执行结果
     */
    @Nonnull
    public ProcessResult execAndWaitResult(boolean print, @Nonnull String... command) {
        int exitCode = -1;
        try {
            Process process = exec(print, command);

            List<String> out = new ArrayList<>();
            ProcessOutput.output(out, process.getInputStream(), cherset);
            ProcessOutput.output(out, process.getErrorStream(), cherset);
            exitCode = process.waitFor();

            return new ProcessResult(exitCode, out);
        } catch (IOException | InterruptedException e) {
            throw new ShellExecException("执行命令 " + Arrays.toString(command) + " 失败", e, exitCode);
        }
    }

    /**
     * 执行命令并等待执行结果
     * @param command 要执行的命令
     * @return 执行结果
     */
    public ProcessResult execAndWaitResult(@Nonnull String... command) {
        return execAndWaitResult(true, command);
    }

    /**
     * 执行一个交互命令
     * @param command 要执行的命令
     * @return 交互指令运行时
     */
    public ProcessRunTime execInteractive(@Nonnull String... command) {
        try {
            Process process = exec(false, command);
            return new ProcessRunTime(process, cherset);
        } catch (IOException e) {
            throw new RuntimeException("执行命令 " + Arrays.toString(command) + " 失败", e);
        }
    }

    /**
     * 执行命令
     * @param print 是否打印执行的命令
     * @param command 要执行的命令
     */
    private Process exec(boolean print, @Nonnull String... command) throws IOException {
        if (command.length == 1){
            if (print) {
                log.info("exec command {}", command[0]);
            }
            return runtime.exec(command[0], null, workDirectory);
        }
        else {
            if (print) {
                log.info("exec command {}", Arrays.toString(command));
            }
            return runtime.exec(command, null, workDirectory);
        }
    }

}
