package me.meilon.vpnmgr.infrastructure.utils;


import jakarta.annotation.Nullable;

public class StrUtils {

    public static boolean isNotBlank(@Nullable CharSequence cs) {
        return !isBlank(cs);
    }

    public static boolean isBlank(@Nullable CharSequence cs) {
        int strLen;
        if (cs == null || (strLen = cs.length()) == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if (!Character.isWhitespace(cs.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    public static boolean notEqualsAndEmpty(Object var1, Object var2){
        if (var1 == null || var2 == null){
            return false;
        }
        else {
            return !var1.equals(var2);
        }
    }


    public static boolean notEqualsAndEmpty(String var1, String var2){
        boolean var1IsNull = var1 == null || var1.isEmpty();
        boolean var2IsNull = var2 == null || var2.isEmpty();
        if (var1IsNull || var2IsNull){
            return false;
        }
        else {
            return !var1.equals(var2);
        }
    }

    public static boolean notEquals(String var1, String var2){
        return !equals(var1,var2);
    }

    public static boolean equals(String var1, String var2){
        boolean var1IsNull = var1 == null || var1.isEmpty();
        boolean var2IsNull = var2 == null || var2.isEmpty();
        if (var1IsNull && var2IsNull){
            return true;
        }
        if (!var1IsNull && !var2IsNull){
            return var1.equals(var2);
        }
        return false;
    }

    public static boolean equalsIgnoreCase(CharSequence cs1, CharSequence cs2) {
        if (cs1 == cs2) {
            return true;
        }
        if (cs1 == null || cs2 == null) {
            return false;
        }
        if (cs1.length() != cs2.length()) {
            return false;
        }
        return CharSequenceUtils.regionMatches(cs1, true, 0, cs2, 0, cs1.length());
    }

    public static boolean notEqualsIgnoreCase(CharSequence cs1, CharSequence cs2) {
        return !equalsIgnoreCase(cs1,cs2);
    }

}
