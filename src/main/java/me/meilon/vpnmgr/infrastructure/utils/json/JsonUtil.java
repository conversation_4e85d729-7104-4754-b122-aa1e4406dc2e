package me.meilon.vpnmgr.infrastructure.utils.json;

import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.validation.constraints.NotNull;

import java.util.List;
import java.util.Map;
import java.util.Set;


public class JsonUtil {
  /** 完整的时间格式 */
  public static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss.SSS";
  /** 日期格式 */
  public static final String DATE_PATTERN = "yyyy-MM-dd";
  /** 时间格式 */
  public static final String TIME_PATTERN = "HH:mm:ss.SSS";



  @NotNull
  public static <T> String toJsonString(@NotNull T value) {
    return JacksonUtil.toJsonString(value);
  }

  @NotNull
  public static <T> String toPrettyJsonString(@NotNull T value) {
    return JacksonUtil.toPrettyJsonString(value);
  }

  @NotNull
  public static <T> String toJsonStringIgnoreNull(@NotNull T value) {
    return JacksonUtil.toJsonStringIgnoreNull(value);
  }

  @NotNull
  public static <T> String toPrettyJsonStringIgnoreNull(@NotNull T value) {
    return JacksonUtil.toPrettyJsonStringIgnoreNull(value);
  }

  @NotNull
  public static <T> T parse(@NotNull String jsonString, @NotNull Class<T> clazz) {
    return JacksonUtil.parse(jsonString, clazz);
  }

  @NotNull
  public static <T> T parse(@NotNull String jsonString,
                            @NotNull Class<?> parametrized,
                            @NotNull Class<?> parameterClass) {
    return JacksonUtil.parse(jsonString, parametrized, parameterClass);
  }

  @NotNull
  public static <T> T parse(@NotNull String jsonString,
                            @NotNull Class<?> parametrized,
                            @NotNull Class<?> parameterClass1,
                            @NotNull Class<?> parameterClass2) {
    return JacksonUtil.parse(jsonString, parametrized, parameterClass1, parameterClass2);
  }

  @NotNull
  public static <T> T parse(@NotNull String jsonString, @NotNull TypeReference<T> type) {
    return JacksonUtil.parse(jsonString, type);
  }

  @NotNull
  public static <T> List<T> parseList(@NotNull String jsonString, @NotNull Class<T> clazz) {
    return JacksonUtil.parseList(jsonString, clazz);
  }

  @NotNull
  public static <T> Set<T> parseSet(@NotNull String jsonString, @NotNull Class<T> clazz) {
    return JacksonUtil.parseSet(jsonString, clazz);
  }

  @NotNull
  public static <K, V> Map<K, V> parseMap(@NotNull String jsonString,
                                          @NotNull Class<K> keyClass,
                                          @NotNull Class<V> valueClass) {
    return JacksonUtil.parseMap(jsonString, keyClass, valueClass);
  }

  private JsonUtil() {
  }
}
