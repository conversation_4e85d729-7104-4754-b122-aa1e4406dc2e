package me.meilon.vpnmgr.infrastructure.utils.json;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.type.MapType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import jakarta.validation.constraints.NotNull;

import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;


@SuppressWarnings({"unused", "SameParameterValue"})
public final class JacksonUtil {
    private JacksonUtil() {
    }

    public static final SimpleModule JAVA_TIME_MODULE = new JavaTimeModule();
    public static final ObjectMapper MAPPER = new ObjectMapper();
    public static final ObjectMapper IGNORE_NULL_MAPPER = new ObjectMapper();

    static {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(JsonUtil.DATE_TIME_PATTERN);
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(JsonUtil.DATE_PATTERN);
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(JsonUtil.TIME_PATTERN);
        JAVA_TIME_MODULE
                .addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter))
                .addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateTimeFormatter))
                .addSerializer(LocalDate.class, new LocalDateSerializer(dateFormatter))
                .addDeserializer(LocalDate.class, new LocalDateDeserializer(dateFormatter))
                .addSerializer(LocalTime.class, new LocalTimeSerializer(timeFormatter))
                .addDeserializer(LocalTime.class, new LocalTimeDeserializer(timeFormatter));
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .registerModule(JAVA_TIME_MODULE)
                .findAndRegisterModules();
        IGNORE_NULL_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .setSerializationInclusion(JsonInclude.Include.NON_NULL)
                .registerModule(JAVA_TIME_MODULE)
                .findAndRegisterModules();
    }


    @NotNull
    static <T> String toJsonString(@NotNull T value) {
        return toJsonString(value, false, false);
    }


    @NotNull
    static <T> String toPrettyJsonString(@NotNull T value) {
        return toJsonString(value, false, true);
    }

    @NotNull
    static <T> String toJsonStringIgnoreNull(@NotNull T value) {
        return toJsonString(value, true, false);
    }

    @NotNull
    static <T> String toPrettyJsonStringIgnoreNull(@NotNull T value) {
        return toJsonString(value, true, true);
    }

    @NotNull
    static <T> String toJsonString(@NotNull T value, boolean ignoreNull, boolean pretty) {
        ObjectMapper mapper = MAPPER;
        if (ignoreNull) {
            mapper = IGNORE_NULL_MAPPER;
        }
        try {
            if (pretty) {
                return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(value);
            } else {
                return mapper.writeValueAsString(value);
            }
        } catch (JsonProcessingException e) {
            throw new JsonFormatException(e);
        }
    }


    @SuppressWarnings({"unused", "SameParameterValue"})
    @NotNull
    static <T> T parse(@NotNull String jsonString, @NotNull Class<T> clazz) {
        try {
            return IGNORE_NULL_MAPPER.readValue(jsonString, clazz);
        } catch (IOException e) {
            throw new JsonParseException(e);
        }
    }

    @NotNull
    static <T> T parse(@NotNull String jsonString,
                       @NotNull Class<?> parametrized,
                       @NotNull Class<?> parameterClass) {
        JavaType javaType = IGNORE_NULL_MAPPER.getTypeFactory()
                .constructParametricType(parametrized, parameterClass);
        try {
            return IGNORE_NULL_MAPPER.readValue(jsonString, javaType);
        } catch (IOException e) {
            throw new JsonParseException(e);
        }
    }

    @NotNull
    static <T> T parse(@NotNull String jsonString,
                       @NotNull Class<?> parametrized,
                       @NotNull Class<?> parameterClass1,
                       @NotNull Class<?> parameterClass2) {
        TypeFactory typeFactory = IGNORE_NULL_MAPPER.getTypeFactory();
        JavaType javaType = typeFactory.constructParametricType(parameterClass1, parameterClass2);
        JavaType type = typeFactory.constructParametricType(parametrized, javaType);
        try {
            return IGNORE_NULL_MAPPER.readValue(jsonString, type);
        } catch (IOException e) {
            throw new JsonParseException(e);
        }
    }

    @NotNull
    static <T> T parse(@NotNull String jsonString,
                       @NotNull Class<?> parametrized,
                       @NotNull Class<?> parameterClass1,
                       @NotNull Class<?> parameterClass2,
                       @NotNull Class<?> parameterClass3) {
        TypeFactory typeFactory = IGNORE_NULL_MAPPER.getTypeFactory();
        JavaType javaType = typeFactory.constructParametricType(parameterClass2, parameterClass3);
        JavaType type = typeFactory.constructParametricType(parameterClass1, javaType);
        JavaType finalType = typeFactory.constructParametricType(parametrized, type);
        try {
            return IGNORE_NULL_MAPPER.readValue(jsonString, finalType);
        } catch (IOException e) {
            throw new JsonParseException(e);
        }
    }

    @NotNull
    public static <T> T parse(@NotNull String jsonString, JavaType javaType) {
        try {
            return IGNORE_NULL_MAPPER.readValue(jsonString, javaType);
        } catch (IOException e) {
            throw new JsonParseException(e);
        }
    }

    @NotNull
    static <T> T parse(@NotNull String jsonString, @NotNull TypeReference<T> type) {
        try {
            return IGNORE_NULL_MAPPER.readValue(jsonString, type);
        } catch (IOException e) {
            throw new JsonParseException(e);
        }
    }

    @NotNull
    static <T> List<T> parseList(@NotNull String jsonString, @NotNull Class<T> clazz) {
        CollectionType type = IGNORE_NULL_MAPPER.getTypeFactory()
                .constructCollectionType(ArrayList.class, clazz);
        return parse(jsonString, type);
    }

    @NotNull
    @SuppressWarnings("rawtypes")
    static <T> List<T> parseList(@NotNull String jsonString,
                                 @NotNull Class<? extends List> listClass,
                                 @NotNull Class<T> elementClass) {
        CollectionType type = IGNORE_NULL_MAPPER.getTypeFactory()
                .constructCollectionType(listClass, elementClass);
        return parse(jsonString, type);
    }

    @NotNull
    static <T> Set<T> parseSet(@NotNull String jsonString, @NotNull Class<T> clazz) {
        CollectionType type = IGNORE_NULL_MAPPER.getTypeFactory()
                .constructCollectionType(LinkedHashSet.class, clazz);
        return parse(jsonString, type);
    }

    @NotNull
    @SuppressWarnings("rawtypes")
    static <T> Set<T> parseSet(@NotNull String jsonString,
                               @NotNull Class<? extends Set> listClass,
                               @NotNull Class<T> elementClass) {
        CollectionType type = IGNORE_NULL_MAPPER.getTypeFactory()
                .constructCollectionType(listClass, elementClass);
        return parse(jsonString, type);
    }

    @NotNull
    static <K, V> Map<K, V> parseMap(@NotNull String jsonString,
                                     @NotNull Class<K> keyClass,
                                     @NotNull Class<V> valueClass) {
        return parseMap(jsonString, LinkedHashMap.class, keyClass, valueClass);
    }

    @NotNull
    @SuppressWarnings("rawtypes")
    static <K, V> Map<K, V> parseMap(@NotNull String jsonString,
                                     @NotNull Class<? extends Map> mapClass,
                                     @NotNull Class<K> keyClass,
                                     @NotNull Class<V> valueClass) {
        MapType type = IGNORE_NULL_MAPPER.getTypeFactory()
                .constructMapType(mapClass, keyClass, valueClass);
        return parse(jsonString, type);
    }

    @NotNull
    public static JsonNode readTree(@NotNull String jsonString) {
        try {
            return IGNORE_NULL_MAPPER.readTree(jsonString);
        } catch (IOException e) {
            throw new JsonParseException(e);
        }
    }

    @NotNull
    public static JavaType constructJavaType(@NotNull Type type) {
        if (type instanceof ParameterizedType parameterizedType) {
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            Class<?> rowClass = (Class<?>) parameterizedType.getRawType();
            JavaType[] javaTypes = new JavaType[actualTypeArguments.length];
            for (int i = 0; i < actualTypeArguments.length; i++) {
                javaTypes[i] = constructJavaType(actualTypeArguments[i]);
            }
            return TypeFactory.defaultInstance().constructParametricType(rowClass, javaTypes);
        } else {
            Class<?> cla = (Class<?>) type;
            return TypeFactory.defaultInstance().constructParametricType(cla, new JavaType[0]);
        }
    }

}
