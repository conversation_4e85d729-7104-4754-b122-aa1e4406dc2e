package me.meilon.vpnmgr.infrastructure.utils.shell;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

/**
 * shell 命令执行结果对象
 * <AUTHOR>
 */
public class ProcessOutput implements Runnable {

    private final Type type;
    private final List<String> output = new ArrayList<>();
    private final Process process;
    private final Charset charset;
    private final WaitOnCondition waitOnCondition;


    public ProcessOutput(@Nonnull Type type, @Nonnull Process process) {
        this(type, process, Charset.defaultCharset(), null);
    }

    public ProcessOutput(@Nonnull Type type, @Nonnull Process process, @Nonnull Charset charset) {
        this(type, process, charset, null);
    }

    public ProcessOutput(@Nonnull Type type, @Nonnull Process process,
                         @Nonnull Charset charset, @Nullable WaitOnCondition waitOnCondition) {
        this.type = type;
        this.process = process;
        this.charset = charset;
        this.waitOnCondition = waitOnCondition;
    }

    @Nonnull
    public Type getType() {
        return this.type;
    }

    @Nonnull
    public List<String> getOutput() {
        return this.output;
    }

    @Nonnull
    private InputStream getInputStream(){
        return this.type == Type.ERROR ? this.process.getErrorStream() : this.process.getInputStream();
        // return switch (this.type) {
        //     case ERROR -> this.process.getInputStream();
        //     default -> this.process.getErrorStream();
        // };
    }

    @Override
    public void run() {
        output(this.output, getInputStream(), this.charset, this.waitOnCondition);
    }


    public static void output(@Nonnull List<String> out, @Nonnull InputStream in){
        output(out, in, Charset.defaultCharset(), null);
    }

    public static void output(@Nonnull List<String> out, @Nonnull InputStream in, @Nonnull Charset charset){
        output(out, in, charset, null);
    }

    /**
     * 将执行结果存到 List 中
     * @param out 要输出结果的 List
     * @param in 获取执行结果的流
     * @param charset 指定字符集
     * @param waitOnCondition 对于交互型命令根据条件回调
     */
    public static void output(@Nonnull List<String> out, @Nonnull InputStream in,
                              @Nonnull Charset charset, WaitOnCondition waitOnCondition){
        try (InputStreamReader reader = new InputStreamReader(in, charset);
             BufferedReader buff = new BufferedReader(reader))
        {
            String line;
            while ((line = buff.readLine()) != null) {
                out.add(line);
                if (waitOnCondition != null){
                    waitOnCondition.accept(line);
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    public enum Type {
        /**
         * 普通流输出
         */
        NORMAL,
        /**
         * 异常流输出
         */
        ERROR

    }
}
