package me.meilon.vpnmgr.infrastructure.utils.shell;

import jakarta.annotation.Nonnull;

import java.util.Objects;

public class Condition {

    private final static String FUZZY = "*";
    private final Rules rule;
    private final String key;

    public Condition(@Nonnull String key) {
        boolean start = key.startsWith(FUZZY);
        boolean end = key.endsWith(FUZZY);
        int length = key.length();
        if (start && end){
            rule = Rules.FUZZY_MATCH;
            this.key = key.substring(1, length - 1);
        }
        else if (start) {
            rule = Rules.HEAD_FUZZY_MATCH;
            this.key = key.substring(1);
        }
        else if (end) {
            rule = Rules.TAIL_FUZZY_MATCH;
            this.key = key.substring(0, length - 1);
        }
        else {
            rule = Rules.EXACT_MATCH;
            this.key = key;
        }
    }

    public boolean match(String line){
        if (line == null){
            return false;
        }
        String var = line.trim();
        return switch (this.rule) {
            case FUZZY_MATCH -> var.contains(key);
            case HEAD_FUZZY_MATCH -> var.endsWith(key);
            case TAIL_FUZZY_MATCH -> var.startsWith(key);
            default -> var.equals(key);
        };
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o instanceof Condition condition){
            return rule == condition.rule && Objects.equals(key, condition.key);
        }
        return false;
    }

    @Override
    public int hashCode() {
        return Objects.hash(rule, key);
    }


    enum Rules{

        // 头部模糊匹配
        HEAD_FUZZY_MATCH
        ,
        // 尾部模糊匹配
        TAIL_FUZZY_MATCH
        ,
        // 模糊匹配
        FUZZY_MATCH
        ,
        // 精确匹配
        EXACT_MATCH
    }
}
