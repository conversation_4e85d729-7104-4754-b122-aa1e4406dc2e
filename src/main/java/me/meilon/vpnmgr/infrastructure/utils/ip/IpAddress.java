package me.meilon.vpnmgr.infrastructure.utils.ip;

import org.jetbrains.annotations.NotNull;

public class IpAddress implements Comparable<IpAddress> {

  // 使用byte数组存储IP地址
  private final byte[] ipBytes;

  // 允许被分配的主机的最小ip地址, 表示 *******
  private static final IpAddress MIN = new IpAddress(16777217L);
  // 允许被分配的主机的最大ip地址, 表示 ***************
  private static final IpAddress MAX = new IpAddress(3758096382L);


  /**
   * 构造函数，接收IP字符串
   *
   * @param ipStr 格式为"***********"的IP字符串
   */
  public IpAddress(String ipStr) {
    this.ipBytes = parseIpString(ipStr);
  }

  /**
   * 构造函数, 接收 long 型数字
   *
   * @param ip IP地址的long表示
   */
  public IpAddress(long ip) {
    this.ipBytes = parseIpLong(ip);
  }

  private IpAddress(byte[] ipBytes) {
    this.ipBytes = ipBytes;
  }

  /**
   * 拷贝IP地址对象
   *
   * @return 新的IP地址对象
   */
  public IpAddress copy() {
    byte[] copyBytes = new byte[4];
    System.arraycopy(ipBytes, 0, copyBytes, 0, 4);
    return new IpAddress(copyBytes);
  }

  /**
   * 获取下一个IP地址
   *
   * @return 下一个IP的字符串表示
   */
  public IpAddress next() {
    return next(null, null);
  }

  public IpAddress next(IpAddress maxIp){
    return next(null, maxIp);
  }

  /**
   * 获取下一个IP地址
   *
   * @return 下一个IP的字符串表示
   */
  public IpAddress next(IpAddress minIp, IpAddress maxIp) {
    // 创建一个新的IPUtil对象，复制当前IP
    IpAddress nextIp = this.copy();

    // 增加IP地址
    boolean carry = true;
    for (int i = 3; i >= 0 && carry; i--) {
      if ((nextIp.ipBytes[i] & 0xFF) == 255) {
        nextIp.ipBytes[i] = 0;
      } else {
        nextIp.ipBytes[i]++;
        carry = false;
      }
    }

    byte[] maxIpBytes = maxIp == null ? MAX.ipBytes : maxIp.ipBytes;
    // 检查是否超出范围
    if (maxIp != null && compare(nextIp.ipBytes, maxIpBytes) > 0) {
      // 如果超过最大IP，重置为最小IP
      if (minIp != null) {
        return new IpAddress(minIp.toLong());
      } else {
        return null;
      }
    }
    return nextIp;
  }

  /**
   * 将当前IP转换为long型数字
   *
   * @return IP的long表示
   */
  public long toLong() {
    return toLong(ipBytes);
  }

  /**
   * 将IP字符串解析为byte数组
   *
   * @param ipStr IP字符串
   * @return byte数组
   */
  private byte[] parseIpString(String ipStr) {
    String[] parts = ipStr.split("\\.");
    if (parts.length != 4) {
      throw new IllegalArgumentException("无效的IP地址格式: " + ipStr);
    }
    byte[] bytes = new byte[4];

    for (int i = 0; i < 4; i++) {
      bytes[i] = (byte) Integer.parseInt(parts[i]);

    }
    for (String part : parts) {
      int value = Integer.parseInt(part);
      if (value < 0 || value > 255) {
        throw new IllegalArgumentException("无效的IP地址格式: " + ipStr);
      }
    }
    return bytes;
  }

  /**
   * 将long型IP转换为byte数组
   *
   * @param ipLong long型IP地址
   * @return byte数组
   */
  private byte[] parseIpLong(long ipLong) {
    if (ipLong < 0L || ipLong > 4294967295L) {
      // 0 和 4294967295 是IP地址可以表示的范围, 分别表示0.0.0.0和***************
      throw new IllegalArgumentException("无效的IP地址值: " + ipLong);
    }
    byte[] bytes = new byte[4];
    bytes[0] = (byte) ((ipLong >> 24) & 0xFF);
    bytes[1] = (byte) ((ipLong >> 16) & 0xFF);
    bytes[2] = (byte) ((ipLong >> 8) & 0xFF);
    bytes[3] = (byte) (ipLong & 0xFF);
    return bytes;
  }

  /**
   * 比较两个IP地址的大小
   *
   * @return 负数表示小于，0表示相等，正数表示大于
   */
  private int compare(byte[] ip1, byte[] ip2) {
    for (int i = 0; i < 4; i++) {
      int b1 = ip1[i] & 0xFF;
      int b2 = ip2[i] & 0xFF;
      if (b1 != b2) {
        return b1 - b2;
      }
    }
    return 0;
  }


  /**
   * 将指定byte数组的IP转换为long型数字
   */
  private long toLong(byte[] bytes) {
    long result = 0;
    for (int i = 0; i < 4; i++) {
      result = (result << 8) | (bytes[i] & 0xFF);
    }
    return result;
  }

  /**
   * 将当前IP转换为字符串表示
   *
   * @return 格式为"***********"的IP字符串
   */
  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < 4; i++) {
      sb.append(ipBytes[i] & 0xFF);
      if (i < 3) {
        sb.append(".");
      }
    }
    return sb.toString();
  }

  @Override
  public int compareTo(@NotNull IpAddress o) {
    return compare(this.ipBytes, o.ipBytes);
  }

//  public static void main(String[] args) {
//
//    Ip min = new Ip("********");
//    Ip max = new Ip("**********");
//    System.out.println(new Ip("**********").next(min, max));

//    System.out.println(new Ip("***************").toLong());
//  }
}
