package me.meilon.vpnmgr.infrastructure.utils;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;

public class NanoIdGenerator {

    public static final char[] ALPHABET =
            "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".toCharArray();


    public static String generate(){
        return NanoIdUtils.randomNanoId(com.aventrix.jnanoid.jnanoid.NanoIdUtils.DEFAULT_NUMBER_GENERATOR, ALPHABET, 21);
    }

}
