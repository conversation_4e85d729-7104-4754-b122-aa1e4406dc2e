package me.meilon.vpnmgr.infrastructure.utils;


import jakarta.annotation.Nonnull;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.time.*;
import java.util.Date;

public class DateUtil {

  /**
   * 计算两个日期的天数差
   */
  public static long daysDiff(@Nonnull LocalDate day1, @Nonnull LocalDate day2) {
    return day2.toEpochDay() - day1.toEpochDay();
  }

  public static long daysDiff(@Nonnull LocalDate day1) {
    return daysDiff(day1, LocalDate.now());
  }

  public static long daysDiff(@Nonnull LocalDateTime day1) {
    return daysDiff(day1, LocalDateTime.now());
  }

  public static long daysDiff(@Nonnull LocalDateTime day1, @Nonnull LocalDateTime day2) {
    return Duration.between(day1, day2).toDays();
  }

  public static String toFormat(Date timestamp) {
    return DateFormatUtils.format(timestamp, "yyyy-MM-dd HH:mm:ss");
  }

  /**
   * 将连接时间戳转换为 LocalDateTime
   */
  @Nonnull
  public static LocalDateTime toLocalDateTime(@Nonnull Long timeStamps) {
    Instant instant = Instant.ofEpochSecond(timeStamps);
    return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
  }

  public static long toTimestamp(@Nonnull LocalDateTime localDateTime) {
    return localDateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
  }

}
