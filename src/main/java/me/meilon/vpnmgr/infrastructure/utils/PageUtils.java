package me.meilon.vpnmgr.infrastructure.utils;

import me.meilon.vpnmgr.app.args.base.PageQry;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

public class PageUtils {

    public static Pageable of(PageQry args){
        int number = args.getPageNumber() < 0 ? 0:args.getPageNumber();
        int size = args.getPageSize() < 1 ? 1:args.getPageSize();

        return PageRequest.of(number, size);
    }

    public static Pageable of(PageQry args, Sort sort){
        int number = args.getPageNumber() < 0 ? 0:args.getPageNumber();
        int size = args.getPageSize() < 1 ? 1:args.getPageSize();

        return PageRequest.of(number, size, sort);
    }
}
