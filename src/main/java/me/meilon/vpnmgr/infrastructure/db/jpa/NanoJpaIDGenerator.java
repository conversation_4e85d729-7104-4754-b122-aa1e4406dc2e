package me.meilon.vpnmgr.infrastructure.db.jpa;

import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.infrastructure.utils.NanoIdGenerator;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.IdentifierGenerator;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class NanoJpaIDGenerator implements IdentifierGenerator {


    @Override
    public Serializable generate(@Nonnull SharedSessionContractImplementor s, @Nonnull Object obj) {
        return NanoIdGenerator.generate();
    }
}
