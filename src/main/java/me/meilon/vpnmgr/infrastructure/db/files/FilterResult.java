package me.meilon.vpnmgr.infrastructure.db.files;

import jakarta.annotation.Nonnull;

import java.util.function.Function;

public class FilterResult {

    private boolean value;
    private Function<String, Boolean> after;

    public FilterResult(boolean value) {
        this.value = value;
    }

    public void set(boolean value){
        this.value = value;
    }

    public boolean get(){
        return value;
    }

    public void setAfter(@Nonnull Function<String, Boolean> after){
        this.after = after;
    }

    public void after(String line){
        if (after != null){
            value = after.apply(line);
        }
    }
}
