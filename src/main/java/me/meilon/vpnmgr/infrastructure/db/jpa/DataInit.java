package me.meilon.vpnmgr.infrastructure.db.jpa;

import lombok.RequiredArgsConstructor;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.AuthMode;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.LoginMode;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.enums.StatusEnum;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserDo;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserRepository;
import me.meilon.vpnmgr.infrastructure.utils.MD5;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class DataInit implements ApplicationRunner {

    private final SystemUserRepository userRepository;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        String userCode = "admin";
        Optional<SystemUserDo> optional = userRepository.findOneByUserCode(userCode);
        optional.ifPresent(userDo -> {
            userDo.setStatus(StatusEnum.ENABLE);
            userDo.setPwdErrCnt(0);
            userDo.setAuthMode(AuthMode.NONE);
            userDo.setLoginMode(LoginMode.PWD);
            userDo.setUserName("管理员");
            userRepository.save(userDo);
        });
        optional.orElseGet(()->{
            SystemUserDo insert = new SystemUserDo();
            insert.setUserCode(userCode);
            insert.setPassword(MD5.encode(userCode));
            insert.setStatus(StatusEnum.ENABLE);
            insert.setPwdErrCnt(0);
            insert.setStatusTime(LocalDateTime.now());
            insert.setAuthMode(AuthMode.NONE);
            insert.setLoginMode(LoginMode.PWD);
            insert.setUserName("管理员");
            return userRepository.save(insert);
        });

    }
}
