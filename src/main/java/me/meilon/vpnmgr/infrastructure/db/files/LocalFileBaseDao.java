package me.meilon.vpnmgr.infrastructure.db.files;

import jakarta.annotation.Nonnull;

import java.io.*;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.OpenOption;
import java.nio.file.Path;
import java.nio.file.spi.FileSystemProvider;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

public interface LocalFileBaseDao {


    default List<String> readByPath(@Nonnull Path path){
        return readByPath(path, null);
    }
    /**
     *
     * @param path 要读取的文件路径
     * @param filter 过滤器, 根据条件过滤掉不需要的行
     * @return 从文件中读取的结果
     */
    default List<String> readByPath(@Nonnull Path path, Function<String, FilterResult> filter){

        CharsetDecoder decoder = StandardCharsets.UTF_8.newDecoder();
        FileSystemProvider provider = path.getFileSystem().provider();

        try (InputStream inputStream = provider.newInputStream(path);
             Reader reader = new InputStreamReader(inputStream, decoder);
             BufferedReader buff = new BufferedReader(reader)) {
            // 读取文件内容放到 List 中
            // list 中每一个字段对应文件的一行内容
            List<String> result = new ArrayList<>();
            String line;
            while ((line = buff.readLine()) != null) {
                if (filter == null){
                    result.add(line);
                }
                else {
                    FilterResult filterResult = filter.apply(line);
                    if (filterResult.get()){
                        result.add(line);
                        filterResult.after(line);
                    }
                }
            }
            return result;
        }
        catch (IOException e) {
            throw new RuntimeException("读取文件 "+ path.toAbsolutePath() +" 失败", e);
        }
    }

    default List<String> readByLimit(Path path, @Nonnull String begin, @Nonnull String end){
        FilterResult fl = new FilterResult(false);
        fl.setAfter(line-> !line.contains(end));
        return readByPath(path, line ->{
            if (line.contains(begin)){
                fl.set(true);
            }
            return fl;
        });
    }

    default void save(@Nonnull Path path, @Nonnull List<String> lines, OpenOption... options){
        try {
            Files.write(path, lines, options);
        } catch (IOException e) {
            throw new RuntimeException("保存文件 " + path +" 失败", e);
        }
        File file = path.toFile();
        if (!file.isFile()){
            throw new RuntimeException("保存文件 "+ path +" 失败, 没有文件");
        }
    }

    default void save(@Nonnull Path path, @Nonnull String line, OpenOption... options){
        List<String> lines = Collections.singletonList(line);
        save(path, lines, options);
    }

    default void delete(@Nonnull Path path){
        if (path.toFile().isFile()) {
            try {
                Files.delete(path);
            } catch (IOException e) {
                throw new RuntimeException("删除文件 "+path+" 失败", e);
            }
        }
    }



}
