package me.meilon.vpnmgr.infrastructure.exception;


import jakarta.annotation.Nonnull;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * 将异常栈 转换成完整字符串
 *
 * <AUTHOR>
 */
public class ExceptionUtil {
    @Nonnull
    public static Throwable getRootCause(@Nonnull Throwable throwable) {
        Throwable cause = throwable.getCause();
        if (cause == null) {
            return throwable;
        }
        return getRootCause(cause);
    }

    @Nonnull
    public static String getStackMsg(Throwable throwable) {
        try (StringWriter stringWriter = new StringWriter();
             PrintWriter printWriter = new PrintWriter(stringWriter)) {
            throwable.printStackTrace(printWriter);
            return stringWriter.toString();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
