package me.meilon.vpnmgr.infrastructure.exception;

public class ShellExecException extends RuntimeException{

    private final int code;

    public ShellExecException(String message, int code) {
        super(message);
        this.code = code;
    }

    public ShellExecException(String message, Throwable cause, int code) {
        super(message, cause);
        this.code = code;
    }

    public ShellExecException(Throwable cause, int code) {
        super(cause);
        this.code = code;
    }

    public ShellExecException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace, int code) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
