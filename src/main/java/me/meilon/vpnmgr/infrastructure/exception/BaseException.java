package me.meilon.vpnmgr.infrastructure.exception;

import me.meilon.vpnmgr.app.dto.base.Result;

/**
 * 自定义基础异常类
 * 扩展了 RuntimeException 增加了 errorCode
 * 异常拦截后可直接转换为 {@link Result}
 *
 * <AUTHOR>
 */
public class BaseException extends RuntimeException {

    /**
     * 异常编码
     */
    private Integer errorCode = -1;

    public BaseException(String message) {
        super(message);
    }

    public BaseException(String message, Throwable cause) {
        super(message, cause);
    }

    public BaseException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public BaseException(int errorCode, Throwable cause) {
        super(cause);
        this.errorCode = errorCode;
    }

    /**
     * @param errorCode     异常编码
     * @param message       异常说明
     * @param cause         关联另一个异常
     */
    public BaseException(int errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * 获取异常编码
     *
     * @return int
     */
    public int getErrorCode() {
        return errorCode;
    }


}
