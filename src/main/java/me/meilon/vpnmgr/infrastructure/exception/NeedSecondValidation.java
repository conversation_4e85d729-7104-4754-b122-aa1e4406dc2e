package me.meilon.vpnmgr.infrastructure.exception;

import jakarta.annotation.Nonnull;

/**
 * 需要二次验证
 * Second verification required
 * need Second validation
 */
public class NeedSecondValidation extends RuntimeException{

  private final String type;

  public NeedSecondValidation(@Nonnull String type, String message) {
    super(message);
    this.type = type;
  }

  @Nonnull
  public String getType() {
    return type;
  }

}
