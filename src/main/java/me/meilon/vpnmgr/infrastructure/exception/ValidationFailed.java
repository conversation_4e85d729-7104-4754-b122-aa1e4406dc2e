package me.meilon.vpnmgr.infrastructure.exception;



/**
 * 校验失败抛出异常, 主要用于业务逻辑校验不通过, 区别程序执行错误
 * 注: 此类异常捕获后不会打印异常栈
 * <AUTHOR>
 */
public class ValidationFailed extends BaseException{

    public ValidationFailed(String message) {
        super(-1, message);
    }

    public ValidationFailed(String message, Throwable cause) {
        super(-1, message, cause);
    }

    public ValidationFailed(int errorCode, String message) {
        super(errorCode, message);
    }

    public ValidationFailed(int errorCode, Throwable cause) {
        super(errorCode, cause);
    }

    public ValidationFailed(int errorCode, String message, Throwable cause) {
        super(errorCode, message, cause);
    }
}
