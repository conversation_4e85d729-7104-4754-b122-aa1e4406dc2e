package me.meilon.vpnmgr.infrastructure.management.v3.parsers;

import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.infrastructure.management.v3.message.LoginMessage;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.Source;
import org.jetbrains.annotations.NotNull;

public class LoginInfoParser extends InfoParser {

    public static final String SUFFIX = "-- type 'help' for more info";


    @Override
    public boolean isMatch(@Nonnull String line) {
        return super.isMatch(line) && line.endsWith(SUFFIX);
    }

    @Override
    public LoginMessage from(@NotNull String line) {
        return new LoginMessage(line.substring(Source.INFO.headerLength()));
    }


}
