package me.meilon.vpnmgr.infrastructure.management.v3.message;

import me.meilon.vpnmgr.infrastructure.management.Message;

import java.util.List;

public class HelpMessage implements Message {

    private final List<String> line = new java.util.ArrayList<>();

    public void addLine(String line){
        this.line.add(line);
    }

    @Override
    public String toStr() {
        return line.stream()
                .filter(s -> !"END".equals(s))
                .map(s -> s + "\r\n")
                .reduce("", String::concat);
    }

    @Override
    public String toString() {
        return line.stream()
                .map(s -> s + "\r\n")
                .reduce("", String::concat);
    }
}
