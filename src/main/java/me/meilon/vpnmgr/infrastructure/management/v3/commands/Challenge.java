package me.meilon.vpnmgr.infrastructure.management.v3.commands;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import me.meilon.vpnmgr.infrastructure.utils.NanoIdGenerator;

import java.util.Base64;

@Getter
public class Challenge {

    private static final String PREFIX = "CRV1";
    private final String stateId;
    private final String userName;
    private final String challengeText;

    private Challenge(@Nonnull String stateId,
                     @Nonnull String userName,
                     @Nonnull String challengeText) {
        this.stateId = stateId;
        this.userName = userName;
        this.challengeText = challengeText;
    }

    public static Challenge of(@Nonnull String userName,
                               @Nonnull String challengeText) {
        String stateId = NanoIdGenerator.generate();
        return of(stateId, userName, challengeText);
    }

    public static Challenge of(@Nonnull String stateId,
                               @Nonnull String userName,
                               @Nonnull String challengeText) {
        return new Challenge(stateId, userName, challengeText);
    }

    @Override
    public String toString() {

        String userBase64 = Base64.getEncoder().encodeToString(userName.getBytes());
        return PREFIX + ":R,E:" + stateId + ":" + userBase64 + ":" + challengeText;
    }
}
