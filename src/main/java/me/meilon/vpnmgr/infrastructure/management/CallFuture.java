package me.meilon.vpnmgr.infrastructure.management;

import jakarta.annotation.Nonnull;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

/**
 * @param <T>
 * <AUTHOR>
 */
public class CallFuture<T extends Message> extends AbstractFuture<T> {

    private final Thread t;


    CallFuture() {
        t = Thread.currentThread();
    }

    protected void setMessage(Message message) {
        if (!isDone() && message != null) {
            super.setMessage(message);
            LockSupport.unpark(t);
        }
    }

    @Override
    public T get() {
        LockSupport.park(this);
        return super.get();
    }

    @Override
    public T get(long timeout, @Nonnull TimeUnit unit) {
        return get(unit.toMillis(timeout));
    }

    @Override
    public T get(long timeout) {
        long deadline = System.currentTimeMillis() + timeout;
        LockSupport.parkUntil(this, deadline);
        return super.get();
    }

}
