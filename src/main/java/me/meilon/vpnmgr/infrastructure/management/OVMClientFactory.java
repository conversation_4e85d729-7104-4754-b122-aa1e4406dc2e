package me.meilon.vpnmgr.infrastructure.management;

import jakarta.annotation.Nullable;
import me.meilon.vpnmgr.infrastructure.conf.OpenVpnManagementProperty;
import me.meilon.vpnmgr.infrastructure.management.session.OVMSession;
import me.meilon.vpnmgr.infrastructure.management.session.OVMSocket;
import me.meilon.vpnmgr.infrastructure.management.session.OVMUnix;

import java.io.IOException;
import java.nio.file.Path;

public class OVMClientFactory {

    private static volatile OpenvpnManagementClient client;
    private static OpenVpnManagementProperty PROPERTY;

    public static void setProperty(OpenVpnManagementProperty property){
        PROPERTY = property;
    }

    /**
     * 创建一个Open VPN客户端
     * 客户端是单例的, 如果已经创建过了, 则直接返回
     *
     * @param handler 前置消息处理器, 此消息处理器会在连接被创建之前添加到消息解析器中
     *                如果需要获取登录消息的时候, 可以在此处理器中获取
     *                注意: 连接创建成功以后, 再次调用此方法, 将不会再添加处理器
     */
    public static OpenvpnManagementClient create(@Nullable OVMTypeHandler<?> handler) throws IOException {
        if (client == null){
            synchronized (OVMClientFactory.class){
                if (client == null){
                    OVMSession session;
                    OpenVpnManagementProperty.Type type = PROPERTY.getType();
                    session = switch (type) {
                        case TCP -> new OVMSocket(PROPERTY.getHost(), PROPERTY.getPort());
                        case UNIX -> new OVMUnix(Path.of(PROPERTY.getSockFile()));
                        case NONE -> throw new IllegalArgumentException("未配置管理接口");
                    };
                    if (handler != null){
                        session.getParserRepository().addHandler(handler);
                    }
                    if (session.isClosed()){
                        session.connect();
                    }
                    client = new OpenvpnManagementClient(session, PROPERTY.getReadThreadDaemon());
                }
            }
        }
        return client;
    }


    public static void addHandler(OVMTypeHandler<?> handler) throws IOException {
        OpenvpnManagementClient client = create(null);
        client.addParseHandler(handler);
    }

}
