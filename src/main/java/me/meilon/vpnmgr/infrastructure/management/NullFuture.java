package me.meilon.vpnmgr.infrastructure.management;

import jakarta.annotation.Nonnull;

import java.util.concurrent.TimeUnit;

/**
 * 空的Future, 用于处理不需要等待结果的命令
 * @param <T>
 * <AUTHOR>
 */
public class NullFuture<T extends Message> extends AbstractFuture<T> {

    NullFuture() {
    }

    @Override
    public boolean isDone() {
        return true;
    }

    @Override
    public T get() {
        return super.get();
    }

    @Override
    public T get(long timeout, @Nonnull TimeUnit unit) {
        return super.get();
    }

    @Override
    public T get(long timeout) {
        return super.get();
    }

}
