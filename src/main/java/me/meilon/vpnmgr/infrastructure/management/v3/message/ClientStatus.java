package me.meilon.vpnmgr.infrastructure.management.v3.message;

import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.management.Message;

@Getter
@Setter
public class ClientStatus implements Message {
    public static final String CLIENT_LIST = "CLIENT_LIST";
    public static final String COMMON_NAME = "Common Name";
    public static final String REAL_ADDRESS = "Real Address";
    public static final String VIRTUAL_ADDRESS = "Virtual Address";
    public static final String VIRTUAL_IPV6_ADDRESS = "Virtual IPv6 Address";
    public static final String BYTES_RECEIVED = "Bytes Received";
    public static final String BYTES_SENT = "Bytes Sent";
    public static final String CONNECTED_SINCE = "Connected Since";
    public static final String CONNECTED_SINCE_TIME_T = "Connected Since (time_t)";
    public static final String USERNAME = "Username";
    public static final String CLIENT_ID = "Client ID";
    public static final String PEER_ID = "Peer ID";
    public static final String DATA_CHANNEL_CIPHER = "Data Channel Cipher";

    private String commonName;
    private String realAddress;
    private String virtualAddress;
    private String virtualIPv6Address;
    private Long bytesReceived;
    private Long bytesSent;
    private String connectedSince;
    private Long connectedSinceTimeStamps;
    private String username;
    private Long clientId;
    private String peerId;
    private String dataChannelCipher;


    @Override
    public String toStr() {
        return commonName+","
                +realAddress+","
                +virtualAddress+","
                +virtualIPv6Address+","
                +bytesReceived+","
                +bytesSent+","
                +connectedSince+","
                +connectedSinceTimeStamps+","
                +username+","
                +clientId+","
                +peerId+","
                +dataChannelCipher;
    }
}
