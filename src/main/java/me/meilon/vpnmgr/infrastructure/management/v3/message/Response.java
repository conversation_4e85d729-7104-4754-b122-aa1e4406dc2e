package me.meilon.vpnmgr.infrastructure.management.v3.message;

import jakarta.annotation.Nonnull;
import lombok.Getter;

/**
 * CRV1::Om01u7Fh4LrGBS7uh0SWmzwabUiGiW6l::123
 */
@Getter
public class Response {

    private static final String PREFIX = "CRV1";
    private final String stateId;
    private final String text;

    private Response(@Nonnull String stateId, @Nonnull String text) {
        this.stateId = stateId;
        this.text = text;
    }

    public static boolean match(@Nonnull String response){
        return response.startsWith(PREFIX);
    }

    public static Response of(@Nonnull String response) {
        if (!response.startsWith(PREFIX)){
            throw new IllegalArgumentException("不是一个合法的响应");
        }
        String[] split = response.split(":");
        if (split.length != 5){
            throw new IllegalArgumentException("不是一个合法的响应");
        }
        return new Response(split[2], split[4]);
    }

    @Override
    public String toString() {
        return PREFIX + "::" + stateId + "::" + text;
    }
}
