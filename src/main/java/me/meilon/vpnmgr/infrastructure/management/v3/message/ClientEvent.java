package me.meilon.vpnmgr.infrastructure.management.v3.message;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.ClientActions;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.Notification;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.Source;
import me.meilon.vpnmgr.infrastructure.utils.DateUtil;

import java.time.LocalDateTime;


@Getter
@Setter
public class ClientEvent extends Notification {

    private static final String N_CLIENTS = "n_clients";
    private static final String PASSWORD = "password";
    private static final String SESSION_ID = "session_id";
    private static final String SESSION_STATE = "session_state";
    private static final String UNTRUSTED_PORT = "untrusted_port";
    private static final String UNTRUSTED_IP = "untrusted_ip";
    private static final String COMMON_NAME = "common_name";
    private static final String USERNAME = "username";
    private static final String IV_SSO = "IV_SSO";
    private static final String IV_GUI_VER = "IV_GUI_VER";
    private static final String IV_CIPHERS = "IV_CIPHERS";
    private static final String IV_MTU = "IV_MTU";
    private static final String IV_PROTO = "IV_PROTO";
    private static final String IV_TCPNL = "IV_TCPNL";
    private static final String IV_NCP = "IV_NCP";
    private static final String IV_PLAT = "IV_PLAT";
    private static final String IV_VER = "IV_VER";
    private static final String TLS_SERIAL_HEX_0 = "tls_serial_hex_0";
    private static final String TLS_SERIAL_0 = "tls_serial_0";
    private static final String TLS_DIGEST_SHA256_0 = "tls_digest_sha256_0";
    private static final String TLS_DIGEST_0 = "tls_digest_0";
    private static final String TLS_ID_0 = "tls_id_0";
    private static final String X509_0_CN = "X509_0_CN";
    private static final String TLS_SERIAL_HEX_1 = "tls_serial_hex_1";
    private static final String TLS_SERIAL_1 = "tls_serial_1";
    private static final String TLS_DIGEST_SHA256_1 = "tls_digest_sha256_1";
    private static final String TLS_DIGEST_1 = "tls_digest_1";
    private static final String TLS_ID_1 = "tls_id_1";
    private static final String X509_1_CN = "X509_1_CN";
    private static final String REMOTE_PORT_1 = "remote_port_1";
    private static final String LOCAL_PORT_1 = "local_port_1";
    private static final String LOCAL_1 = "local_1";
    private static final String PROTO_1 = "proto_1";
    private static final String DAEMON_PID = "daemon_pid";
    private static final String DAEMON_START_TIME = "daemon_start_time";
    private static final String DAEMON_LOG_REDIRECT = "daemon_log_redirect";
    private static final String DAEMON = "daemon";
    private static final String VERB = "verb";
    private static final String CONFIG = "config";
    private static final String IFCONFIG_LOCAL = "ifconfig_local";
    private static final String IFCONFIG_NETMASK = "ifconfig_netmask";
    private static final String SCRIPT_CONTEXT = "script_context";
    private static final String TUN_MTU = "tun_mtu";
    private static final String LINK_MTU = "link_mtu";
    private static final String DEV = "dev";
    private static final String DEV_TYPE = "dev_type";
    private static final String REDIRECT_GATEWAY = "redirect_gateway";
    private static final String TRUSTED_IP = "trusted_ip";
    private static final String TRUSTED_PORT = "trusted_port";
    private static final String IFCONFIG_POOL_REMOTE_IP = "ifconfig_pool_remote_ip";
    private static final String TIME_ASCII = "time_ascii";
    private static final String TIME_UNIX = "time_unix";
    private static final String TIME_DURATION = "time_duration";

    /**
     * 客户端的行为
     */
    @Nonnull
    private ClientActions action;
    /**
     * 客户端 ID，每个连接的客户端的数字 ID，
     * 序列 = 0,1,2,...
     */
    private Long cid;
    /**
     * 密钥 ID，与给定客户端 TLS 会话关联的密钥的数字 ID，
     * 序列 = 0,1,2,...
     */
    private Long kid;
    /**
     * IPv4 地址格式为 ******* 或 *******/************* 的子网
     */
    private String addr;
    /**
     * 主要（1）或次要（0）VPN 地址/子网。
     * 所有客户端都至少有一个主 IP 地址。
     * 次要地址/子网与特定于客户端的“iroute”指令相关联。
     */
    private String pri;

    public ClientEvent() {
        super(Source.CLIENT);
    }


    public String getNClients(){
        return getEnv(N_CLIENTS);
    }
    public String getPassword(){
        return getEnv(PASSWORD);
    }

    public String getSessionId(){
        return getEnv(SESSION_ID);
    }

    public String getSessionState(){
        return getEnv(SESSION_STATE);
    }

    public String getUntrustedPort(){
        return getEnv(UNTRUSTED_PORT);
    }

    public String getUntrustedIp(){
        return getEnv(UNTRUSTED_IP);
    }

    /**
     * 通用名称, 通常情况下为证书名
     * 注: 如果配置了 username-as-common-name,
     * 则只有在 ClientActions.CONNECTED 时此值为证书名
     * 否则为 username 的值
     */
    public String getCommonName(){
        return getEnv(COMMON_NAME);
    }

    public String getUsername(){
        return getEnv(USERNAME);
    }

    public String getIvSso(){
        return getEnv(IV_SSO);
    }

    public String getIvGuiVer(){
        return getEnv(IV_GUI_VER);
    }

    public String getIvCiphers(){
        return getEnv(IV_CIPHERS);
    }

    public String getIvMtu(){
        return getEnv(IV_MTU);
    }

    public String getIvProto(){
        return getEnv(IV_PROTO);
    }

    public String getIvTcpnl(){
        return getEnv(IV_TCPNL);
    }

    public String getIvNcp(){
        return getEnv(IV_NCP);
    }

    public String getIvPlat(){
        return getEnv(IV_PLAT);
    }

    public String getIvVer(){
        return getEnv(IV_VER);
    }

    public String getTlsSerialHex0(){
        return getEnv(TLS_SERIAL_HEX_0);
    }

    public String getTlsSerial0(){
        return getEnv(TLS_SERIAL_0);
    }

    public String getTlsDigestSha2560(){
        return getEnv(TLS_DIGEST_SHA256_0);
    }

    public String getTlsDigest0(){
        return getEnv(TLS_DIGEST_0);
    }

    public String getTlsId0(){
        return getEnv(TLS_ID_0);
    }

    /**
     * 客户端证书名
     */
    public String getX5090Cn(){
        return getEnv(X509_0_CN);
    }

    public String getTlsSerialHex1(){
        return getEnv(TLS_SERIAL_HEX_1);
    }

    public String getTlsSerial1(){
        return getEnv(TLS_SERIAL_1);
    }

    public String getTlsDigestSha2561(){
        return getEnv(TLS_DIGEST_SHA256_1);
    }

    public String getTlsDigest1(){
        return getEnv(TLS_DIGEST_1);
    }

    public String getTlsId1(){
        return getEnv(TLS_ID_1);
    }

    public String getX5091Cn(){
        return getEnv(X509_1_CN);
    }

    public String getRemotePort1(){
        return getEnv(REMOTE_PORT_1);
    }

    public String getLocalPort1(){
        return getEnv(LOCAL_PORT_1);
    }

    public String getLocal1(){
        return getEnv(LOCAL_1);
    }

    public String getProto1(){
        return getEnv(PROTO_1);
    }

    public String getDaemonPid(){
        return getEnv(DAEMON_PID);
    }

    public String getDaemonStartTime(){
        return getEnv(DAEMON_START_TIME);
    }

    public String getDaemonLogRedirect(){
        return getEnv(DAEMON_LOG_REDIRECT);
    }

    public String getDaemon(){
        return getEnv(DAEMON);
    }

    public String getVerb(){
        return getEnv(VERB);
    }

    public String getConfig(){
        return getEnv(CONFIG);
    }

    public String getIfconfigLocal(){
        return getEnv(IFCONFIG_LOCAL);
    }

    public String getIfconfigNetmask(){
        return getEnv(IFCONFIG_NETMASK);
    }

    public String getScriptContext(){
        return getEnv(SCRIPT_CONTEXT);
    }

    public String getTunMtu(){
        return getEnv(TUN_MTU);
    }

    public String getLinkMtu(){
        return getEnv(LINK_MTU);
    }

    /**
     * 获取虚拟网卡名称
     * 例: tun0
     */
    public String getDev(){
        return getEnv(DEV);
    }

    public String getDevType(){
        return getEnv(DEV_TYPE);
    }

    public String getRedirectGateway(){
        return getEnv(REDIRECT_GATEWAY);
    }

    public String getTrustedIp(){
        return getEnv(TRUSTED_IP);
    }

    public String getTrustedPort(){
        return getEnv(TRUSTED_PORT);
    }

    public String getIfconfigPoolRemoteIp(){
        return getEnv(IFCONFIG_POOL_REMOTE_IP);
    }

    /**
     * 客户端连接时间，格式为人类可读的时间字符串。
     * {@link ClientActions#CONNECT}之后可用
     */
    public String getTimeAscii(){
        return getEnv(TIME_ASCII);
    }
    /**
     * 客户端连接时间，格式为 unix 整数日期时间值。
     * {@link ClientActions#CONNECT}之后可用
     */
    public Long getTimeUnix(){
        String timeUnix = getEnv(TIME_UNIX);
        if (timeUnix == null){
            return null;
        }
        return Long.parseLong(timeUnix);
    }
    public LocalDateTime getTime(){
        String timeUnix = getEnv(TIME_UNIX);
        if (timeUnix == null){
            return LocalDateTime.now();
        }
        long time = Long.parseLong(timeUnix);
        return DateUtil.toLocalDateTime(time);
    }
    /**
     * 当前断开连接的客户端会话的持续时间（以秒为单位）。
     * {@link ClientActions#DISCONNECT} 时可用
     */
    public String getTimeDuration(){
        return getEnv(TIME_DURATION);
    }

    @JsonIgnore
    public String getTrustedAddress(){
        return this.getTrustedIp() + ":" + this.getTrustedPort();
    }

    @JsonIgnore
    public String getDeviceType(){
        return covDriver(this.getIvPlat());
    }

    @JsonIgnore
    @Nonnull
    private String covDriver(String ivPlat) {
        return switch (ivPlat) {
            case "os" -> "ios";
            case "win" -> "Windows";
            case "linux" -> "Linux";
            case "solaris" -> "Solaris";
            case "openbsd" -> "OpenBSD";
            case "mac" -> "Mac";
            case "netbsd" -> "NetBSD";
            case "freebsd" -> "FreeBSD";
            default -> "Unknown";
        };
    }

    @Override
    public String toStr() {
        StringBuilder sb = new StringBuilder()
                .append(getSource().getHeader())
                .append(action)
                .append(",");
        switch (action){
            case REAUTH,CONNECT -> sb.append(cid).append(",").append(kid);
            case ESTABLISHED,DISCONNECT -> sb.append(cid);
            case ADDRESS -> sb.append(cid).append(",").append(addr).append(",").append(pri);
        }
        return sb.append("\n") + super.toStr();
    }
}
