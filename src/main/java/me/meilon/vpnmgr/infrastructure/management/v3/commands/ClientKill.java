package me.meilon.vpnmgr.infrastructure.management.v3.commands;

import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.infrastructure.management.Command;

public class ClientKill implements Command {

  private static final String CMD = "client-kill";
  private final Long cid;

  public ClientKill(Long cid) {
    this.cid = cid;
  }

  public static ClientKill of(@Nonnull Long cid) {
    return new ClientKill(cid);
  }

  @Override
  public String toCmdStr() {
    return CMD + " " + cid;
  }
}
