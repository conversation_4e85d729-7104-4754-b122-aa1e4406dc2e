package me.meilon.vpnmgr.infrastructure.management.v3.parsers;

import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.infrastructure.management.AbstractParser;
import me.meilon.vpnmgr.infrastructure.management.v3.message.ClientStatus;
import me.meilon.vpnmgr.infrastructure.management.v3.message.RoutingTable;
import me.meilon.vpnmgr.infrastructure.management.v3.message.StatusMessage;

import java.util.Arrays;

public class StatusParser extends AbstractParser<StatusMessage> {

    private final String SPLIT = "\t";
    private StatusMessage cache;

    @Override
    public boolean isMatch(@Nonnull String line) {
        return line.startsWith(StatusMessage.TITLE + SPLIT + StatusMessage.OPEN_VPN);
    }

    @Override
    public StatusMessage from(@Nonnull String line) {
        if (cache == null) {
            cache = new StatusMessage();
        }
        if (line.startsWith(StatusMessage.TITLE)){
            cache.setTitle(line.substring(StatusMessage.TITLE.length()).trim());
        }
        if (line.startsWith(StatusMessage.TIME)){
            cache.setTime(parBase(line));
        }
        if (line.startsWith(ClientStatus.CLIENT_LIST)){
            cache.addClient(parClient(line));
        }
        if (line.startsWith(RoutingTable.ROUTING_TABLE)){
            cache.addRoutingTable(parRoutingTable(line));
        }
        if (line.startsWith(StatusMessage.GLOBAL_STATS)){
            cache.setGlobalStats(parBase(line));
        }
        if (isEnd(line)){
            final StatusMessage status = cache;
            cache = null;
            return status;
        }
        return null;
    }

    private String parBase(String line){
        String[] split = line.split(SPLIT);
        if (split.length < 3) {
            System.out.println(Arrays.toString(split));
            return null;
        }
        return split[1];
    }


    private ClientStatus parClient(String line){
        String[] split = line.split(SPLIT);
        if (split.length < 12) {
            return null;
        }
        ClientStatus client = new ClientStatus();
        client.setCommonName(split[1]);
        client.setRealAddress(split[2]);
        client.setVirtualAddress(split[3]);
        client.setVirtualIPv6Address(split[4]);
        client.setBytesReceived(Long.valueOf(split[5]));
        client.setBytesSent(Long.valueOf(split[6]));
        client.setConnectedSince(split[7]);
        client.setConnectedSinceTimeStamps(Long.valueOf(split[8]));
        client.setUsername(split[9]);
        client.setClientId(Long.valueOf(split[10]));
        client.setPeerId(split[11]);
        client.setDataChannelCipher(split[12]);
        return client;
    }

    private RoutingTable parRoutingTable(String line){
        String[] split = line.split(SPLIT);
        if (split.length < 6) {
            System.out.println(Arrays.toString(split));
            return null;
        }
        RoutingTable routingTable = new RoutingTable();
        routingTable.setVirtualAddress(split[1]);
        routingTable.setCommonName(split[2]);
        routingTable.setRealAddress(split[3]);
        routingTable.setLastRef(split[4]);
        routingTable.setLastRefTimeStamps(Long.parseLong(split[5]));
        return routingTable;
    }

}
