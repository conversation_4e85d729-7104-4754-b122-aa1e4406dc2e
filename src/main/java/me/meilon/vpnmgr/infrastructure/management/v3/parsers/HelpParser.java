package me.meilon.vpnmgr.infrastructure.management.v3.parsers;

import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.infrastructure.management.AbstractParser;
import me.meilon.vpnmgr.infrastructure.management.v3.message.HelpMessage;
import org.jetbrains.annotations.NotNull;

public class HelpParser extends AbstractParser<HelpMessage> {

    public static final String HEADER = "Management Interface for OpenVPN";
    private HelpMessage cache;

    @Override
    public boolean isMatch(@NotNull String line) {
        return line.startsWith(HEADER);
    }

    @Override
    public HelpMessage from(@Nonnull String line) {
        if (cache == null) {
            cache = new HelpMessage();
        }
        cache.addLine(line);
        if (isEnd(line)) {
            HelpMessage help = cache;
            cache = null;
            return help;
        }
        return null;
    }

}
