package me.meilon.vpnmgr.infrastructure.management.v3.message;


import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.management.Message;

@Getter
@Setter
public class RoutingTable implements Message {
    public static final String ROUTING_TABLE = "ROUTING_TABLE";
    public static final String VIRTUAL_ADDRESS_2 = "Virtual Address";
    public static final String REAL_ADDRESS_2 = "Real Address";
    public static final String LAST_REF = "Last Ref";
    public static final String LAST_REF_TIME_T = "Last Ref (time_t)";

    private String virtualAddress;
    private String commonName;
    private String realAddress;
    private String lastRef;
    private Long lastRefTimeStamps;


    @Override
    public String toStr() {
        return virtualAddress + ","+commonName+","+realAddress+","+lastRef+","+lastRefTimeStamps;
    }
}
