package me.meilon.vpnmgr.infrastructure.management.v3.message;


import me.meilon.vpnmgr.infrastructure.management.Message;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.Source;

public class LoginMessage implements Message {

    private final String data;

    public LoginMessage(String data) {
        this.data = data;
    }

    @Override
    public String toStr() {
        return Source.INFO.getHeader() + data;
    }


}
