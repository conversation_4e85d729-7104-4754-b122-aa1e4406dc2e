package me.meilon.vpnmgr.infrastructure.management;

import jakarta.annotation.Nonnull;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadPool {

    private static volatile ThreadPool INSTANCE;

    public static ThreadPool getInstance() {
        if (INSTANCE == null) {
            synchronized (ThreadPool.class) {
                if (INSTANCE == null) {
                    INSTANCE = new ThreadPool();
                }
            }
        }
        return INSTANCE;
    }

    private final ThreadPoolExecutor executor;

    public ThreadPool() {
        executor = new ThreadPoolExecutor(
                30,
                30,
                60,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(),
                ThreadFactoryBuilder.create().setNameFormat("handler-%d").build(),
                new ThreadPoolExecutor.AbortPolicy());
    }

    public void execute(@Nonnull Runnable runnable) {
        executor.execute(runnable);
    }


}
