package me.meilon.vpnmgr.infrastructure.management.v3.parsers;

import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.infrastructure.management.AbstractParser;
import me.meilon.vpnmgr.infrastructure.management.v3.message.Version;

public class VersionParser extends AbstractParser<Version> {

    private Version cache;

    @Override
    public boolean isMatch(@Nonnull String line) {
        return line.startsWith(Version.OPENVPN_VERSION);
    }

    @Override
    public Version from(@Nonnull String line) {
        if (cache == null){
            cache = new Version();
        }
        if (line.startsWith(Version.OPENVPN_VERSION)){
            String version = line.substring(Version.OPENVPN_VERSION.length()).trim();
            cache.setOpenvpnVersion(version);
        }
        if (line.startsWith(Version.MANAGEMENT_VERSION)){
            String version = line.substring(Version.MANAGEMENT_VERSION.length()).trim();
            cache.setManagementVersion(version);
        }
        if (isEnd(line)){
            final Version version = cache;
            cache = null;
            return version;
        }
        return null;
    }

}
