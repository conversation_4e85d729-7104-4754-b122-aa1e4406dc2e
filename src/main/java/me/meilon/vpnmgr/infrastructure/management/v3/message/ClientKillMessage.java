package me.meilon.vpnmgr.infrastructure.management.v3.message;

import lombok.Getter;
import me.meilon.vpnmgr.infrastructure.management.Message;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.CmdResult;


@Getter
public class ClientKillMessage implements Message {




    private final CmdResult result;
    private final String message;

    private ClientKillMessage(CmdResult result, String message) {
        this.result = result;
        this.message = message;
    }

    public static ClientKillMessage of(CmdResult type, String message) {
        return new ClientKillMessage(type, message);
    }

    @Override
    public String toStr() {
        return result.getValue() + message;
    }

    @Override
    public String toString() {
        return toStr();
    }

}
