package me.meilon.vpnmgr.infrastructure.management;

public abstract class AbstractFuture<T extends Message> implements OVMFuture<T> {

    private T message;


    @SuppressWarnings("unchecked")
    protected void setMessage(Message message) {
        this.message = (T) message;
    }

    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return false;
    }

    @Override
    public boolean isCancelled() {
        return false;
    }

    @Override
    public boolean isDone() {
        return message != null;
    }

    @Override
    public T get() {
        return message;
    }

}
