package me.meilon.vpnmgr.infrastructure.management.v3.message.base;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import me.meilon.vpnmgr.infrastructure.management.Message;

import java.util.HashMap;
import java.util.Map;

public abstract class Notification implements Message {

    public static final String ENV = "ENV,";
    /**
     * 通知消息的来源
     */
    private Source source;
    /**
     * 通知消息的类型
     */
    private String type;
    private Map<String,String> envs;

    public Notification(Source source) {
        this.source = source;
    }


    @Nonnull
    public Source getSource() {
        return source;
    }

    public void setSource(Source source) {
        this.source = source;
    }

    @Nullable
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void addEnv(String key, String value){
        if(envs == null){
            envs = new HashMap<>(50);
        }
        envs.put(key,value);
    }

    public String getEnv(String key){
        if(envs == null){
            return null;
        }
        return envs.get(key);
    }

    @Override
    public String toStr() {
        StringBuilder sb = new StringBuilder();
        if (envs != null){
            envs.forEach((key,value)->{
                if(key != null && value != null){
                    sb.append(source.getHeader())
                            .append(ENV)
                            .append(key)
                            .append("=")
                            .append(value)
                            .append("\n");
                }
            });
        }
        return sb.toString();
    }
}
