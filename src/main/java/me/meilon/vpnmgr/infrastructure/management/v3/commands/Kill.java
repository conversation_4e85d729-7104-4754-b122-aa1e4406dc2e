package me.meilon.vpnmgr.infrastructure.management.v3.commands;

import me.meilon.vpnmgr.infrastructure.management.Command;

/**
 * COMMAND -- kill
 * ---------------
 * 在服务器模式下，杀死特定的客户端实例。
 * 使用“status”命令查看已连接的客户端。
 */
public class Kill implements Command {

    private static final String CMD = "kill";

    /**
     * 通用名称或源地址和端口
     * 为 通用名 时表示杀掉所有匹配的客户端实例
     * 为 源地址和端口 时表示杀掉指定源地址的客户端实例
     * 例:
     *   kill Test-Client
     *   杀死具有“Test-Client”通用名称的客户端实例。
     *   kill 1.2.3.4:4000
     *   杀死具有 "1.2.3.4:4000" 源地址和端口的客户端实例。
     */
    private final String option;

    private Kill(String option) {
        this.option = option;
    }

    public static Kill of(String option) {
        return new Kill(option);
    }

    @Override
    public String toCmdStr() {
        return CMD + " " + option;
    }
}
