package me.meilon.vpnmgr.infrastructure.management.v3.commands;

import me.meilon.vpnmgr.infrastructure.management.Command;

public class Status implements Command {

    public static final String CMD = "status";
    private static final Status INSTANCE = new Status(null);

    private final Integer version;

    public static Status of(){
        return of(3);
    }
    public static Status of(Integer version){
        if (version == null){
            return INSTANCE;
        }
        return new Status(version);
    }

    public Status(Integer version) {
        this.version = version;
    }

    @Override
    public String toCmdStr() {
        if (version == null){
            return CMD;
        }
        return CMD + " " + version;
    }
}
