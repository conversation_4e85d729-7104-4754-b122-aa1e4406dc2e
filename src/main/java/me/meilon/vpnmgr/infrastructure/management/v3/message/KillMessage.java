package me.meilon.vpnmgr.infrastructure.management.v3.message;

import lombok.Getter;
import me.meilon.vpnmgr.infrastructure.management.Message;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.CmdResult;


@Getter
public class KillMessage implements Message {


    public static final String COMMON_NAME = "common name";
    public static final String ADDRESS = "client at address";

    private final CmdResult result;
    private final String option;
    private final String message;

    private KillMessage(CmdResult result, String option, String message) {
        this.result = result;
        this.option = option;
        this.message = message;
    }

    public static KillMessage of(CmdResult type, String option, String message) {
        return new KillMessage(type, option, message);
    }

    @Override
    public String toStr() {
        return result.getValue() + message;
    }

    @Override
    public String toString() {
        return toStr();
    }

}
