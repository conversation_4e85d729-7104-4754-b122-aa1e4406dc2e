package me.meilon.vpnmgr.infrastructure.management.v3.parsers;

import me.meilon.vpnmgr.infrastructure.management.AbstractParser;
import me.meilon.vpnmgr.infrastructure.management.v3.message.KillMessage;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.CmdResult;
import org.jetbrains.annotations.NotNull;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static me.meilon.vpnmgr.infrastructure.management.v3.message.base.CmdResult.ERROR;
import static me.meilon.vpnmgr.infrastructure.management.v3.message.base.CmdResult.SUCCESS;


/**
 * SUCCESS: common name 'cmmon_name' found, 1 client(s) killed
 * SUCCESS: 1 client(s) at address ***************:11784 killed
 * ERROR: common name 'cmmon_name' not found
 * ERROR: client at address ***********:3000 not found
 */
public class KillParser extends AbstractParser<KillMessage> {
    @Override
    public boolean isMatch(@NotNull String line) {
        return (line.startsWith(SUCCESS.getValue()) && line.endsWith("killed"))
                || (line.startsWith(ERROR.getValue()) && line.endsWith("not found"));
    }

    @Override
    public KillMessage from(@NotNull String line) {

        CmdResult result = line.startsWith(SUCCESS.getValue()) ? SUCCESS : ERROR;
        String message = line.substring(result.length()).trim();
        String option = getOption(message);
        return KillMessage.of(result, option, message);

    }

    private String getOption(String message){
        if (message.startsWith(KillMessage.COMMON_NAME)) {
            String tmp = getMatchStr(message, common_name_pattern);
            if(tmp != null){
                return tmp.substring(2, tmp.length() - 2);
            }
        }
        else {
            String tmp = getMatchStr(message, address_pattern);
            if(tmp != null){
                return tmp.trim();
            }
        }
        return null;
    }

    private final Pattern common_name_pattern = Pattern.compile(" '.*?' ");
    private final Pattern address_pattern = Pattern.compile(" \\d{1,3}(?:\\.\\d{1,3}){3}:\\d{1,5} ");
    private static String getMatchStr(final String str, Pattern pattern) {
        Matcher matcher = pattern.matcher(str);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }



}
