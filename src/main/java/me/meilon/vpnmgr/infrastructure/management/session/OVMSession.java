package me.meilon.vpnmgr.infrastructure.management.session;

import me.meilon.vpnmgr.infrastructure.management.ParserRepository;

import java.io.IOException;

public interface OVMSession {

    void connect() throws IOException;

    OVMReader getReader();

    OVMWriter getWriter();

    ParserRepository getParserRepository();

    void disconnect() throws IOException;

    boolean isConnected();

    boolean isClosed();
}
