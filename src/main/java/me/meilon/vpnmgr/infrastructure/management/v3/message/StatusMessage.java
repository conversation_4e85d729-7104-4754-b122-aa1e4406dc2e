package me.meilon.vpnmgr.infrastructure.management.v3.message;

import lombok.Getter;
import me.meilon.vpnmgr.infrastructure.management.Message;

import java.util.ArrayList;
import java.util.List;


@Getter
public class StatusMessage implements Message {

    public static final String TITLE = "TITLE";
    public static final String OPEN_VPN = "OpenVPN";
    public static final String TIME = "TIME";
    public static final String HEADER = "HEADER";
    public static final String GLOBAL_STATS = "GLOBAL_STATS";


    private String title;
    private String time;
    private final List<ClientStatus> clientList;
    private final List<RoutingTable> routingTable;
    private String globalStats;

    public StatusMessage() {
        clientList = new ArrayList<>();
        routingTable = new ArrayList<>();
    }

    public void setTitle(String title){
        this.title = title;
    }

    public void setTime(String time){
        this.time = time;
    }

    public void addClient(ClientStatus client){
        clientList.add(client);
    }

    public void addRoutingTable(RoutingTable routingTable){
        this.routingTable.add(routingTable);
    }

    public void setGlobalStats(String globalStats){
        this.globalStats = globalStats;
    }

    @Override
    public String toStr() {
        StringBuilder sb = new StringBuilder();
        sb.append(title).append("\n")
                .append(time).append("\n")
                .append(ClientStatus.CLIENT_LIST).append("\n");
        for (ClientStatus client : clientList) {
            sb.append(client.toStr()).append("\n");
        }
        sb.append(RoutingTable.ROUTING_TABLE).append("\n");
        for (RoutingTable routingTable : routingTable) {
            sb.append(routingTable.toStr()).append("\n");
        }
        sb.append(globalStats).append("\n");
        return sb.toString();
    }

}
