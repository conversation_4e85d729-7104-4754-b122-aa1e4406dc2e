package me.meilon.vpnmgr.infrastructure.management;

import jakarta.annotation.Nonnull;

import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

public interface OVMFuture<T> extends Future<T> {

    /**
     * 阻塞当前线程, 直到获取结果
     * 注意: 此方法没有超时时间, 可能会导致线程一直阻塞
     */
    @Override
    T get();

    /**
     * 阻塞当前线程, 直到获取结果
     *
     * @param timeout 超时时间
     * @param unit 超时参数的时间单位
     */
    @Override
    T get(long timeout, @Nonnull TimeUnit unit);

    /**
     * 阻塞当前线程, 直到获取结果
     * @param timeout 超时时间, 单位毫秒
     */
    T get(long timeout);
}
