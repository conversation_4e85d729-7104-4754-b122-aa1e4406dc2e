package me.meilon.vpnmgr.infrastructure.management.v3.parsers;

import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.infrastructure.management.AbstractParser;
import me.meilon.vpnmgr.infrastructure.management.v3.message.StateMessage;

import java.util.Date;
import java.util.regex.Pattern;

public class StateParser extends AbstractParser<StateMessage> {

    private StateMessage state;
    private final Pattern p = Pattern.compile("^\\d+,\\w+,.*,.*,.*");


    /**
     * 正则表达式判断指定字符串, 是否由4个逗号分割, 且第一个字段是数字, 第二个字段是英文字母, 其余字段任意
     * 例如:
     * 1689505970,CONNECTED,SUCCESS,10.8.0.1,
     *
     * @param line 指定字符串
     * @return 是否匹配
     */
    @Override
    public boolean isMatch(@Nonnull String line) {
        return p.matcher(line).matches();
    }

    @Override
    public StateMessage from(@Nonnull String line) {

        if (state == null){
            state = convert(line);
        }
        if (isEnd(line)){
            final StateMessage temp = state;
            state = null;
            return temp;
        }

        return null;
    }

    private StateMessage convert(String line){
        String[] split = line.split(",");
        int length = split.length;
        StateMessage state = new StateMessage();
        state.setTimestamp(new Date(Long.parseLong(split[0]) * 1000));
        state.setState(split[1]);
        if (length > 2){
            state.setReason(split[2]);
        }
        if (length > 3){
            state.setIp(split[3]);
        }
        if (length > 4){
            state.setRemoteIp(split[4]);
        }
        return state;
    }


}
