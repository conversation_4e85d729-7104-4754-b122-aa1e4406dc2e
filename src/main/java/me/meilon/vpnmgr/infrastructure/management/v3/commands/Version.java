package me.meilon.vpnmgr.infrastructure.management.v3.commands;

import me.meilon.vpnmgr.infrastructure.management.Command;

public class Version implements Command {

    private static final String CMD = "version";

    private static final Version INSTANCE = new Version();

    public static Version of(){
        return INSTANCE;
    }

    @Override
    public String toCmdStr() {
        return CMD;
    }
}
