package me.meilon.vpnmgr.infrastructure.management;



import java.io.IOException;


public interface OVMErrorHandler {

    void handle(final IOException response);

    default void handle0(final IOException response){
        try {
            handle(response);
        }catch (Throwable e) {
            e.printStackTrace();
        }
    }

    default void handle(final Throwable response){
    }

    default void handle0(final Throwable response){
        try {
            handle(response);
        }catch (Throwable e) {
            e.printStackTrace();
        }
    }
}
