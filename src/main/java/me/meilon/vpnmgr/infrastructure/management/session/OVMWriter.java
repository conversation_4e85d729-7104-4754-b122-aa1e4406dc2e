package me.meilon.vpnmgr.infrastructure.management.session;


import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.infrastructure.management.Command;

import java.io.*;

public class OVMWriter implements Closeable {

    private final BufferedWriter writer;

    public OVMWriter(Writer writer) {
        this.writer = new BufferedWriter(writer);
    }

    public OVMWriter(OutputStream writer) {
        this(new OutputStreamWriter(writer));
    }

    public void send(@Nonnull Command command) throws IOException {
        send(command.toCmdStr());
    }

    public synchronized void send(String command) throws IOException {
        writer.write(command);
        writer.newLine();
        writer.flush();
    }

    @Override
    public void close() throws IOException {
        if (writer != null){
            writer.close();
        }
    }
}
