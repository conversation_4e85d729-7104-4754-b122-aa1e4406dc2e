package me.meilon.vpnmgr.infrastructure.management.v3.parsers;


import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.infrastructure.management.AbstractParser;
import me.meilon.vpnmgr.infrastructure.management.v3.message.LoginMessage;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.Source;

public abstract class InfoParser extends AbstractParser<LoginMessage> {

    protected static final Source SOURCE = Source.INFO;

    @Override
    public boolean isMatch(@Nonnull String line) {
        return SOURCE.match(line);
    }

}
