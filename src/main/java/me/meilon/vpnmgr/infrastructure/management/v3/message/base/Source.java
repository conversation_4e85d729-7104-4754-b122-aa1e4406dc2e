package me.meilon.vpnmgr.infrastructure.management.v3.message.base;

/**
 * 实时消息的来源
 * “CLIENT”、“ECHO”、“FATAL”、“HOLD”、“INFO”、“LOG”、“NEED-OK”、“PASSWORD”或“STATE”
 *
 */
public enum Source {

    CLIENT("CLIENT:"),
    ECHO("ECHO:"),
    FATAL("FATAL:"),
    HOLD("HOLD:"),
    INFO("INFO:"),
    LOG("LOG:"),
    NEED_OK("NEED-OK:"),
    PASSWORD("PASSWORD:"),
    STATE("STATE:");

    private final String value;
    private final String header;

    Source(String value){
        this.value = value;
        this.header = ">"+value;
    }

    public String getValue(){
        return value;
    }

    public int length(){
        return value.length();
    }

    public boolean match(String line){
        return line.startsWith(header) || line.startsWith(value);
    }

    public String getHeader(){
        return header;
    }

    public int headerLength(){
        return header.length();
    }


}
