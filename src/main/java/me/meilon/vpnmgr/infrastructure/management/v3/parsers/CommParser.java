package me.meilon.vpnmgr.infrastructure.management.v3.parsers;

import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.management.AbstractParser;
import me.meilon.vpnmgr.infrastructure.management.v3.message.CommLine;

@Slf4j
public class CommParser extends AbstractParser<CommLine> {


    @Override
    public boolean isMatch(@Nonnull String line) {
        return true;
    }

    @Override
    public CommLine from(@Nonnull String line) {
        log.info("| {}", line);
        return new CommLine(line);
    }

}
