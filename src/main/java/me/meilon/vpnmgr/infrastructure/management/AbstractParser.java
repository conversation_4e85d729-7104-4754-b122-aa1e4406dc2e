package me.meilon.vpnmgr.infrastructure.management;

import jakarta.annotation.Nonnull;

import java.util.function.Consumer;

public abstract class AbstractParser<M extends Message> implements Parser<M> {

    public static final String END = "END";
    protected OVMTypeHandler<M> handler;

    /**
     * 解析一行接收到的消息, 如果是完整的消息体则返回, 否则返回null
     * @param line 消息行
     * @param consumer 回调函数, 对于某些消息, 需要返回一个命令给 openvpn 管理接口的, 通过这个回调函数返回
     * @return 返回解析后的消息体
     */
    @Override
    public Message parse(String line, Consumer<Command> consumer){
        M message = from(line);
        if (handler != null && message != null) {
            ThreadPool.getInstance().execute(() -> {
                Command command = handler.handle(message);
                if (command != null) {
                    consumer.accept(command);
                }
            });
        }
        return message;
    }

    @Override
    public void addHandler(OVMTypeHandler<M> handler) {
        this.handler = handler;
    }


    public boolean isEnd(@Nonnull String line){
        return line.startsWith(END);
    }

}
