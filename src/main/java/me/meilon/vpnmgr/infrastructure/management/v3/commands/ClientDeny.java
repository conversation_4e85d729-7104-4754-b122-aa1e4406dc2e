package me.meilon.vpnmgr.infrastructure.management.v3.commands;


import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.app.dto.AuthResult;
import me.meilon.vpnmgr.infrastructure.management.Command;

/**
 * 用于拒绝“>CLIENT:CONNECT”或“>CLIENT:REAUTH”请求。
 * client-deny {CID} {KID} "reason-text" ["client-reason-text"]
 * reason-text: 人类可读的消息，解释为什么拒绝了身份验证请求。
 * 此消息将输出到 OpenVPN 日志文件或 syslog。
 * client-reason-text: 将作为 AUTH_FAILED 消息的一部分发送到客户端的消息。
 */
public class ClientDeny implements Command {

    private static final String cmd = "client-deny";
    @Nonnull
    private final Long cid;
    @Nonnull
    private final Long kid;
    @Nonnull
    private final String reasonText;
    private final String clientReasonText;

    private ClientDeny(@Nonnull Long cid, @Nonnull Long kid,
                       @Nonnull String reasonText, String clientReasonText) {
        this.cid = cid;
        this.kid = kid;
        this.reasonText = reasonText;
        this.clientReasonText = clientReasonText;
    }

    public static ClientDeny of(Long cid, Long kid, AuthResult authResult){
        return new ClientDeny(cid, kid, authResult.getState().name(), authResult.getMessage());
    }

    public static ClientDeny of(Long cid, Long kid, String reasonText){
        return new ClientDeny(cid, kid,reasonText,null);
    }

    public static ClientDeny of(Long cid, Long kid, String reasonText, String clientReasonText){
        return new ClientDeny(cid, kid,reasonText,clientReasonText);
    }

    @Override
    public String toCmdStr() {
        return cmd + " " + cid + " " + kid + " " + reasonText + " " + clientReasonText;
    }
}
