package me.meilon.vpnmgr.infrastructure.management.v3.commands;

import me.meilon.vpnmgr.infrastructure.management.Command;

/**
 * COMMAND -- state
 * ----------------
 * 显示当前的 OpenVPN 状态，显示状态历史记录，或启用实时通知状态更改。
 * </br>
 * 以下是 OpenVPN 的状态：
 * </br>
 * CONNECTING    -- OpenVPN的初始状态。
 * WAIT          -- （仅限客户端）等待服务器的初始响应。
 * AUTH          -- （仅限客户端）与服务器进行身份验证。
 * GET_CONFIG    -- （仅限客户端）从服务器下载配置选项。
 * ASSIGN_IP     -- 将IP地址分配给虚拟网络接口。
 * ADD_ROUTES    -- 将路由添加到系统。
 * CONNECTED     -- 初始化序列已完成。
 * RECONNECTING  -- 发生了重新启动。
 * EXITING       -- 正在退出。
 * </br>
 * Command examples:
 * </br>
 *   state        -- 打印当前 OpenVPN 状态。
 *   state on     -- 启用状态更改的实时通知。
 *   state off    -- 禁用状态更改的实时通知。
 *   state all    -- 打印当前状态历史记录。
 *   state 3      -- 打印最近的 3 个状态转换。
 *   state on all -- 原子地显示状态历史记录，同时启用将来状态转换的实时状态通知。
 * 输出格式由4个逗号分隔的参数组成：
 *  (a) 整数的Unix日期/时间，
 *  (b) 状态名称，
 *  (c)可选的描述字符串（主要用于RECONNECTING和EXITING以显示断开连接的原因），
 *  (d) 可选的TUN/TAP本地IP地址（在ASSIGN_IP和CONNECTED时显示），
 *  (e) 可选的远程服务器地址（仅适用于OpenVPN 2.1或更高版本）。
 * </br>
 *  实时状态通知将在前面加上“>STATE:”前缀。
 */
public class State implements Command {

    public static final String CMD = "state";
    private static final State INSTANCE = new State();

    public static State of(){
        return INSTANCE;
    }

    @Override
    public String toCmdStr() {
        return CMD;
    }
}
