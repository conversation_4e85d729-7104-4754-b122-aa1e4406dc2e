package me.meilon.vpnmgr.infrastructure.management.v3.message;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.management.Message;
import me.meilon.vpnmgr.infrastructure.utils.DateUtil;

import java.util.Date;

@Getter
@Setter
public class StateMessage implements Message {

    private Date timestamp;
    /**
     * 状态名称
     * CONNECTING    -- OpenVPN的初始状态。
     * WAIT          -- （仅限客户端）等待服务器的初始响应。
     * AUTH          -- （仅限客户端）与服务器进行身份验证。
     * GET_CONFIG    -- （仅限客户端）从服务器下载配置选项。
     * ASSIGN_IP     -- 将IP地址分配给虚拟网络接口。
     * ADD_ROUTES    -- 将路由添加到系统。
     * CONNECTED     -- 初始化序列已完成。
     * RECONNECTING  -- 发生了重新启动。
     * EXITING       -- 正在进行优雅的退出。
     */
    private String state;
    /**
     * 可选的描述字符串（主要用于 RECONNECTING 和 EXITING 以显示断开连接的原因）
     */
    private String reason;
    /**
     * 可选的 TUN/TAP 本地 IP 地址（显示为 ASSIGN_IP 和 CONNECTED）
     */
    private String ip;
    /**
     * 远程服务器的可选地址（OpenVPN 2.1 或更高版本）
     */
    private String remoteIp;

    @JsonIgnore
    public String getTimeStr(){
        return DateUtil.toFormat(timestamp);
    }

    @Override
    public String toStr() {
        return getTimeStr() + "," + state + "," + reason + "," + ip + "," + remoteIp + ",,,";
    }

    @Override
    public String toString() {
        return toStr();
    }

}
