package me.meilon.vpnmgr.infrastructure.management.session;

import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.management.VOMAbstractSession;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketAddress;

@Slf4j
public class OVMSocket extends VOMAbstractSession {

    private final SocketAddress socketAddress;
    private Socket socket;
    private OVMWriter writer;
    private OVMReader reader;


    public OVMSocket(String host, Integer port) {
        this.socketAddress = new InetSocketAddress(host, port);
    }

    @Override
    public void connect() throws IOException {
        if (isClosed()){
            this.socket = new Socket();
            log.info("正在连接到 openVPN 管理接口 {} ...", socketAddress);
            this.socket.connect(socketAddress);
            reader = new OVMReader(this.socket.getInputStream(), getParserRepository());
            writer = new OVMWriter(this.socket.getOutputStream());
            log.info("openVPN 管理接口连接成功");
        }
    }

    @Override
    public OVMReader getReader() {
        return reader;
    }

    @Override
    public OVMWriter getWriter() {
        return writer;
    }

    @Override
    public void disconnect() throws IOException {
        if (socket != null && socket.isConnected()){
            socket.close();
        }
        socket = null;
        if (writer != null) writer.close();
        writer = null;
        if (reader != null) reader.close();
        reader = null;
    }

    @Override
    public boolean isClosed() {
        return socket == null || socket.isClosed();
    }

    @Override
    public boolean isConnected(){
        return socket != null && socket.isConnected();
    }
}
