package me.meilon.vpnmgr.infrastructure.management.session;

import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.management.VOMAbstractSession;

import java.io.IOException;
import java.io.Reader;
import java.io.Writer;
import java.net.StandardProtocolFamily;
import java.net.UnixDomainSocketAddress;
import java.nio.channels.Channels;
import java.nio.channels.SocketChannel;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;

@Slf4j
public class OVMUnix extends VOMAbstractSession {

    private final UnixDomainSocketAddress address;
    private SocketChannel channel;
    private OVMReader reader;
    private OVMWriter writer;


    public OVMUnix(Path sockFilePath) {
        this.address = UnixDomainSocketAddress.of(sockFilePath);
    }

    public OVMUnix(UnixDomainSocketAddress address) {
        this.address = address;
    }

    public void connect() throws IOException {
        if (isClosed()) {
            this.channel = SocketChannel.open(StandardProtocolFamily.UNIX);
            log.info("正在连接到 openVPN 管理接口 {} ...", address.getPath().toString());
            if (channel.connect(address)) {
                Writer w = Channels.newWriter(channel, StandardCharsets.UTF_8);
                Reader r = Channels.newReader(channel, StandardCharsets.UTF_8);
                reader = new OVMReader(r, getParserRepository());
                writer = new OVMWriter(w);
                log.info("openVPN 管理接口连接成功");
            } else {
                log.error("openVPN 管理接口连接失败: {}", address.getPath().toString());
                throw new IOException("openvpn 管理接口连接失败");
            }
        }
    }

    @Override
    public OVMReader getReader() {
        return reader;
    }

    @Override
    public OVMWriter getWriter() {
        return writer;
    }

    @Override
    public void disconnect() throws IOException {
        if (isConnected()) {
            channel.close();
        }
        channel = null;
        if (writer != null) writer.close();
        writer = null;
        if (reader != null) reader.close();
        reader = null;
    }

    @Override
    public boolean isClosed() {
        return !isConnected();
    }

    @Override
    public boolean isConnected() {
        return channel != null && channel.isConnected();
    }
}
