package me.meilon.vpnmgr.infrastructure.management.v3;

import org.jetbrains.annotations.NotNull;
import jakarta.websocket.RemoteEndpoint;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.management.Command;
import me.meilon.vpnmgr.infrastructure.management.OVMTypeHandler;
import me.meilon.vpnmgr.infrastructure.management.Parser;
import me.meilon.vpnmgr.infrastructure.management.v3.message.LoginMessage;
import me.meilon.vpnmgr.infrastructure.management.v3.parsers.LoginInfoParser;

@Slf4j
public class LoginHandler implements OVMTypeHandler<LoginMessage> {

    private final RemoteEndpoint.Async async;

    public LoginHandler(RemoteEndpoint.Async async){
        this.async = async;
    }

    @Override
    public Command handle(@NotNull LoginMessage response) {
        async.sendText(response.toString());
        return null;
    }

    @Override
    public Class<? extends Parser<LoginMessage>> getParserClass() {
        return LoginInfoParser.class;
    }
}
