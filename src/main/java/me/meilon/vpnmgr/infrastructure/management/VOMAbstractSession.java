package me.meilon.vpnmgr.infrastructure.management;

import me.meilon.vpnmgr.infrastructure.management.session.OVMSession;

public abstract class VOMAbstractSession implements OVMSession {

    private final ParserRepository parserRepository;

    public VOMAbstractSession() {
        this.parserRepository = new ParserRepository();
    }

    @Override
    public ParserRepository getParserRepository() {
        return parserRepository;
    }

}
