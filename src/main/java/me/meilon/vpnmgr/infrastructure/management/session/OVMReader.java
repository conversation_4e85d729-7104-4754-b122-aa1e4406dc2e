package me.meilon.vpnmgr.infrastructure.management.session;

import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.infrastructure.management.Command;
import me.meilon.vpnmgr.infrastructure.management.Message;
import me.meilon.vpnmgr.infrastructure.management.Parser;
import me.meilon.vpnmgr.infrastructure.management.ParserRepository;

import java.io.*;
import java.util.function.Consumer;

public class OVMReader implements Closeable{

    private final BufferedReader reader;
    private final ParserRepository parserRepository;

    public OVMReader(Reader reader, ParserRepository parserRepository) {
        this.reader = new BufferedReader(reader);
        this.parserRepository = parserRepository;
    }

    public OVMReader(InputStream inputStream, ParserRepository parserRepository) {
        this(new InputStreamReader(inputStream), parserRepository);
    }

    public String readLine() throws IOException {
        return reader.readLine();
    }

    public Message readMessage() throws IOException {
        return readMessage(null);
    }

    @Nonnull
    public Message readMessage(Consumer<Command> consumer) throws IOException {
        Parser<?> parser = null;
        Message message;
        String line;
        do {
            line = readLine();
            if (parser == null) {
                parser = parserRepository.getParser(line);
            }
            message = parser.parse(line, consumer);
        }
        while (message == null);
        return message;
    }

    public ParserRepository getParserRepository() {
        return parserRepository;
    }


    @Override
    public void close() throws IOException {
        if (reader != null){
            reader.close();
        }
    }
}
