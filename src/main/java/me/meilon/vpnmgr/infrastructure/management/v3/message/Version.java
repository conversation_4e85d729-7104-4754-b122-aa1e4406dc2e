package me.meilon.vpnmgr.infrastructure.management.v3.message;

import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;
import me.meilon.vpnmgr.infrastructure.management.Message;

@Getter
@Setter
public class Version implements Message {

    public static final String OPENVPN_VERSION = "OpenVPN Version:";
    public static final String MANAGEMENT_VERSION = "Management Version:";

    @Nonnull
    private String openvpnVersion;
    @Nonnull
    private String managementVersion;


    @Override
    public String toStr() {
        return OPENVPN_VERSION + " " + openvpnVersion + "\n" +
                MANAGEMENT_VERSION + " " + managementVersion;
    }
}
