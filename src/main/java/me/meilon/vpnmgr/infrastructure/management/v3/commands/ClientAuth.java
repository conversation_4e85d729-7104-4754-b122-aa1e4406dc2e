package me.meilon.vpnmgr.infrastructure.management.v3.commands;

import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.infrastructure.management.Command;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * 格式
 *   client-auth {CID} {KID}
 *   line_1
 *   line_2
 *   ...
 *   line_n
 *   END
 * </br>
 * CID，KID -- 客户端 ID 和密钥 ID。有关更多信息，请参阅“>CLIENT:”通知的文档。
 * </br>
 * line_1 到 line_n -- client-connect 配置文本块，如 --client-connect 脚本返回的那样。
 * 文本块可以为空，"END" 紧跟 "client-auth" 行（使用空文本块等效于使用 client-auth-nt 命令）。
 * </br>
 * 一个客户端连接配置文本块包含了OpenVPN指令，这些指令将应用于代表新连接的客户端实例对象。
 */
public class ClientAuth implements Command {

  private static final String cmd = "client-auth";
  private static final String END = "END";
  @Nonnull
  private final Long cid;
  @Nonnull
  private final Long kid;

  private final Set<String> lines = new HashSet<>();

  private ClientAuth(@Nonnull Long cid, @Nonnull Long kid) {
    this.cid = cid;
    this.kid = kid;
  }

  public static ClientAuth of(@Nonnull Long cid, @Nonnull Long kid) {
    return new ClientAuth(cid, kid);
  }

  public void addLineAll(Collection<String> lines) {
    this.lines.addAll(lines);
  }

  public void addLine(String line) {
    this.lines.add(line);
  }

  public void removeLine(String line) {
    this.lines.remove(line);
  }

  @Override
  public String toCmdStr() {
    StringBuilder builder = new StringBuilder();
    builder.append(cmd)
        .append(" ")
        .append(cid)
        .append(" ")
        .append(kid)
        .append("\n");
    if (!lines.isEmpty()) {
      for (String line : lines) {
        builder.append(line)
            .append("\n");
      }
    }
    builder.append(END);
    return builder.toString();
  }
}
