package me.meilon.vpnmgr.infrastructure.management.v3.commands;

import me.meilon.vpnmgr.infrastructure.management.Command;

/**
 * COMMAND -- bytecount
 * --------------------
 * bytecount 命令用于请求实时通知 OpenVPN 带宽使用情况。
 * Command syntax:
 * </br>
 *   bytecount n  (n 必须大于 0, 设置每 n 秒自动通知带宽使用情况)
 *   bytecount 0  (关闭 bytecount 通知)
 * </br>
 * 如果 OpenVPN 作为客户端运行，则 bytecount 通知如下所示：
 *   >BYTECOUNT:{BYTES_IN},{BYTES_OUT}
 * BYTES_IN 是从服务器接收的字节数，BYTES_OUT 是发送到服务器的字节数。
 * 如果 OpenVPN 作为服务器运行，则 bytecount 通知如下所示：
 *   >BYTECOUNT_CLI:{CID},{BYTES_IN},{BYTES_OUT}
 * CID 是客户端 ID，BYTES_IN 是从客户端接收的字节数，BYTES_OUT 是发送到客户端的字节数。
 * 请注意，当在服务器上使用 bytecount 命令时，每个连接的客户端将每 n 秒报告其带宽数字。
 * 当客户端断开连接时，最终的带宽数字将放在“bytes_received”和“bytes_sent”环境变量中，如 >CLIENT:DISCONNECT 通知中所包含的那样。
 */
public class Bytecount implements Command {

    public static final String CMD = "bytecount";

    /**
     * 设置每多少秒自动通知带宽使用情况
     * 为 0 时表示关闭 bytecount 通知
     */
    private final Integer second;

    private Bytecount(Integer second){
        this.second = second;
    }

    public static Bytecount of(Integer s){
        return new Bytecount(s);
    }

    @Override
    public String toCmdStr() {
        return CMD + " " + second;
    }
}
