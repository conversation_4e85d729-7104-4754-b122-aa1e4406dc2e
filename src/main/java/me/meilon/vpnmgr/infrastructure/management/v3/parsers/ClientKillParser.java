
package me.meilon.vpnmgr.infrastructure.management.v3.parsers;

import me.meilon.vpnmgr.infrastructure.management.AbstractParser;
import me.meilon.vpnmgr.infrastructure.management.v3.message.ClientKillMessage;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.CmdResult;
import org.jetbrains.annotations.NotNull;

import static me.meilon.vpnmgr.infrastructure.management.v3.message.base.CmdResult.ERROR;
import static me.meilon.vpnmgr.infrastructure.management.v3.message.base.CmdResult.SUCCESS;


/**
 * SUCCESS: client-kill command succeeded
 * ERROR: client-kill command failed
 */
public class ClientKillParser extends AbstractParser<ClientKillMessage> {

    public static final String PREFIX = "client-kill command";

    @Override
    public boolean isMatch(@NotNull String line) {
        return (line.startsWith(SUCCESS.getValue()) || line.startsWith(ERROR.getValue())) && line.contains(PREFIX);
    }

    @Override
    public ClientKillMessage from(@NotNull String line) {

        CmdResult result = line.startsWith(SUCCESS.getValue()) ? SUCCESS : ERROR;
        String message = line.substring(result.length()).trim();
        return ClientKillMessage.of(result, message);
    }

}
