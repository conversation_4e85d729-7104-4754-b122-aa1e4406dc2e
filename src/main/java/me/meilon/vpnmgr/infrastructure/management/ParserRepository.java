package me.meilon.vpnmgr.infrastructure.management;

import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.infrastructure.management.v3.commands.Kill;
import me.meilon.vpnmgr.infrastructure.management.v3.commands.State;
import me.meilon.vpnmgr.infrastructure.management.v3.commands.Status;
import me.meilon.vpnmgr.infrastructure.management.v3.message.KillMessage;
import me.meilon.vpnmgr.infrastructure.management.v3.message.StateMessage;
import me.meilon.vpnmgr.infrastructure.management.v3.message.StatusMessage;
import me.meilon.vpnmgr.infrastructure.management.v3.parsers.*;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


public class ParserRepository {

    private final Map<Class<?>, Parser<?>> parsers;
    private final Parser<?> DEFAULT_PARSER = new CommParser();
    private final Map<Class<? extends Command>, Class<? extends Message>> commandMapper;


    public ParserRepository() {
        this.parsers = new ConcurrentHashMap<>();
        this.commandMapper = new ConcurrentHashMap<>();
        init();
    }
    private void init() {
        addParser(new LoginInfoParser());
        addParser(new HelpParser());
        addParser(new StateParser());
        addParser(new StatusParser());
        addParser(new VersionParser());
        addParser(new KillParser());
        addParser(new ClientKillParser());
        addParser(new ClientNotificationParser());
        this.commandMapper.put(Kill.class, KillMessage.class);
        this.commandMapper.put(State.class, StateMessage.class);
        this.commandMapper.put(Status.class, StatusMessage.class);
    }

    @Nonnull
    public Parser<?> getParser(String line){
        for (Parser<?> parser : parsers.values()) {
            if (parser.isMatch(line)){
                return parser;
            }
        }
        return DEFAULT_PARSER;
    }

    public void addParser(@Nonnull Parser<?> parser){
        parsers.put(parser.getClass(), parser);
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    public void addHandler(OVMTypeHandler handler){
        Class<?> clazz = handler.getParserClass();
        if (parsers.containsKey(clazz)) {
            Parser<?> parser = parsers.get(clazz);
            parser.addHandler(handler);
        }
    }

    public Class<? extends Message> getMessageMapClass(Class<? extends Command> commandClass){
        Class<? extends Message> msgClass = commandMapper.get(commandClass);
        if (msgClass == null){
            throw new IllegalArgumentException("命令没有响应消息: " + commandClass.getName());
        }
        return msgClass;
    }
}
