package me.meilon.vpnmgr.infrastructure.management.v3.parsers;

import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.management.AbstractParser;
import me.meilon.vpnmgr.infrastructure.management.v3.message.ClientEvent;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.ClientActions;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.Notification;
import me.meilon.vpnmgr.infrastructure.management.v3.message.base.Source;
import org.jetbrains.annotations.NotNull;

@Slf4j
public class ClientNotificationParser extends AbstractParser<ClientEvent> {

    private ClientEvent cache;
    private final Source source = Source.CLIENT;
    private final String header = source.getHeader() + Notification.ENV;
    @Override
    public boolean isMatch(@NotNull String line) {
        return source.match(line);
    }

    @Override
    public ClientEvent from(@NotNull String line) {
        if (cache == null){
            cache = new ClientEvent();
        }
        String msg = line.substring(source.headerLength());

        if (msg.startsWith(ClientActions.CONNECT.name())
                || msg.startsWith(ClientActions.REAUTH.name())){
            String[] split = msg.split(",");
            if (split.length != 3){
                log.error("error: {}", msg);
                return null;
            }
            cache = new ClientEvent();
            cache.setAction(ClientActions.valueOf(split[0]));
            cache.setCid(Long.valueOf(split[1]));
            cache.setKid(Long.valueOf(split[2]));
            return null;
        }
        else if (msg.startsWith(ClientActions.ESTABLISHED.name())
          || msg.startsWith(ClientActions.DISCONNECT.name())){
            String[] split = msg.split(",");
            if (split.length != 2){
                log.error("error: "+msg);
                return null;
            }
            cache = new ClientEvent();
            cache.setAction(ClientActions.valueOf(split[0]));
            cache.setCid(Long.valueOf(split[1]));
        }
        else if (msg.startsWith(ClientActions.ADDRESS.name())){
            String[] split = msg.split(",");
            if (split.length != 4){
                log.error("error: "+msg);
                return null;
            }
            cache = new ClientEvent();
            cache.setAction(ClientActions.valueOf(split[0]));
            cache.setCid(Long.valueOf(split[1]));
            cache.setAddr(split[2]);
            cache.setPri(split[3]);
            return cache;
        }
        else if (msg.startsWith(ClientEvent.ENV + END)){
            return cache;
        }
        else {
            String env = msg.substring(ClientEvent.ENV.length());
            String[] kv = env.split("=");
            cache.addEnv(kv[0], kv[1]);
        }
        return null;
    }
}
