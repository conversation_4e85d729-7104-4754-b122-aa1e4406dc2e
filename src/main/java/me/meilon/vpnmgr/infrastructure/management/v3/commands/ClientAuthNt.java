package me.meilon.vpnmgr.infrastructure.management.v3.commands;

import jakarta.annotation.Nonnull;
import me.meilon.vpnmgr.infrastructure.management.Command;

public class ClientAuthNt implements Command {

    private static final String cmd = "client-auth-nt";
    @Nonnull
    private final Long cid;
    @Nonnull
    private final Long kid;

    private ClientAuthNt(@Nonnull Long cid, @Nonnull Long kid) {
        this.cid = cid;
        this.kid = kid;
    }

    public static ClientAuthNt of(@Nonnull Long cid, @Nonnull Long kid){
        return new ClientAuthNt(cid, kid);
    }


    @Override
    public String toCmdStr() {
        return cmd + " " + cid + " " + kid;
    }
}
