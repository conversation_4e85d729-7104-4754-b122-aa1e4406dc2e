package me.meilon.vpnmgr.infrastructure.management.v3.message.base;

/**
 * 客户端动作
 */
public enum ClientActions {

    /**
     * 通知新的客户端连接
     *     >CLIENT:CONNECT|REAUTH,{CID},{KID}
     *     >CLIENT:ENV,name1=val1
     *     >CLIENT:ENV,name2=val2
     *     >CLIENT:ENV,...
     *     >CLIENT:ENV,END
     */
    CONNECT,
    /**
     * 通知现有的客户端 TLS 会话重新协商
     */
    REAUTH,
    /**
     * 通知成功的客户端身份验证和会话启动。
     *     >CLIENT:ESTABLISHED,{CID}
     *     >CLIENT:ENV,name1=val1
     *     >CLIENT:ENV,name2=val2
     *     >CLIENT:ENV,...
     *     >CLIENT:ENV,END
     */
    ESTABLISHED,
    /**
     * 通知现有的客户端断开连接。
     *     >CLIENT:DISCONNECT,{CID}
     *     >CLIENT:ENV,name1=val1
     *     >CLIENT:ENV,name2=val2
     *     >CLIENT:ENV,...
     *     >CLIENT:ENV,END
     */
    DISCONNECT,
    /**
     * 通知特定的虚拟地址或子网现在与特定的客户端关联。
     * >CLIENT:ADDRESS,{CID},{ADDR},{PRI}
     */
    ADDRESS





}
