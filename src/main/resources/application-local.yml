spring:
  datasource:
    url: "***************************************************************************************************************************************************************************************************************"
    username: admin
    password: '32bo7a@dac!QRFLJ'
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimumIdle: 0
      maximum-pool-size: 10
      connection-timeout: 5000
      validation-timeout: 5000
      max-lifetime: 1800000
      idle-timeout: 600000
  jpa:
    database-platform: org.hibernate.dialect.MySQLDialect
    database: mysql
    hibernate:
      ddl-auto: update
    show-sql: false
    open-in-view: true
logging:
  charset:
    console: utf-8
  level:
    root: info
    org.springframework.data.neo4j: debug
    cn.idealio.framework.trace.reactive.TraceFilter: off
    io.netty.resolver.dns.DnsServerAddressStreamProviders: off
    org.springframework.core.LocalVariableTableParameterNameDiscoverer: error
  pattern:
    console: '%d{MM-dd HH:mm:ss.SSS}%clr(%5p)[%15.15t] %clr(%-40.40logger{39}){cyan} %clr(%6L){magenta} : %m%n'
openvpn:
  serviceName: <EMAIL>
  base-dir: /etc/openvpn
  server-dir: ${openvpn.base-dir}/server
  server-conf-path: ${openvpn.server-dir}/server.conf
  client-config-dir: ${openvpn.server-dir}/ccd
  client-template-path: ${openvpn.server-dir}/client-common.txt
  client-save-dir: /etc/openvpn_mgr/client
  management:
    type: TCP
    host: *************
    port: 7500
    sock-file: /etc/openvpn/server/openvpn.sock
  easy-rsa:
    base-dir: ${openvpn.server-dir}/easy-rsa
    run-file: ./easyrsa
  # IP地址池配置
  ip-pool:
    start-ip: *********
    end-ip: **********
    lease-time: 10  # 租约时间(天)
    renew-threshold: 5  # 续约阈值(天)