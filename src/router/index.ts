// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { createRouter, createWebHistory, createWebHashHistory, RouteRecordRaw } from 'vue-router'
//
import Base from '@/layouts/index.vue'
import NotFind from '@/layouts/error/404.vue'
import Desktop from '@/layouts/desktop/index.vue'
import MenuList from '@/layouts/menulist/index.vue'
// import Overview from '@/layouts/overview/index.vue'
// 登录
import Login from '@/views/login/login.vue'
import userInfo from '@/views/Mydat/mydata.vue'
/* 页面部分 */
import CertList from '@/views/CertList/index.vue'
import UserList from '@/views/UserList/index.vue'
import Settings from '@/views/Settings/index.vue'
import Resource from '@/views/Resource/index.vue'
import Group from '@/views/Group/index.vue'
import Client from '@/views/Client/index.vue'
import store from '@/store'
const routes: Array<RouteRecordRaw> = [
  {
    path: '',
    redirect: { name: 'Login' },
  },
  {
    path: '/login',
    name: '<PERSON><PERSON>',
    component: <PERSON>gin,
  },
  {
    path: '/',
    name: 'Desktop',
    component: Desktop,
    children: [
      {
        path: '/ideal',
        name: 'Base',
        component: Base,
      },
    ],
  },
  {
    path: '/vpn',
    name: 'MenuList',
    component: MenuList,
    children: [
      {
        path: 'user-info',
        name: 'userInfo',
        // component: () => import(/* webpackChunkName: "userInfo" */ '@/views/userInfo/index.vue'),certificate
        component: userInfo,
        meta: { title: '我的信息', icon: 'Document', parent: [{ name: 'Base', text: '首页' }, { name: 'userInfo', text: '我的信息' }] },
      },
      {
        path: 'cert-list',
        name: 'CertList',
        // component: () => import(/* webpackChunkName: "UserList" */ '@/views/UserList/index.vue'),certificate
        component: CertList,
        meta: { title: '证书列表', icon: 'Document', parent: [{ name: 'Base', text: '首页' }, { name: 'CertList', text: '证书列表' }] },
      },
      {
        path: 'user-list',
        name: 'UserList',
        // component: () => import(/* webpackChunkName: "UserList" */ '@/views/UserList/index.vue'),
        component: UserList,
        meta: { title: '用户列表', icon: 'Avatar', parent: [{ name: 'Base', text: '首页' }, { name: 'UserList', text: '用户列表' }] },
      },
      {
        path: 'resource',
        name: 'Resource',
        // component: () => import(/* webpackChunkName: "Settings" */ '@/views/Settings/index.vue'),
        component: Resource,
        meta: { title: '资源管理', icon: 'Folder', parent: [{ name: 'Base', text: '首页' }, { name: 'Settings', text: '资源管理' }] },
      },
      {
        path: 'group',
        name: 'Group',
        // component: () => import(/* webpackChunkName: "Settings" */ '@/views/Settings/index.vue'),
        component: Group,
        meta: { title: '资源组管理', icon: 'Files', parent: [{ name: 'Base', text: '首页' }, { name: 'Settings', text: '资源组管理' }] },
      },
      {
        path: 'client',
        name: 'Client',
        // component: () => import(/* webpackChunkName: "Settings" */ '@/views/Settings/index.vue'),
        component: Client,
        meta: { title: '客户端管理', icon: 'Monitor', parent: [{ name: 'Base', text: '首页' }, { name: 'Settings', text: '客户端管理' }] },
      },
      {
        path: 'settings',
        name: 'Settings',
        // component: () => import(/* webpackChunkName: "Settings" */ '@/views/Settings/index.vue'),
        component: Settings,
        meta: { title: '系统设置', icon: 'SetUp', parent: [{ name: 'Base', text: '首页' }, { name: 'Settings', text: '系统设置' }] },
      },
    ],
  },
  // {
  //   path: '/',
  //   name: 'Overview',
  //   component: Overview,
  // },
  {
    path: '/404',
    name: 'NotFind',
    component: NotFind,
  },
  {
    path: '/:pathMatch(.*)',
    redirect: '/404',
  },
]

const router = createRouter({
  // history: createWebHistory(process.env.BASE_URL),
  history: createWebHashHistory(process.env.BASE_URL),
  routes,
})
router.beforeEach((to, from, next) => {
  if (to.path !== '/login') {
    if (!store.state.headers['X-Auth-Metadata'] || !localStorage.getItem('headers')) {
      next({ path: '/login' })
    } else { next() }
  } else {
    next()
  }
})
export default router
