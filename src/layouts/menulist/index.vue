<template>
  <el-aside class="menulist-menu" width="fit-content">
    <el-header><div /></el-header>
    <el-scrollbar style="height: calc(100% - 60px);">
      <el-menu router :default-active="route.name" :collapse="isCollapse" menu-trigger="hover">
        <el-menu-item v-for="(item, index) in routeMap" :key="`menu-${index}`" :index="item.name" :route="{ name: item.name }">
          <el-icon><component :is="item.icon" /></el-icon>
          <template #title>
            {{ item.title }}
          </template>
        </el-menu-item>
      </el-menu>
    </el-scrollbar>
  </el-aside>
  <el-container class="menulist">
    <el-header>
      <el-button :icon="isCollapse ? Expand : Fold" :style="{ background: 'transparent', border: '0' }" @click="isCollapse = !isCollapse" class="collapse-btn" />
      <el-breadcrumb style="line-height: var(--el-header-height);">
        <el-breadcrumb-item replace :to="{ name: page.name }" v-for="(page, index) in menuParent" :key="index">
          {{ page.text }}
        </el-breadcrumb-item>
      </el-breadcrumb>
      <div class="status">
        <p>
          VPN 服务状态:
          <el-tag :type="server.install.indexOf('CONNECTED') > -1 ? 'success' : 'warning'">
            {{ server.install }}
          </el-tag>
        </p>
        <!-- <p>
          VPN登录模式:
          <el-tag :type="server.mode.indexOf('已开通') > -1 ? 'success' : 'warning'">
            {{ server.mode }}
          </el-tag>
        </p> -->
      </div>
      <div style="height:100%;display: flex;align-items: center;">
        <!-- <el-button type="primary" style="margin-left:50px" @click="exidLogin">退出登录</el-button> -->
        <el-popconfirm
          title="是否退出登录"
          confirm-button-text="确认"
          cancel-button-text="取消"
          @confirm="exidLogin"
        >
          <template #reference>
            <el-button type="primary" style="margin-left:50px">
              退出
            </el-button>
          </template>
        </el-popconfirm>
      </div>
    </el-header>
    <el-scrollbar height="100%">
      <el-main>
        <el-config-provider :locale="zhCN">
          <router-view />
        </el-config-provider>
      </el-main>
    </el-scrollbar>
  </el-container>
</template>

<script lang="ts">
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { defineComponent, Component, ref, Ref, computed, ComputedRef } from 'vue'
import { Router, RouteRecordNormalized, RouteLocationNormalizedLoaded, useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import * as Icon from '@element-plus/icons-vue'
import { Expand, Fold } from '@element-plus/icons-vue'
import zhCN from '@/locale/zh-cn'

interface TreeRouterMap {
  name: string,
  title: string,
  icon?: Component | null,
  children: TreeRouterMap[],
}

export default defineComponent({
  name: 'MenuListView',
  setup () {
    const store = useStore()

    const server = ref({
      install: '未知',
      mode: '未知',
    })
    const isCollapse: Ref<boolean> = ref(false)
    const router: Router = useRouter()
    const route: RouteLocationNormalizedLoaded = useRoute()

    
    store.dispatch('queryInitialCheck').then(
      function ({ message }) {
        server.value.install = message instanceof Array ? message.join() : message
      },
      function () {
        console.log('[ ERROR ]', ...arguments)
      },
    )
    // store.dispatch('checkCert').then(
    //   function ({ message }) {
    //     if (message instanceof Array) {
    //       server.value.mode = message.join()
    //     }
    //   },
    //   function () {
    //     console.log('[ ERROR ]', ...arguments)
    //   },
    // )

    const menuParent: ComputedRef<{name: string, text: string}[]> = computed(() => {
      return (((route.meta.parent as {name: string, text: string}[]) || []).length) ? ((route.meta.parent as {name: string, text: string}[]) || []) : [{ name: 'Base', text: '首页' }]
    })

    const routeMap: ComputedRef<TreeRouterMap[]> = computed(() => {
      function tree (route: RouteRecordNormalized | undefined): TreeRouterMap {
        if (route instanceof Array) {
          const icon = String(((route[0] || {}).meta || {}).icon || '')
          return {
            name: String((route[0] || {}).name || ''),
            title: String(((route[0] || {}).meta || {}).title || ''),
            icon: Object.prototype.hasOwnProperty.call(Icon, icon) ? (Icon as { [k: string]: Component })[icon] : null,
            children: (((route[0] || {}).children || []) as RouteRecordNormalized[]).map(r => tree(r)),
          }
        } else if (route && route instanceof Object) {
          const icon = String((route.meta || {}).icon || '')
          return {
            name: String(route.name || ''),
            title: String((route.meta || {}).title || ''),
            icon: Object.prototype.hasOwnProperty.call(Icon, icon) ? (Icon as { [k: string]: Component })[icon] : null,
            children: ((route.children || []) as RouteRecordNormalized[]).map(r => tree(r)),
          }
        } else {
          return {
            name: '',
            title: '',
            icon: null,
            children: [],
          }
        }
      }
      return (tree(router.getRoutes().find(({ name }) => name === 'MenuList')).children)
    })
    function exidLogin () {
      store.commit('CLEAR_HEADERS')
      router.push('/login')
    }
    return {
      Expand,
      Fold,
      exidLogin,
      routeMap,
      route,

      isCollapse,
      menuParent,
      server,
      router,

      zhCN,
    }
  },
})
</script>

<style lang="scss" scoped>
.el-aside {
  background-color: #2c3e50;
  color: white;
}
.menulist-menu {
  :deep(.el-menu):not(.el-menu--collapse) {
    .el-menu-item {
      width: 220px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
.collapse-btn {
  height: 100%;
  :deep(.el-icon) {
    transform: scale(1.5);
  }
}
.menulist {
  > :deep(.el-scrollbar) {
    height: 100%;
    > .el-scrollbar__wrap {
      > .el-scrollbar__view {
        min-height: 100%;
        display: flex;
        flex-wrap: nowrap;
        flex-direction: column;
      }
    }
  }
  .el-header {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    background: white;
  }
  .el-main {
    background-color: var(--el-svg-monochrome-grey);
    > * {
      background-color: white;
      height: 100%;
    }
  }
  .status {
    width: calc(100% - 180px);
    height: 100%;
    overflow: hidden;
    white-space: nowrap;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: center;
    align-content: flex-end;

    > * {
      display: flex;
      justify-content: space-between;
      align-content: center;
      align-items: center;
      box-sizing: border-box;
      // width: 275px;
      height: calc(var(--el-header-height) / 2);
      line-height: calc(var(--el-header-height) / 2);
      margin: 0;
    }
  }
}
</style>
