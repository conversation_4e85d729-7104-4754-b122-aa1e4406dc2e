<template>
  <div>IdealVpn</div>
</template>

<script lang="ts">
import { defineComponent, nextTick } from 'vue'
import { useRouter } from 'vue-router'

export default defineComponent({
  name: 'VDesktopRoot',
  setup () {
    const router = useRouter()
    nextTick(() => {
      const routers = router.getRoutes().map(v => ({ name: String(v.name), children: v.children.map(({ name }) => ({ name, children: [] })) || [] }))
      const name = (routers.find(({ name }) => name === 'MenuList')?.children || []).find(({ name }) => name)?.name
      name && router.replace({ name })
    })
  },
})
</script>
