<template>
  <router-view />
</template>

<style lang="scss">
@import "~@/styles/theme/index.scss";
html,
body,
#app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}
#app {
  font-size: var(--el-font-size-base);
  > * {
    box-sizing: border-box;
  }
}

// Adapted from https://gist.github.com/pentzzsolt/4949bbd7691d43d00616dc4f1451cae9#file-non-destructive-map-merge-4-scss
$transition: () !default;
@function map-deep-merge($parent-map, $child-map) {
  $result: $parent-map;

  @each $key, $child in $child-map {
    $parent-has-key: map-has-key($result, $key);
    $parent-value: map-get($result, $key);
    $parent-type: type-of($parent-value);
    $child-type: type-of($child);
    $parent-is-map: $parent-type == map;
    $child-is-map: $child-type == map;
    @if (not $parent-has-key) or ($parent-type != $child-type) or (not ($parent-is-map and $child-is-map)) {
      $result: map-merge($result, ( $key: $child ));
    } @else {
      $result: map-merge($result, ( $key: map-deep-merge($parent-value, $child) ));
    };
  };
  @return $result;
}
$transition: map-deep-merge(
  (
    'fast-out-slow-in': cubic-bezier(0.4, 0, 0.2, 1),
    'linear-out-slow-in': cubic-bezier(0, 0, 0.2, 1),
    'fast-out-linear-in': cubic-bezier(0.4, 0, 1, 1),
    'ease-in-out': cubic-bezier(0.4, 0, 0.6, 1),
    'fast-in-fast-out': cubic-bezier(0.25, 0.8, 0.25, 1),
    'swing': cubic-bezier(0.25, 0.8, 0.5, 1)
  ),
  $transition
);
$ripple-animation-transition-in: transform .25s map-get($transition, 'fast-in-slow-out'),
                                  opacity .1s map-get($transition, 'fast-in-slow-out') !default;
$ripple-animation-transition-out: opacity .3s map-get($transition, 'fast-in-slow-out') !default;
$ripple-animation-visible-opacity: .15 !default;

.v-ripple {
  &__container {
    color: inherit;
    border-radius: inherit;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    overflow: hidden;
    z-index: 0;
    pointer-events: none;
    contain: strict;
  }
  &__animation {
    color: inherit;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 50%;
    background: currentColor;
    opacity: 0;
    pointer-events: none;
    overflow: hidden;
    will-change: transform, opacity;
    &--enter {
      transition: none
    }
    &--in {
      transition: $ripple-animation-transition-in
    }
    &--out {
      transition: $ripple-animation-transition-out
    }
  }
}
</style>
