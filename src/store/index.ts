import { createStore } from 'vuex'
import { formApi, jsonApi } from '@/api'
import { AxiosPromise } from 'axios'
interface AxiosResponse<T> {
  exitCode: number;
  data?: T;
  message: string;
}

interface Cn {
  id: string,
  name: string,
  statusCd: 'V'|'R',
  status: string,
}

export default createStore({
  state: {
    headers: JSON.parse(localStorage.getItem('headers') || '{}'),
    cnList: [],
  },
  getters: {
    getHeaders (state) {
      return state.headers
    },
  },
  mutations: {
    setCnList (state, payload: Cn[]) {
      if (payload instanceof Array) (state.cnList as Cn[]) = payload
    },
    SET_HEADERS (state, payload: { [k: string]: string }) {
      Object.assign(state.headers, payload)
      localStorage.setItem('headers', JSON.stringify(state.headers))
    },
    CLEAR_HEADERS (state) {
      state.headers = {}
      localStorage.setItem('headers', JSON.stringify(state.headers))
    },
  },
  actions: {
    // 33查询当前登录用户信息
    selUser (_state, payload): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/current/queryUser',
        method: 'GET',
        data: payload,
      })
    },
    // 查询当前用户的链接记录链接记录 - http://*************:3000/project/80/interface/api/11237
    getRecord (_state, payload): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/current/connRecord',
        method: 'POST',
        data: payload,
      })
    },
    // 为当前登录用户创建QR码
    createQR (_state, payload): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/current/createQrCode',
        method: 'GET',
        data: payload,
      })
    },
    // 验证当前用户QR码
    verifyQrCode (_state, payload): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/current/verifyQrCode?otp=' + payload,
        method: 'GET',
        data: {},
      })
    },
    // 33管理员创建动态码
    // 创建QR码
    admcQR (_state, payload): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/totp/createQrCode?userCode=' + payload,
        method: 'GET',
        data: {},
      })
    },
    // 验证QR码
    selcQR (_state, payload): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/totp/verifyQrCode?userCode=' + payload.userCode + '&otp=' + payload.otp,
        method: 'GET',
        data: {},
      })
    },
    // 重置密码
    rootPwd (_state, payload): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return formApi({
        url: '/user/resetPasswd?userId=' + payload.userId + '&pwd=' + payload.pwd,
        method: 'POST',
        data: {},
      })
    },
    // 用户登录
    /**
     * 查询VPN用户 - /api/addUser
     * @link http://192.168.1.181:3000/project/168/interface/api/11343
     */
    loginUser (_state, payload): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/system/login',
        method: 'POST',
        data: { ...payload },
      })
    },
    // 333
    // Vpn证书管理
    /**
     * 检查 openvpn 是否安装 - /api/initialCheck
     * @link http://192.168.1.181:3000/project/168/interface/api/11335
     */
    queryInitialCheck (_state, payload = {}): AxiosPromise<AxiosResponse<unknown>> {
      return jsonApi({
        url: '/vpn/status',
        method: 'GET',
        params: payload,
      })
    },
    /**
     * 检查是否开通了 用户-密码登录模式 - /api/userCheck
     * @link http://192.168.1.181:3000/project/168/interface/api/11345
     */
    checkCert (_state, payload = {}): AxiosPromise<AxiosResponse<unknown>> {
      return jsonApi({
        url: '/vpn/userCheck',
        method: 'GET',
        params: payload,
      })
    },
    /**
     * 查询证书 - /api/queryConfs
     * @link http://192.168.1.181:3000/project/168/interface/api/11337
     */
    // queryConfs (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
    //   return jsonApi({
    //     url: '/vpn/queryConfs',
    //     method: 'GET',
    //     params: payload,
    //   })
    // },
    queryConfs (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/cer/query',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 添加证书 - /api/newClient
     * @link http://192.168.1.181:3000/project/168/interface/api/11337
     */
    createConf (_state, payload = { userName: '', passwd: '' }): AxiosPromise<AxiosResponse<null>> {
      return jsonApi({
        url: '/cer/create',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 删除证书 - /api/revokeClient
     * @link http://192.168.1.181:3000/project/168/interface/api/11337
     */
    revokeConf (_state, payload = { cn: '' }): AxiosPromise<AxiosResponse<null>> {
      return jsonApi({
        url: '/cer/revokeClient',
        method: 'GET',
        params: payload,
      })
    },
    /**
     * 下载证书配置文件 - /api/downConf
     * @link http://192.168.1.181:3000/project/168/interface/api/11343
     */
    downConf (_state, payload = {}): AxiosPromise<AxiosResponse<null>> {
      return jsonApi({
        url: '/cer/downConf',
        method: 'GET',
        params: payload,
        responseType: 'blob',
      })
    },
    /**
     * 下载当前登录用户的证书 - /api/current/downConf
     * @link http://*************:3000/project/80/interface/api/10477
     */
    downCurrentUserConf (_state, payload = {}): AxiosPromise<AxiosResponse<null>> {
      return jsonApi({
        url: '/current/downConf',
        method: 'GET',
        params: payload,
        responseType: 'blob',
      })
    },

    // Vpn用户管理
    /**
     * 查询VPN用户 - /api/addUser
     * @link http://192.168.1.181:3000/project/168/interface/api/11343
     */
    queryUser (_state, payload = { pageNumber: 1, pageSize: 30 }): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/user/query',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 添加一个VPN用户 - /api/addUser
     * @link http://192.168.1.181:3000/project/168/interface/api/11343
     */
    createUser (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/user/add',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 修改用户密码 - /api/modPsw
     * @link http://192.168.1.181:3000/project/168/interface/api/11343
     */
    modifyUser (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/modPsw',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 删除一个VPN用户 - /api/delUser
     * @link http://192.168.1.181:3000/project/168/interface/api/11343
     */
    removeUser (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/user/invalid',
        method: 'GET',
        params: payload,
      })
    },
    /**
     * 删除一个VPN用户 - /api/delUser
     * @link http://192.168.1.181:3000/project/168/interface/api/11343
     */
    editmoUser (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/user/mod',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 查询客户端链接记录 - /api/delUser
     * @link http://*************:3000/project/80/interface/api/11232
     */
    queryConnRecord (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/vpn/queryConnRecord',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 查看 IP 租约表 - /api/vpn/leases
     * @link http://*************:3000/project/80/interface/api/11217
     */
    queryLeases (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/vpn/leases',
        method: 'GET',
        params: { userCode: payload.userCode },
      })
    },
    /**
     * 分页查询资源列表 - /api/resource/query/page
     * @link http://*************:3000/project/80/interface/api/34597
     */
    queryResource (_state, payload = { pageNumber: 0, pageSize: 30 }): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/resource/query/page',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 分页查询资源列表 - /api/resource/query/list
     * @link http://*************:3000/project/80/interface/api/34597
     */
    queryResourceList (_state, payload = { pageNumber: 0, pageSize: 30 }): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/resource/query/list',
        method: 'GET',
        params: payload,
      })
    },
    /**
     * 新增资源 - /api/resource/add
     * @link http://*************:3000/project/80/interface/api/34597
     */
    addResource (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/resource/add',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 修改资源 - /api/resource/mod
     * @link http://*************:3000/project/80/interface/api/34597
     */
    modResource (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/resource/mod',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 删除资源 - /api/resource/del
     * @link http://*************:3000/project/80/interface/api/34597
     */
    delResource (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/resource/del',
        method: 'GET',
        params: payload,
      })
    },
    /**
     * 查询资源组列表 - /api/group/query/list
     * @link http://*************:3000/project/80/interface/api/34597
     */
    queryResourceGroup (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/group/query/list',
        method: 'GET',
        params: payload,
      })
    },
    /**
     * 删除资源组 - /api/group/del
     * @link http://*************:3000/project/80/interface/api/34597
     */
    delGroup (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/group/del',
        method: 'GET',
        params: payload,
      })
    },
    /**
     * 分页查询资源组列表 - /api/group/query/page
     * @link http://*************:3000/project/80/interface/api/34597
     */
    queryGroupPage (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/group/query/page',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 新增资源组 - /api/group/add
     * @link http://*************:3000/project/80/interface/api/34597
     */
    addGroup (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/group/add',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 修改资源组 - /api/group/mod
     * @link http://*************:3000/project/80/interface/api/34597
     */
    modGroup (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/group/mod',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 向资源组添加资源 - /api/group/add/resource
     * @link http://*************:3000/project/80/interface/api/34597
     */
    addResourceForGroup (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/group/add/resource',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 从资源组移除资源 - /api/group/del/resource
     * @link http://*************:3000/project/80/interface/api/34597
     */
    delResourceForGroup (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/group/del/resource',
        method: 'GET',
        params: payload,
      })
    },
    /**
     * 查询客户端链接状态 - /api/client/query/status
     * @link http://*************:3000/project/80/interface/api/34597
     */
    clientQueryStatus (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/client/query/status',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 查询客户端链接状态 - /api/client/query/record
     * @link http://*************:3000/project/80/interface/api/34597
     */
    clientQueryRecord (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/client/query/record',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 添加一个用户配置 - /api/user/config/add
     * @link http://*************:3000/project/80/interface/api/34597
     */
    userConfigAdd (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/user/config/add',
        method: 'POST',
        data: payload,
      })
    },
    /**
     * 删除一个用户配置 - /api/user/config/del
     * @link http://*************:3000/project/80/interface/api/34597
     */
    userConfigDel (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/user/config/del',
        method: 'GET',
        params: payload,
      })
    },
    /**
     * 查询用户配置 - /api/user/config/query
     * @link http://*************:3000/project/80/interface/api/34597
     */
    userConfigQuery (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/user/config/query',
        method: 'GET',
        params: payload,
      })
    },
    // 激活用户
    userActive (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/user/active',
        method: 'GET',
        params: payload,
      })
    },
    // 删除用户
    userdel (_state, payload = {}): AxiosPromise<AxiosResponse<{id: string, name: string, statusCd: 'V'|'R', status: string}[]>> {
      return jsonApi({
        url: '/user/del',
        method: 'GET',
        params: payload,
      })
    },
  },
  modules: {
  },
})
