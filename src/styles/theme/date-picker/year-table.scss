@use '../mixins/mixins' as *;
@use '../common/var' as *;

@include b(year-table) {
  font-size: 12px;
  margin: -1px;
  border-collapse: collapse;

  .#{$namespace}-icon {
    color: getCssVar('datepicker', 'icon-color');
  }

  td {
    text-align: center;
    padding: 20px 3px;
    cursor: pointer;

    &.today {
      .cell {
        color: getCssVar('color', 'primary');
        font-weight: bold;
      }
    }

    &.disabled .cell {
      background-color: getCssVar('fill-color', 'light');
      cursor: not-allowed;
      color: getCssVar('text-color', 'placeholder');

      &:hover {
        color: getCssVar('text-color', 'placeholder');
      }
    }

    .cell {
      width: 48px;
      height: 36px;
      display: block;
      line-height: 36px;
      color: getCssVar('datepicker-text-color');
      border-radius: 18px;
      margin: 0 auto;

      &:hover {
        color: getCssVar('datepicker-hover-text-color');
      }
    }

    &.current:not(.disabled) .cell {
      color: getCssVar('datepicker-active-color');
    }

    &:focus-visible {
      outline: none;
      .cell {
        outline: 2px solid getCssVar('datepicker-active-color');
      }
    }
  }
}
