@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(select-dropdown) {
  z-index: calc(#{getCssVar('index-top')} + 1);
  border-radius: getCssVar('border-radius-base');
  box-sizing: border-box;

  .#{$namespace}-scrollbar.is-empty .#{$namespace}-select-dropdown__list {
    padding: 0;
  }

  @include e(option-item) {
    @include when(selected) {
      &:not(.is-multiple) {
        @include when(disabled) {
          color: getCssVar('text-color-disabled');
          &::after {
            background-color: getCssVar('text-color-disabled');
          }
        }
      }
    }

    &:hover {
      &:not(.hover) {
        background-color: transparent;
      }
    }
  }

  @include when(multiple) {
    .#{$namespace}-select-dropdown__option-item {
      @include when(disabled) {
        @include when(selected) {
          color: getCssVar('text-color-disabled');
        }
      }
    }
  }
}

@include b(select-dropdown__empty) {
  padding: map.get($select-dropdown, 'empty-padding');
  margin: 0;
  text-align: center;
  color: map.get($select-dropdown, 'empty-color');
  font-size: getCssVar('select-font-size');
}

@include b(select-dropdown__wrap) {
  max-height: map.get($select-dropdown, 'max-height');
}

@include b(select-dropdown__list) {
  list-style: none;
  margin: map.get($select-dropdown, 'padding') !important;
  padding: 0 !important;
  box-sizing: border-box;
}
