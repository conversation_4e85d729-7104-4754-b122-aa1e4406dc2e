@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/utils' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;
@use './select-dropdown.scss';
@use './select/common.scss' as *;

@mixin select-tag-normal {
  white-space: normal;
  z-index: getCssVar('index-normal');
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  cursor: pointer;

  .#{$namespace}-tag {
    box-sizing: border-box;
    border-color: transparent;
    margin: 2px 6px 2px 0;
    &:last-child {
      margin-right: 0;
    }

    .#{$namespace}-icon-close {
      background-color: getCssVar('text-color', 'placeholder');
      right: -7px;
      top: 0;
      color: $color-white;

      &:hover {
        background-color: getCssVar('text-color', 'secondary');
      }

      &::before {
        display: block;
        transform: translate(0, 0.5px);
      }
    }
  }
  .#{$namespace}-tag--info {
    background-color: getCssVar('fill-color');
  }
}

@include b(select) {
  @include set-component-css-var('select', $select);
}

@include b(select) {
  display: inline-block;
  position: relative;
  vertical-align: middle;
  line-height: map.get($input-height, 'default');

  @include e(popper) {
    @include picker-popper(
      map.get($select-dropdown, 'bg-color'),
      map.get($select-dropdown, 'border'),
      map.get($select-dropdown, 'shadow')
    );
  }

  .#{$namespace}-select-tags-wrapper {
    &.has-prefix {
      margin-left: map.get($select-tags-prefix-padding, 'default');
    }
  }

  @each $size in (large, small) {
    @include m($size) {
      line-height: map.get($input-height, $size);
      .#{$namespace}-select-tags-wrapper {
        &.has-prefix {
          margin-left: map.get($select-tags-prefix-padding, $size);
        }
      }
    }
  }

  .#{$namespace}-select__tags > span {
    display: inline-block;
  }

  &:hover:not(.#{$namespace}-select--disabled) {
    .#{$namespace}-input__wrapper {
      box-shadow: 0 0 0 1px getCssVar('select-border-color-hover') inset;
    }
  }

  @include select-common(select);

  .#{$namespace}-input__wrapper {
    cursor: pointer;

    @include when(focus) {
      @include inset-input-border(
        getCssVar('select-input-focus-border-color'),
        true
      );
    }
  }

  .#{$namespace}-input__inner {
    cursor: pointer;

    &::selection {
      outline: none;
    }
  }

  .#{$namespace}-input {
    display: flex;

    & .#{$namespace}-select__caret {
      color: getCssVar('select-input-color');
      font-size: getCssVar('select-input-font-size');
      transition: transform getCssVar('transition-duration');
      transform: rotateZ(0deg);
      cursor: pointer;

      @include when(reverse) {
        transform: rotateZ(-180deg);
      }

      @include when(show-close) {
        font-size: getCssVar('select-font-size');
        text-align: center;
        transform: rotateZ(0deg);
        border-radius: getCssVar('border-radius-circle');
        color: getCssVar('select-input-color');
        transition: getCssVar('transition', 'color');

        &:hover {
          color: getCssVar('select-close-hover-color');
        }
      }

      &.#{$namespace}-icon {
        position: relative;
        height: inherit;
        z-index: 2;
      }
    }

    &.is-disabled {
      & .#{$namespace}-input__wrapper {
        cursor: not-allowed;

        &:hover {
          @include inset-input-border(#{getCssVar('select-disabled-border')});
        }
      }
      & .#{$namespace}-input__inner {
        cursor: not-allowed;
      }
      & .#{$namespace}-select__caret {
        cursor: not-allowed;
      }
    }

    &.is-focus .#{$namespace}-input__wrapper {
      @include inset-input-border(
        getCssVar('select-input-focus-border-color'),
        true
      );
    }
  }

  @include e(input) {
    border: none;
    outline: none;
    padding: 0;
    margin-left: 15px;
    color: getCssVar('select-multiple-input-color');
    font-size: getCssVar('select-font-size');
    appearance: none;
    height: 28px;
    background-color: transparent;

    @include when(small) {
      height: 14px;
    }
  }

  @include e(close) {
    cursor: pointer;
    position: absolute;
    top: 8px;
    z-index: getCssVar('index-top');
    right: 25px;
    color: getCssVar('select-input-color');
    line-height: 18px;
    font-size: getCssVar('select-input-font-size');

    &:hover {
      color: getCssVar('select-close-hover-color');
    }
  }

  @include e(tags) {
    position: absolute;
    line-height: normal;
    top: 50%;
    transform: translateY(-50%);
    @include select-tag-normal;
  }

  @include e(collapse-tags) {
    @include select-tag-normal;
  }

  @include e(collapse-tag) {
    line-height: inherit;
    height: inherit;
    display: flex;
  }
}
