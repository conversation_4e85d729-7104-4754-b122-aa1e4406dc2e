@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(avatar) {
  @include set-component-css-var('avatar', $avatar);
  @include set-component-css-var('avatar-size', $avatar-size);

  display: inline-flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  text-align: center;
  overflow: hidden;
  color: var(--el-avatar-text-color);
  background: var(--el-avatar-bg-color);
  width: var(--el-avatar-size);
  height: var(--el-avatar-size);
  font-size: var(--el-avatar-text-size);

  > img {
    display: block;
    height: 100%;
  }

  @include m(circle) {
    border-radius: 50%;
  }

  @include m(square) {
    border-radius: var(--el-avatar-border-radius);
  }

  @include m(icon) {
    font-size: var(--el-avatar-icon-size);
  }

  @each $size in (small, default, large) {
    @include m($size) {
      --el-avatar-size: #{map.get($avatar-size, $size)};
    }
  }
}
