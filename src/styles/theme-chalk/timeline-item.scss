@use 'mixins/mixins' as *;
@use 'common/var' as *;

@include b(timeline-item) {
  position: relative;
  padding-bottom: 20px;

  @include e(wrapper) {
    position: relative;
    padding-left: 28px;
    top: -3px;
  }

  @include e(tail) {
    position: absolute;
    left: 4px;
    height: 100%;
    border-left: 2px solid var(--el-timeline-node-color);
  }

  @include e(icon) {
    color: var(--el-color-white);
    font-size: var(--el-font-size-small);
  }

  @include e(node) {
    position: absolute;
    background-color: var(--el-timeline-node-color);
    border-color: var(--el-timeline-node-color);
    border-radius: 50%;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;

    @include m(normal) {
      left: -1px;
      width: var(--el-timeline-node-size-normal);
      height: var(--el-timeline-node-size-normal);
    }
    @include m(large) {
      left: -2px;
      width: var(--el-timeline-node-size-large);
      height: var(--el-timeline-node-size-large);
    }
    @include when(hollow) {
      background: var(--el-color-white);
      border-style: solid;
      border-width: 2px;
    }

    @each $type in (primary, success, warning, danger, info) {
      @include m($type) {
        background-color: var(--el-color-#{$type});
        border-color: var(--el-color-#{$type});
      }
    }
  }

  @include e(dot) {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  @include e(content) {
    color: var(--el-text-color-primary);
  }

  @include e(timestamp) {
    color: var(--el-text-color-secondary);
    line-height: 1;
    font-size: var(--el-font-size-small);

    @include when(top) {
      margin-bottom: 8px;
      padding-top: 4px;
    }
    @include when(bottom) {
      margin-top: 8px;
    }
  }
}
