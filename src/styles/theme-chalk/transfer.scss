@use 'sass:math';
@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/utils' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(transfer) {
  @include set-component-css-var('transfer', $transfer);
}

@include b(transfer) {
  font-size: var(--el-font-size-base);

  @include e(buttons) {
    display: inline-block;
    vertical-align: middle;
    padding: 0 30px;
  }

  @include e(button) {
    &:first-child {
      margin-bottom: 10px;
    }

    &:nth-child(2) {
      margin: 0 0 0 10px;
    }

    i,
    span {
      font-size: 14px;
    }

    & .#{$namespace}-icon + span {
      margin-left: 0;
    }
  }
}

@include b(transfer-panel) {
  border: 1px solid var(--el-transfer-border-color);
  border-radius: var(--el-transfer-border-radius);
  overflow: hidden;
  background: var(--el-color-white);
  display: inline-block;
  text-align: left;
  vertical-align: middle;
  width: var(--el-transfer-panel-width);
  max-height: 100%;
  box-sizing: border-box;
  position: relative;

  @include e(body) {
    height: var(--el-transfer-panel-body-height);

    @include when(with-footer) {
      padding-bottom: var(--el-transfer-panel-footer-height);
    }
  }

  @include e(list) {
    margin: 0;
    padding: 6px 0;
    list-style: none;
    height: var(--el-transfer-panel-body-height);
    overflow: auto;
    box-sizing: border-box;

    @include when(filterable) {
      height: calc(
        var(--el-transfer-panel-body-height) - var(--el-transfer-filter-height) -
          20px
      );
      padding-top: 0;
    }
  }

  @include e(item) {
    height: var(--el-transfer-item-height);
    line-height: var(--el-transfer-item-height);
    padding-left: 15px;
    display: block !important;

    & + .#{$namespace}-transfer-panel__item {
      margin-left: 0;
    }

    &.#{$namespace}-checkbox {
      color: var(--el-text-color-regular);
    }

    &:hover {
      color: var(--el-color-primary);
    }

    &.#{$namespace}-checkbox .#{$namespace}-checkbox__label {
      width: 100%;
      @include utils-ellipsis;
      display: block;
      box-sizing: border-box;
      padding-left: 24px;
      line-height: var(--el-transfer-item-height);
    }

    .#{$namespace}-checkbox__input {
      position: absolute;
      top: 8px;
    }
  }

  @include e(filter) {
    text-align: center;
    margin: 15px;
    box-sizing: border-box;
    display: block;
    width: auto;

    .#{$namespace}-input__inner {
      height: var(--el-transfer-filter-height);
      width: 100%;
      font-size: 12px;
      display: inline-block;
      box-sizing: border-box;
      border-radius: calc(var(--el-transfer-filter-height) / 2);
      padding-right: 10px;
      padding-left: 30px;
    }

    .#{$namespace}-input__icon {
      margin-left: 5px;
    }

    .#{$namespace}-icon-circle-close {
      cursor: pointer;
    }
  }

  .#{$namespace}-transfer-panel__header {
    height: var(--el-transfer-panel-header-height);
    line-height: var(--el-transfer-panel-header-height);
    background: var(--el-transfer-panel-header-bg-color);
    margin: 0;
    padding-left: 15px;
    border-bottom: 1px solid var(--el-transfer-border-color);
    box-sizing: border-box;
    color: var(--el-color-black);

    .#{$namespace}-checkbox {
      display: block;
      line-height: 40px;

      .#{$namespace}-checkbox__label {
        font-size: 16px;
        color: var(--el-text-color-primary);
        font-weight: normal;

        span {
          position: absolute;
          right: 15px;
          color: var(--el-text-color-secondary);
          font-size: 12px;
          font-weight: normal;
        }
      }
    }
  }

  .#{$namespace}-transfer-panel__footer {
    height: var(--el-transfer-panel-footer-height);
    background: var(--el-color-white);
    margin: 0;
    padding: 0;
    border-top: 1px solid var(--el-transfer-border-color);
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: var(--el-index-normal);
    @include utils-vertical-center;

    .#{$namespace}-checkbox {
      padding-left: 20px;
      color: var(--el-text-color-regular);
    }
  }

  .#{$namespace}-transfer-panel__empty {
    margin: 0;
    height: var(--el-transfer-item-height);
    line-height: var(--el-transfer-item-height);
    padding: 6px 15px 0;
    color: var(--el-text-color-secondary);
    text-align: center;
  }

  .#{$namespace}-checkbox__label {
    padding-left: 8px;
  }

  .#{$namespace}-checkbox__inner {
    height: 14px;
    width: 14px;
    border-radius: 3px;
    &::after {
      height: 6px;
      width: 3px;
      left: 4px;
    }
  }
}
