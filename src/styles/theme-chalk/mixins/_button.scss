@use 'sass:map';

@use '../common/var' as *;

@mixin button-plain($color, $type) {
  --el-button-text-color: var(--el-color-#{$type});
  --el-button-bg-color: #{map.get($colors, $type, 'light-9')};
  --el-button-border-color: #{map.get($colors, $type, 'light-6')};

  --el-button-hover-text-color: var(--el-color-white);
  --el-button-hover-bg-color: var(--el-color-#{$type});
  --el-button-hover-border-color: var(--el-color-#{$type});

  --el-button-active-text-color: var(--el-color-white);
  --el-button-active-border-color: var(--el-color-#{$type});

  &.is-disabled {
    &,
    &:hover,
    &:focus,
    &:active {
      color: map.get($colors, $type, 'light-4');
      background-color: map.get($colors, $type, 'light-9');
      border-color: map.get($colors, $type, 'light-8');
    }
  }
}

@mixin button-variant($type) {
  --el-button-text-color: var(--el-color-white);
  --el-button-hover-text-color: var(--el-color-white);
  --el-button-disabled-text-color: var(--el-color-white);
  &.is-plain {
    @include button-plain(var(--el-button-bg-color), $type);
  }
}

@mixin button-size(
  $padding-vertical,
  $padding-horizontal,
  $font-size,
  $border-radius
) {
  padding: $padding-vertical $padding-horizontal;
  font-size: $font-size;
  border-radius: $border-radius;
  &.is-round {
    padding: $padding-vertical $padding-horizontal;
  }
}
