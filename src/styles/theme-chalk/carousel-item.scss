@use 'mixins/mixins' as *;
@use 'common/var' as *;

@include b(carousel) {
  @include e(item) {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: inline-block;
    overflow: hidden;
    z-index: calc(var(--el-index-normal) - 1);

    @include when(active) {
      z-index: calc(var(--el-index-normal) - 1);
    }

    @include when(animating) {
      transition: transform 0.4s ease-in-out;
    }

    @include m(card) {
      width: 50%;
      transition: transform 0.4s ease-in-out;
      &.is-in-stage {
        cursor: pointer;
        z-index: var(--el-index-normal);
        &:hover .#{$namespace}-carousel__mask,
        &.is-hover .#{$namespace}-carousel__mask {
          opacity: 0.12;
        }
      }
      &.is-active {
        z-index: calc(var(--el-index-normal) + 1);
      }
    }
  }

  @include e(mask) {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: $color-white;
    opacity: 0.24;
    transition: var(--el-transition-duration-fast);
  }
}
