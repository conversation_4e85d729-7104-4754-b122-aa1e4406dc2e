@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(select-dropdown) {
  z-index: calc(var(--el-index-top) + 1);
  border-radius: var(--el-border-radius-base);
  box-sizing: border-box;

  .#{$namespace}-scrollbar.is-empty .#{$namespace}-select-dropdown__list {
    padding: 0;
  }

  @include e(option-item) {
    &:hover {
      &:not(.hover) {
        background-color: transparent;
      }
    }
  }
}

@include b(select-dropdown__empty) {
  padding: map.get($select-dropdown, 'empty-padding');
  margin: 0;
  text-align: center;
  color: map.get($select-dropdown, 'empty-color');
  font-size: var(--el-select-font-size);
}

@include b(select-dropdown__wrap) {
  max-height: map.get($select-dropdown, 'max-height');
}

@include b(select-dropdown__list) {
  list-style: none;
  margin: map.get($select-dropdown, 'padding') !important;
  padding: 0 !important;
  box-sizing: border-box;
}
