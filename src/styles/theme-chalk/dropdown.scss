@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

$dropdown-item-line-height: () !default;
$dropdown-item-line-height: map.merge(
  (
    'large': 22px,
    'default': 22px,
    'small': 20px,
  ),
  $dropdown-item-line-height
);

$dropdown-item-padding: () !default;
$dropdown-item-padding: map.merge(
  (
    'large': 7px 20px,
    'default': 5px 16px,
    'small': 2px 12px,
  ),
  $dropdown-item-padding
);

$dropdown-caret-width: () !default;
$dropdown-caret-width: map.merge($common-component-size, $dropdown-caret-width);

$dropdown-divider-width: 1px !default;

@include b(dropdown) {
  @include set-component-css-var('dropdown', $dropdown);

  display: inline-flex;
  position: relative;
  color: var(--el-text-color-regular);
  font-size: var(--el-font-size-base);
  line-height: 1;

  @include e(popper) {
    @include set-component-css-var('dropdown', $dropdown);

    // using attributes selector to override

    @include picker-popper(
      $color-white,
      1px solid var(--el-border-color-light),
      var(--el-dropdown-menu-box-shadow)
    );

    .#{$namespace}-dropdown-menu {
      border: none;
    }

    #{& + '-selfdefine'} {
      outline: none;
    }

    @include b(scrollbar__bar) {
      z-index: calc(var(--el-dropdown-menu-index) + 1);
    }

    @include b(dropdown__list) {
      list-style: none;
      padding: 0;
      margin: 0;
      box-sizing: border-box;
    }
  }

  & .#{$namespace}-dropdown__caret-button {
    padding-left: 0;
    padding-right: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: map.get($dropdown-caret-width, 'default');
    border-left: none;

    > span {
      display: inline-flex;
    }

    &::before {
      $gap: 5px;

      content: '';
      position: absolute;
      display: block;
      width: 1px;
      top: $gap;
      bottom: $gap;
      left: 0;
      background: rgba(0, 0, 0, 0.5);
    }

    &.#{$namespace}-button::before {
      background: var(--el-border-color-base);
      opacity: 0.5;
    }

    &:hover {
      &::before {
        top: 0;
        bottom: 0;
      }
    }

    & .#{$namespace}-dropdown__icon {
      font-size: inherit;
      padding-left: 0;
    }
  }
  @include e(icon) {
    font-size: 12px;
    margin: 0 3px;
  }

  .#{$namespace}-dropdown-selfdefine {
    // 自定义
    outline: none;
  }

  @each $size in (large, small) {
    @include m($size) {
      .#{$namespace}-dropdown__caret-button {
        width: map.get($dropdown-caret-width, $size);
      }
    }
  }
}

$dropdown-menu-padding-vertical: () !default;
$dropdown-menu-padding-vertical: map.merge(
  (
    'large': 8px,
    'default': 6px,
    'small': 4px,
  ),
  $dropdown-menu-padding-vertical
);

@include b(dropdown-menu) {
  position: relative;
  top: 0;
  left: 0;
  z-index: var(--el-dropdown-menu-index);
  padding: map.get($dropdown-menu-padding-vertical, 'default')-$border-width-base
    0;
  margin: 0;
  background-color: $color-white;
  border: none;
  border-radius: var(--el-border-radius-base);
  box-shadow: none;

  @include e(item) {
    display: flex;
    align-items: center;
    list-style: none;
    line-height: map.get($dropdown-item-line-height, 'default');
    padding: map.get($dropdown-item-padding, 'default');
    margin: 0;
    font-size: var(--el-font-size-base);
    color: var(--el-text-color-regular);
    cursor: pointer;
    outline: none;
    &:not(.is-disabled):hover,
    &:focus {
      background-color: var(--el-dropdown-menuItem-hover-fill);
      color: var(--el-dropdown-menuItem-hover-color);
    }

    i {
      margin-right: 5px;
    }

    @include m(divided) {
      $divided-offset: 6px;

      position: relative;
      margin-top: $divided-offset;
      border-top: 1px solid var(--el-border-color-lighter);

      &:before {
        content: '';
        height: $divided-offset;
        display: block;
        background-color: $color-white;
      }
    }

    @include when(disabled) {
      cursor: not-allowed;
      color: var(--el-text-color-disabled-base);
    }
  }

  @each $size in (large, small) {
    @include m($size) {
      padding: map.get($dropdown-menu-padding-vertical, $size)-$border-width-base
        0;

      @include e(item) {
        padding: map.get($dropdown-item-padding, $size);
        line-height: map.get($dropdown-item-line-height, $size);
        font-size: map.get($input-font-size, $size);

        &.#{$namespace}-dropdown-menu__item--divided {
          $divided-offset: 3px;
          margin-top: $divided-offset;

          &:before {
            height: $divided-offset;
          }
        }
      }
    }
  }
}
