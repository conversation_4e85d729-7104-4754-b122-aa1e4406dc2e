@use 'mixins/mixins' as *;
@use 'common/var' as *;

@include b(page-header) {
  display: flex;
  line-height: 24px;

  @include e(left) {
    display: flex;
    cursor: pointer;
    margin-right: 40px;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      width: 1px;
      height: 16px;
      right: -20px;
      top: 50%;
      transform: translateY(-50%);
      background-color: var(--el-border-color-base);
    }

    @include e(icon) {
      font-size: 18px;
      margin-right: 6px;
      display: flex;
      align-items: center;

      .el-icon {
        font-size: inherit;
      }
    }

    @include e(title) {
      font-size: 14px;
      font-weight: 500;
    }
  }

  @include e(content) {
    font-size: 18px;
    color: var(--el-text-color-primary);
  }
}
