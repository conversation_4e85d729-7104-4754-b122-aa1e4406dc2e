@use 'sass:map';

@use 'common/var' as *;
@use 'mixins/button' as *;
@use 'mixins/mixins' as *;
@use 'mixins/utils' as *;
@use 'mixins/var' as *;

@include b(button) {
  @include set-component-css-var('button', $button);
}

$button-icon-span-gap: () !default;
$button-icon-span-gap: map.merge(
  (
    'large': 8px,
    'default': 6px,
    'small': 4px,
  ),
  $button-icon-span-gap
);

@include b(button) {
  display: inline-flex;
  justify-content: center;
  align-items: center;

  line-height: 1;
  // min-height will expand when in flex
  height: map.get($input-height, 'default');
  white-space: nowrap;
  cursor: pointer;
  background-color: var(--el-button-bg-color, #{map.get($button, 'bg-color')});
  border: var(--el-border-base);
  border-color: var(
    --el-button-border-color,
    #{map.get($button, 'border-color')}
  );
  color: var(--el-button-text-color, #{map.get($button, 'text-color')});
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: none;
  margin: 0;
  transition: 0.1s;
  font-weight: var(--el-button-font-weight);
  user-select: none;

  > span {
    display: inline-flex;
    align-items: center;
  }

  & + & {
    margin-left: 12px;
  }

  @include button-size(
    map.get($button-padding-vertical, 'default') - $button-border-width,
    map.get($button-padding-horizontal, 'default') - $button-border-width,
    map.get($button-font-size, 'default'),
    map.get($button-border-radius, 'default')
  );

  &:hover,
  &:focus {
    color: var(--el-button-hover-text-color);
    border-color: var(
      --el-button-hover-border-color,
      var(--el-button-hover-bg-color)
    );
    background-color: var(--el-button-hover-bg-color);
    outline: none;
  }

  &:active {
    color: var(--el-button-active-text-color);
    border-color: var(
      --el-button-active-border-color,
      var(--el-button-active-bg-color)
    );
    background-color: var(
      --el-button-active-bg-color,
      var(--el-button-bg-color)
    );
    outline: none;
  }

  &::-moz-focus-inner {
    border: 0;
  }

  & [class*='#{$namespace}-icon'] {
    & + span {
      margin-left: map.get($button-icon-span-gap, 'default');
    }
    svg {
      vertical-align: bottom;
    }
  }

  @include when(plain) {
    --el-button-active-text-color: #{mix(
        $color-black,
        $color-primary,
        $button-active-shade-percent
      )};
    --el-button-active-border-color: #{mix(
        $color-black,
        $color-primary,
        $button-active-shade-percent
      )};

    --el-button-hover-text-color: var(--el-color-primary);
    --el-button-hover-bg-color: var(--el-color-white);
    --el-button-hover-border-color: var(--el-color-primary);
  }

  @include when(active) {
    color: var(--el-button-active-text-color);
    border-color: var(
      --el-button-active-border-color,
      --el-button-active-bg-color
    );
    background-color: var(--el-button-active-bg-color);
    outline: none;
  }

  @include when(disabled) {
    &,
    &:hover,
    &:focus {
      color: var(--el-button-disabled-text-color);
      cursor: not-allowed;
      background-image: none;
      background-color: var(--el-button-disabled-bg-color);
      border-color: var(--el-button-disabled-border-color);
    }

    &.#{$namespace}-button--text {
      background-color: transparent;
    }

    &.is-plain {
      &,
      &:hover,
      &:focus {
        background-color: var(--el-color-white);
        border-color: var(--el-button-disabled-border-color);
        color: var(--el-button-disabled-text-color);
      }
    }
  }

  @include when(loading) {
    position: relative;
    pointer-events: none;

    &:before {
      pointer-events: none;
      content: '';
      position: absolute;
      left: -1px;
      top: -1px;
      right: -1px;
      bottom: -1px;
      border-radius: inherit;
      background-color: rgba(255, 255, 255, 0.35);
    }
  }
  @include when(round) {
    border-radius: var(--el-border-radius-round);
  }
  @include when(circle) {
    border-radius: 50%;
    padding: map.get($button-padding-vertical, 'default') - $button-border-width;
  }

  @include e(text) {
    @include m(expand) {
      letter-spacing: 0.3em;
      margin-right: -0.3em;
    }
  }

  @include m(default) {
    --el-button-text-color: var(--el-text-color-regular);
    --el-button-hover-text-color: var(--el-color-primary);
    --el-button-disabled-text-color: var(--el-text-color-placeholder);
  }

  @each $type in (primary, success, warning, danger, info) {
    @include m($type) {
      @include button-variant($type);
    }
  }

  @each $size in (large, small) {
    @include m($size) {
      --el-button-size: #{map.get($input-height, $size)};
      height: var(--el-button-size);

      & [class*='#{$namespace}-icon'] {
        & + span {
          margin-left: map.get($button-icon-span-gap, $size);
        }
      }

      @include button-size(
        map.get($button-padding-vertical, $size) - $button-border-width,
        map.get($button-padding-horizontal, $size) - $button-border-width,
        map.get($button-font-size, $size),
        map.get($button-border-radius, $size)
      );

      @include when(circle) {
        width: var(--el-button-size);
        padding: map.get($button-padding-vertical, $size) - $button-border-width;
      }
    }
  }

  @include m(text) {
    border-color: transparent;
    color: var(--el-color-primary);
    background: transparent;
    padding-left: 0;
    padding-right: 0;

    &:hover,
    &:focus {
      color: var(--el-color-primary-light-2);
      border-color: transparent;
      background-color: transparent;
    }
    &:active {
      color: mix($color-black, $color-primary, $button-active-shade-percent);
      border-color: transparent;
      background-color: transparent;
    }

    &.is-disabled,
    &.is-disabled:hover,
    &.is-disabled:focus {
      border-color: transparent;
    }
  }
}
