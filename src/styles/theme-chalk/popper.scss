@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(popper) {
  @include set-component-css-var('popper', $popper);
}

@include b(popper) {
  position: absolute;
  border-radius: var(--el-popper-border-radius);
  padding: 12px;
  z-index: 2000;
  font-size: 12px;
  line-height: 1.2;
  min-width: 10px;
  word-wrap: break-word;
  visibility: visible;

  $arrow-selector: #{& + '__arrow'};

  @include when(dark) {
    color: var(--el-color-white);
    background: var(--el-text-color-primary);
    #{$arrow-selector}::before {
      background: var(--el-text-color-primary);
      right: 0;
    }
  }

  @include when(light) {
    background: var(--el-color-white);
    border: 1px solid var(--el-border-color-light);

    #{$arrow-selector}::before {
      border: 1px solid var(--el-border-color-light);
      background: var(--el-color-white);
      right: 0;
    }
  }

  @include when(pure) {
    padding: 0;
  }

  @include e(arrow) {
    position: absolute;
    width: 10px;
    height: 10px;
    z-index: -1;

    &::before {
      position: absolute;
      width: 10px;
      height: 10px;
      z-index: -1;
      content: ' ';
      transform: rotate(45deg);
      background: var(--el-text-color-primary);
      box-sizing: border-box;
    }
  }

  $placements: (
    'top': 'bottom',
    'bottom': 'top',
    'left': 'right',
    'right': 'left',
  );

  @each $placement, $opposite in $placements {
    &[data-popper-placement^='#{$placement}'] > #{$arrow-selector} {
      #{$opposite}: -5px;
    }
  }

  @each $placement,
    $adjacency
      in ('top': 'left', 'bottom': 'right', 'left': 'bottom', 'right': 'top')
  {
    &.is-light[data-popper-placement^='#{$placement}'] {
      #{$arrow-selector}::before {
        border-#{$placement}-color: transparent;
        border-#{$adjacency}-color: transparent;
      }
    }
  }
}
