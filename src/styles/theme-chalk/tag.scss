@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(tag) {
  @include set-component-css-var('tag', $tag);
}

$tag-border-width: 1px;

$tag-icon-span-gap: () !default;
$tag-icon-span-gap: map.merge(
  (
    'large': 8px,
    'default': 6px,
    'small': 4px,
  ),
  $tag-icon-span-gap
);

@mixin genTheme(
  $backgroundColorWeight,
  $borderColorWeight,
  $textColorWeight,
  $hoverColorWeight
) {
  --el-tag-bg-color: #{mix(
      map.get($tag-color, 'primary'),
      $color-white,
      $backgroundColorWeight
    )};
  --el-tag-border-color: #{mix(
      map.get($tag-color, 'primary'),
      $color-white,
      $borderColorWeight
    )};
  --el-tag-text-color: #{mix(
      map.get($tag-color, 'primary'),
      $color-white,
      $textColorWeight
    )};
  --el-tag-hover-color: #{mix(
      map.get($tag-color, 'primary'),
      $color-white,
      $hoverColorWeight
    )};

  background-color: var(--el-tag-bg-color);
  border-color: var(--el-tag-border-color);
  color: var(--el-tag-text-color);

  @include when(hit) {
    border-color: map.get($tag-color, 'primary');
  }

  .#{$namespace}-tag__close {
    color: var(--el-tag-text-color);
    &:hover {
      color: var(--el-color-white);
      background-color: var(--el-tag-hover-color);
    }
  }

  @each $type in $types {
    &.#{$namespace}-tag--#{$type} {
      --el-tag-bg-color: #{mix(
          map.get($tag-color, $type),
          $color-white,
          $backgroundColorWeight
        )};
      --el-tag-border-color: #{mix(
          map.get($tag-color, $type),
          $color-white,
          $borderColorWeight
        )};
      --el-tag-text-color: #{mix(
          map.get($tag-color, $type),
          $color-white,
          $textColorWeight
        )};
      --el-tag-hover-color: #{mix(
          map.get($tag-color, $type),
          $color-white,
          $hoverColorWeight
        )};

      @include when(hit) {
        border-color: map.get($tag-color, $type);
      }
    }
  }
}

@include b(tag) {
  @include genTheme(10%, 20%, 100%, 100%);
  display: inline-flex;
  justify-content: center;
  align-items: center;

  height: map.get($tag-height, 'default');
  padding: 0 map.get($tag-padding, 'default') - $border-width-base;
  font-size: var(--el-tag-font-size);
  line-height: 1;
  border-width: $tag-border-width;
  border-style: solid;
  border-radius: var(--el-tag-border-radius);
  box-sizing: border-box;
  white-space: nowrap;

  --el-icon-size: 14px;

  .#{$namespace}-icon {
    border-radius: 50%;
    text-align: center;
    position: relative;
    cursor: pointer;

    font-size: calc(var(--el-icon-size) - 2px);

    height: var(--el-icon-size);
    width: var(--el-icon-size);
    line-height: var(--el-icon-size);

    svg {
      margin: 2px;
    }
  }

  .#{$namespace}-tag__close {
    margin-left: map.get($tag-icon-span-gap, 'default');
  }

  @include m(dark) {
    @include genTheme(100%, 100%, 0, 80%);
  }

  @include m(plain) {
    @include genTheme(0, 40%, 100%, 100%);
  }

  &.is-closable {
    padding-right: map.get($tag-icon-span-gap, 'default') - $border-width-base;
  }

  @each $size in (large, small) {
    @include m($size) {
      padding: 0 map.get($tag-padding, $size) - $tag-border-width;
      height: map.get($tag-height, $size);

      --el-icon-size: #{map.get($tag-icon-size, $size)};

      .#{$namespace}-tag__close {
        margin-left: map.get($tag-icon-span-gap, $size);
      }

      &.is-closable {
        padding-right: map.get($tag-icon-span-gap, $size) - $border-width-base;
      }
    }
  }

  @include m(small) {
    .#{$namespace}-icon-close {
      transform: scale(0.8);
    }
  }
}
