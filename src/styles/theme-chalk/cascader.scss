@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(cascader) {
  @include set-component-css-var('cascader', $cascader);

  display: inline-block;
  position: relative;
  font-size: var(--el-font-size-base);
  line-height: map.get($input-height, 'default');
  outline: none;

  &:not(.is-disabled):hover {
    .#{$namespace}-input__inner {
      cursor: pointer;
      border-color: var(
        --el-input-hover-border,
        map.get($input, 'hover-border')
      );
    }
  }

  .#{$namespace}-input {
    cursor: pointer;

    .#{$namespace}-input__inner {
      text-overflow: ellipsis;

      &:focus {
        border-color: var(
          --el-input-focus-border,
          map.get($input, 'focus-border')
        );
      }
    }

    .#{$namespace}-input__suffix-inner {
      .#{$namespace}-icon {
        height: calc(100% - 2px);

        svg {
          vertical-align: middle;
        }
      }
    }

    .icon-arrow-down {
      transition: transform var(--el-transition-duration);
      font-size: 14px;

      @include when(reverse) {
        transform: rotateZ(180deg);
      }
    }

    .icon-circle-close:hover {
      color: var(
        --el-input-clear-hover-color,
        map.get($input, 'clear-hover-color')
      );
    }

    @include when(focus) {
      .#{$namespace}-input__inner {
        border-color: var(
          --el-input-focus-border,
          map.get($input, 'focus-border')
        );
      }
    }
  }

  @each $size in (large, small) {
    @include m($size) {
      font-size: map.get($input-font-size, $size);
      line-height: map.get($input-height, $size);
    }
  }

  @include when(disabled) {
    .#{$namespace}-cascader__label {
      z-index: calc(var(--el-index-normal) + 1);
      color: var(--el-disabled-text-color);
    }
  }

  @include e(dropdown) {
    @include set-component-css-var('cascader', $cascader);
  }

  @include e(dropdown) {
    font-size: var(--el-cascader-menu-font-size);
    border-radius: var(--el-cascader-menu-radius);

    @include picker-popper(
      var(--el-cascader-menu-fill),
      var(--el-cascader-menu-border),
      var(--el-cascader-menu-shadow)
    );
  }

  @include e(tags) {
    position: absolute;
    left: 0;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-wrap: wrap;
    line-height: normal;
    text-align: left;
    box-sizing: border-box;

    .#{$namespace}-tag {
      display: inline-flex;
      align-items: center;
      max-width: 100%;
      margin: 2px 0 2px 6px;
      text-overflow: ellipsis;
      background: var(--el-cascader-tag-background);

      &:not(.is-hit) {
        border-color: transparent;
      }

      > span {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .#{$namespace}-icon-close {
        flex: none;
        background-color: var(--el-text-color-placeholder);
        color: var(--el-color-white);

        &:hover {
          background-color: var(--el-text-color-secondary);
        }
      }
    }
  }

  @include e(suggestion-panel) {
    border-radius: var(--el-cascader-menu-radius);
  }

  @include e(suggestion-list) {
    max-height: 204px;
    margin: 0;
    padding: 6px 0;
    font-size: var(--el-font-size-base);
    color: var(--el-cascader-menu-text-color);
    text-align: center;
  }

  @include e(suggestion-item) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 34px;
    padding: 0 15px;
    text-align: left;
    outline: none;
    cursor: pointer;

    &:hover,
    &:focus {
      background: var(--el-cascader-node-background-hover);
    }

    &.is-checked {
      color: var(--el-cascader-menu-selected-text-color);
      font-weight: bold;
    }

    > span {
      margin-right: 10px;
    }
  }

  @include e(empty-text) {
    margin: 10px 0;
    color: var(--el-cascader-color-empty);
  }

  @include e(search-input) {
    flex: 1;
    height: 24px;
    min-width: 60px;
    margin: 2px 0 2px 15px;
    padding: 0;
    color: var(--el-cascader-menu-text-color);
    border: none;
    outline: none;
    box-sizing: border-box;

    &::placeholder {
      color: var(--el-text-color-placeholder);
    }
  }
}
