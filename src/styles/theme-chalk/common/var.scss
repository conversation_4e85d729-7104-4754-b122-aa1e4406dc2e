/* Element Chalk Variables */
@use 'sass:math';
@use 'sass:map';

// Special comment for theme configurator
// type|skipAutoTranslation|Category|Order
// skipAutoTranslation 1

// types
$types: primary, success, warning, danger, error, info;

// Color
$colors: () !default;
$colors: map.deep-merge(
  (
    'white': #ffffff,
    'black': #000000,
    'primary': (
      'base': #3A79F1,
      // 'base': #409eff,
    ),
    'success': (
      'base': #40BE53,
      // 'base': #67c23a,
    ),
    'warning': (
      'base': #F9B83F,
      // 'base': #e6a23c,
    ),
    'danger': (
      'base': #F94E54,
      // 'base': #f56c6c,
    ),
    'error': (
      'base': #F94E54,
      // 'base': #f56c6c,
    ),
    'info': (
      'base': #909399,
      // 'base': #909399,
    ),
  ),
  $colors
);

$color-white: map.get($colors, 'white') !default;
$color-black: map.get($colors, 'black') !default;
$color-primary: map.get($colors, 'primary', 'base') !default;
$color-success: map.get($colors, 'success', 'base') !default;
$color-warning: map.get($colors, 'warning', 'base') !default;
$color-danger: map.get($colors, 'danger', 'base') !default;
$color-error: map.get($colors, 'error', 'base') !default;
$color-info: map.get($colors, 'info', 'base') !default;

// https://sass-lang.com/documentation/values/maps#immutability
@mixin set-color-type-light($type, $number) {
  $colors: map.deep-merge(
    (
      $type: (
        'light-#{$number}':
          mix(
            $color-white,
            map.get($colors, $type, 'base'),
            math.percentage(math.div($number, 10))
          ),
      ),
    ),
    $colors
  ) !global;
}

// $colors.primary.light-i
// --el-color-primary-light-i
// 10% 53a8ff
// 20% 66b1ff
// 30% 79bbff
// 40% 8cc5ff
// 50% a0cfff
// 60% b3d8ff
// 70% c6e2ff
// 80% d9ecff
// 90% ecf5ff
@for $i from 1 through 9 {
  @each $type in $types {
    @include set-color-type-light($type, $i);
  }
}

$text-color: () !default;
$text-color: map.merge(
  (
    'primary': #303133,
    'regular': #606266,
    'secondary': #909399,
    'placeholder': #c0c4cc,
  ),
  $text-color
);

$border-color: () !default;
$border-color: map.merge(
  (
    'base': #dcdfe6,
    'light': #e4e7ed,
    'lighter': #ebeef5,
    'extra-light': #f2f6fc,
  ),
  $border-color
);

// Background
/// color|1|Background Color|4
$bg-color: transparent !default;
// $bg-color: #f5f7fa !default;

// Border
$border-width-base: 1px !default;
$border-style-base: solid !default;
$border-color-hover: var(--el-text-color-placeholder) !default;
$border-base: $border-width-base $border-style-base
  map.get($border-color, 'base') !default;
/// borderRadius|1|Radius|0

$border-radius: () !default;
$border-radius: map.merge(
  (
    'base': 0px,
    'small': 2px,
    'round': 20px,
    'circle': 100%,
  ),
  $border-radius
);

// Box-shadow
$box-shadow: () !default;
$box-shadow: map.merge(
  (
    'base': (
      0 2px 4px rgba(0, 0, 0, 0.12),
      0 0 6px rgba(0, 0, 0, 0.04),
    ),
    'light': 0 2px 12px 0 rgba(0, 0, 0, 0.1),
  ),
  $box-shadow
);

// Typography
$font-size: () !default;
$font-size: map.merge(
  (
    'extra-large': 20px,
    'large': 18px,
    'medium': 16px,
    'base': 14px,
    'small': 13px,
    'extra-small': 12px,
  ),
  $font-size
);

// Disable default
$disabled: () !default;
$disabled: map.merge(
  (
    'bg-color': var(--el-bg-color),
    'text-color': var(--el-text-color-placeholder),
    'border-color': var(--el-border-color-light),
  ),
  $disabled
);

$common-component-size: () !default;
$common-component-size: map.merge(
  (
    'large': 40px,
    'default': 32px,
    'small': 24px,
  ),
  $common-component-size
);

// Checkbox
// css3 var in packages/theme-chalk/src/checkbox.scss
$checkbox: () !default;
$checkbox: map.merge(
  (
    'font-size': 14px,
    'font-weight': var(--el-font-weight-primary),
    'text-color': var(--el-text-color-regular),
    'input-height': 14px,
    'input-width': 14px,
    'border-radius': var(--el-border-radius-small),
    'bg-color': var(--el-color-white),
    'input-border': var(--el-border-base),
    'disabled-border-color': var(--el-border-color-base),
    'disabled-input-fill': #edf2fc,
    'disabled-icon-color': var(--el-text-color-placeholder),
    'disabled-checked-input-fill': var(--el-border-color-extra-light),
    'disabled-checked-input-border-color': var(--el-border-color-base),
    'disabled-checked-icon-color': var(--el-text-color-placeholder),
    'checked-text-color': var(--el-color-primary),
    'checked-input-border-color': var(--el-color-primary),
    'checked-bg-color': var(--el-color-primary),
    'checked-icon-color': var(--el-fill-base),
    'input-border-color-hover': var(--el-color-primary),
  ),
  $checkbox
);

$checkbox-button: () !default;
$checkbox-button: map.merge(
  (
    'checked-bg-color': var(--el-color-primary),
    'checked-text-color': var(--el-color-white),
    'checked-border-color': var(--el-color-primary),
  ),
  $checkbox-button
);

$checkbox-bordered-padding-left: () !default;
$checkbox-bordered-padding-left: map.merge(
  (
    'large': 12px,
    'default': 10px,
    'small': 8px,
  ),
  $checkbox-bordered-padding-left
);

$checkbox-bordered-padding-right: () !default;
$checkbox-bordered-padding-right: map.merge(
  (
    'large': 20px,
    'default': 16px,
    'small': 12px,
  ),
  $checkbox-bordered-padding-right
);

// Radio
/// fontSize||Font|1
$radio: () !default;
$radio: map.merge(
  (
    'font-size': var(--el-font-size-base),
    'text-color': var(--el-text-color-regular),
    'font-weight': var(--el-font-weight-primary),
    'input-height': 14px,
    'input-width': 14px,
    'input-border-radius': var(--el-border-radius-circle),
    'input-bg-color': var(--el-color-white),
    'input-border': var(--el-border-base),
    'input-border-color': var(--el-border-color-base),
    'input-border-color-hover': var(--el-color-primary),
  ),
  $radio
);

$radio-height: () !default;
$radio-height: map.merge($common-component-size, $radio-height);

$radio-button: () !default;
$radio-button: map.merge(
  (
    'checked-bg-color': var(--el-color-primary),
    'checked-text-color': var(--el-color-white),
    'checked-border-color': var(--el-color-primary),
    'disabled-checked-fill': var(--el-border-color-extra-light),
  ),
  $radio-button
);

$radio-disabled: () !default;
$radio-disabled: map.merge(
  (
    'input-border-color': var(--el-disabled-border-color),
    'input-fill': var(--el-disabled-bg-color),
    'icon-color': var(--el-disabled-bg-color),
    'checked-input-border-color': var(--el-disabled-border-color),
    'checked-input-fill': var(--el-disabled-bg-color),
    'checked-icon-color': var(--el-text-color-placeholder),
  ),
  $radio-disabled
);

$radio-checked: () !default;
$radio-checked: map.merge(
  (
    'text-color': var(--el-color-primary),
    'input-border-color': var(--el-color-primary),
    'icon-color': var(--el-color-primary),
  ),
  $radio-checked
);

$radio-bordered-input-height: () !default;
$radio-bordered-input-height: map.merge(
  (
    'large': 14px,
    'default': 12px,
    'small': 12px,
  ),
  $radio-bordered-input-height
);

$radio-bordered-input-width: () !default;
$radio-bordered-input-width: map.merge(
  (
    'large': 14px,
    'default': 12px,
    'small': 12px,
  ),
  $radio-bordered-input-width
);

// Select
$select: () !default;
$select: map.merge(
  (
    'border-color-hover': var(--el-border-color-hover),
    'disabled-border': var(--el-disabled-border-color),
    'font-size': var(--el-font-size-base),
    'close-hover-color': var(--el-text-color-secondary),
    'input-color': var(--el-text-color-placeholder),
    'multiple-input-color': var(--el-text-color-regular),
    'input-focus-border-color': var(--el-color-primary),
    'input-font-size': 14px,
  ),
  $select
);

$select-option: () !default;
$select-option: map.merge(
  (
    'text-color': var(--el-text-color-regular),
    'disabled-color': var(--el-text-color-placeholder),
    'height': 34px,
    'hover-background': var(--el-bg-color),
    'selected-text-color': var(--el-color-primary),
  ),
  $select-option
);

$select-group: () !default;
$select-group: map.merge(
  (
    'text-color': var(--el-color-info),
    'height': 30px,
    'font-size': 12px,
  ),
  $select-group
);

$select-dropdown: () !default;
$select-dropdown: map.merge(
  (
    'bg-color': var(--el-color-white),
    'shadow': var(--el-box-shadow-light),
    'empty-color': var(--el-text-color-secondary),
    'max-height': 274px,
    'padding': 6px 0,
    'empty-padding': 10px 0,
    'border': 1px solid var(--el-border-color-light),
  ),
  $select-dropdown
);

// Alert
// css3 var in packages/theme-chalk/src/alert.scss
$alert: () !default;
$alert: map.merge(
  (
    'padding': 8px 16px,
    'border-radius-base': var(--el-border-radius-base),
    'title-font-size': 13px,
    'description-font-size': 12px,
    'close-font-size': 12px,
    'close-customed-font-size': 13px,
    'icon-size': 16px,
    'icon-large-size': 28px,
  ),
  $alert
);

// MessageBox
// css3 var in packages/theme-chalk/src/message-box.scss
$messagebox: () !default;
$messagebox: map.merge(
  (
    'title-color': var(--el-text-color-primary),
    'width': 420px,
    'border-radius': 4px,
    'font-size': var(--el-font-size-large),
    'content-font-size': var(--el-font-size-base),
    'content-color': var(--el-text-color-regular),
    'error-font-size': 12px,
    'padding-primary': 15px,
  ),
  $messagebox
);

// Message
// css3 var in packages/theme-chalk/src/message.scss
$message: () !default;
$message: map.merge(
  (
    'min-width': 380px,
    'bg-color': #edf2fc,
    'padding': 15px 15px 15px 20px,
    'close-size': 16px,
    'close-icon-color': var(--el-text-color-placeholder),
    'close-hover-color': var(--el-text-color-secondary),
  ),
  $message
);

// Notification
// css3 var in packages/theme-chalk/src/notification.scss
$notification: () !default;
$notification: map.merge(
  (
    'width': 330px,
    'padding': 14px 26px 14px 13px,
    'radius': 8px,
    'shadow': var(--el-box-shadow-light),
    'border-color': var(--el-border-color-lighter),
    'icon-size': 24px,
    'close-font-size':
      var(--el-message-close-size, map.get($message, 'close-size')),
    'group-margin-left': 13px,
    'group-margin-right': 8px,
    'content-font-size': var(--el-font-size-base),
    'content-color': var(--el-text-color-regular),
    'title-font-size': 16px,
    'title-color': var(--el-text-color-primary),
    'close-color': var(--el-text-color-secondary),
    'close-hover-color': var(--el-text-color-regular),
  ),
  $notification
);

// Input
// css3 var in packages/theme-chalk/src/input.scss
$input: () !default;
$input: map.merge(
  (
    'text-color': var(--el-text-color-regular),
    'border': var(--el-border-base),
    'border-color': var(--el-border-color-base),
    'border-radius': var(--el-border-radius-base),
    'bg-color': var(--el-color-white),
    'icon-color': var(--el-text-color-placeholder),
    'placeholder-color': var(--el-text-color-placeholder),
    'hover-border': var(--el-border-color-hover),
    'clear-hover-color': var(--el-text-color-secondary),
    'focus-border': var(--el-color-primary),
  ),
  $input
);

$input-disabled: () !default;
$input-disabled: map.merge(
  (
    'fill': var(--el-disabled-bg-color),
    'border': var(--el-disabled-border-color),
    'text-color': var(--el-disabled-text-color),
    'placeholder-color': var(--el-text-color-placeholder),
  ),
  $input-disabled
);

$input-font-size: () !default;
$input-font-size: map.merge(
  (
    'large': 14px,
    'default': 14px,
    'small': 12px,
  ),
  $input-font-size
);

$input-height: () !default;
$input-height: map.merge($common-component-size, $input-height);

$input-line-height: () !default;
$input-line-height: map.merge($common-component-size, $input-line-height);

$input-number-width: () !default;
$input-number-width: map.merge(
  (
    'large': 180px,
    'default': 150px,
    'small': 120px,
  ),
  $input-number-width
);

$input-padding-horizontal: () !default;
$input-padding-horizontal: map.merge(
  (
    'large': 16px,
    'default': 12px,
    'small': 8px,
  ),
  $input-padding-horizontal
);

// Cascader
// css3 var in packages/theme-chalk/src/cascader.scss
$cascader: () !default;
$cascader: map.merge(
  (
    'menu-text-color': var(--el-text-color-regular),
    'menu-selected-text-color': var(--el-color-primary),
    'menu-fill': var(--el-fill-base),
    'menu-font-size': var(--el-font-size-base),
    'menu-radius': var(--el-border-radius-base),
    'menu-border': solid 1px var(--el-border-color-light),
    'menu-shadow': var(--el-box-shadow-light),
    'node-background-hover': var(--el-bg-color),
    'node-color-disabled': var(--el-text-color-placeholder),
    'color-empty': var(--el-text-color-placeholder),
    'tag-background': #f0f2f5,
  ),
  $cascader
);

// Button
// css3 var in packages/theme-chalk/src/button.scss
$button: () !default;
$button: map.merge(
  (
    'font-weight': var(--el-font-weight-primary),
    'border-color': var(--el-border-color-base),
    'bg-color': var(--el-color-white),
    'text-color': var(--el-text-color-regular),
    'disabled-text-color': var(--el-disabled-text-color),
    'disabled-bg-color': var(--el-color-white),
    'disabled-border-color': var(--el-border-color-light),
    'divide-border-color': rgba($color-white, 0.5),
    'hover-text-color': var(--el-color-primary),
    'hover-bg-color': var(--el-color-primary-light-9),
    'hover-border-color': var(--el-color-primary-light-7),
    'active-text-color': var(--el-button-hover-text-color),
    'active-border-color': var(--el-color-primary),
    'active-bg-color': var(--el-button-hover-bg-color),
  ),
  $button
);

$button-border-width: $border-width-base !default;

// need mix, so do not use css var
$button-hover-tint-percent: 20%;
$button-active-shade-percent: 10%;

$button-border-color: () !default;
$button-bg-color: () !default;
$button-text-color: () !default;

@each $type in $types {
  $button-border-color: map.merge(
    (
      $type: map.get($colors, $type, 'base'),
    ),
    $button-border-color
  ) !global;

  $button-bg-color: map.merge(
    (
      $type: map.get($colors, $type, 'base'),
    ),
    $button-bg-color
  ) !global;
}

$button-font-size: () !default;
$button-font-size: map.merge(
  (
    'large': var(--el-font-size-base, map.get($font-size, 'base')),
    'default': var(--el-font-size-base, map.get($font-size, 'base')),
    'small': 12px,
  ),
  $button-font-size
);

$button-border-radius: () !default;
$button-border-radius: map.merge(
  (
    'large': var(--el-border-radius-base),
    'default': var(--el-border-radius-base),
    'small': calc(var(--el-border-radius-base) - 1px),
  ),
  $button-border-radius
);

$button-padding-vertical: () !default;
$button-padding-vertical: map.merge(
  (
    'large': 13px,
    'default': 9px,
    'small': 6px,
  ),
  $button-padding-vertical
);

$button-padding-horizontal: () !default;
$button-padding-horizontal: map.merge(
  (
    'large': 20px,
    'default': 16px,
    'small': 12px,
  ),
  $button-padding-horizontal
);

// Switch
// css3 var in packages/theme-chalk/src/switch.scss
$switch: () !default;
$switch: map.merge(
  (
    'on-color': var(--el-color-primary),
    'off-color': var(--el-border-color-base),
    'font-size': var(--el-font-size-base),
    'core-border-radius': 10px,
    'width': 40px,
    'height': 20px,
    'button-size': 16px,
  ),
  $switch
);

// Dialog
// css3 var in packages/theme-chalk/src/dialog.scss
$dialog: () !default;
$dialog: map.merge(
  (
    'width': 50%,
    'margin-top': 15vh,
    'bg-color': var(--el-color-white),
    'box-shadow': 0 1px 3px rgba(0, 0, 0, 0.3),
    'title-font-size': var(--el-font-size-large),
    'content-font-size': 14px,
    'font-line-height': var(--el-font-line-height-primary),
    'padding-primary': 20px,
  ),
  $dialog
);

// Table
// css3 var in packages/theme-chalk/src/table.scss
$table: () !default;
$table: map.merge(
  (
    'border-color': var(--el-border-color-lighter),
    'border': 1px solid var(--el-table-border-color),
    'text-color': var(--el-text-color-regular),
    'header-text-color': var(--el-text-color-secondary),
    'row-hover-bg-color': var(--el-color-primary-light-9),
    'current-row-bg-color': var(--el-color-primary-light-9),
    'header-bg-color': var(--el-color-white),
    'fixed-box-shadow': 0 0 10px rgba(0, 0, 0, 0.12),
    'bg-color': var(--el-color-white),
    'tr-bg-color': var(--el-color-white),
    'expanded-cell-bg-color': var(--el-color-white),
  ),
  $table
);

$table-font-size: () !default;
$table-font-size: map.merge(
  (
    'large': var(--el-font-size-base, map.get($font-size, 'base')),
    'default': 14px,
    'small': 12px,
  ),
  $table-font-size
);

$table-padding: () !default;
$table-padding: map.merge(
  (
    'large': 12px 0,
    'default': 8px 0,
    'small': 4px 0,
  ),
  $table-padding
);

$table-cell-padding: () !default;
$table-cell-padding: map.merge(
  (
    'large': 0 16px,
    'default': 0 12px,
    'small': 0 8px,
  ),
  $table-cell-padding
);

// Pagination
// css3 var in packages/theme-chalk/src/pagination.scss
$pagination: () !default;
$pagination: map.merge(
  (
    'font-size': 13px,
    'bg-color': var(--el-color-white),
    'text-color': var(--el-text-color-primary),
    'border-radius': 3px,
    'button-color': var(--el-text-color-primary),
    'button-width': 35.5px,
    'button-height': 28px,
    'button-disabled-color': var(--el-text-color-placeholder),
    'button-disabled-bg-color': var(--el-color-white),
    'hover-color': var(--el-color-primary),
    'height-extra-small': 22px,
    'line-height-extra-small': var(--el-pagination-height-extra-small),
  ),
  $pagination
);

// Popup
// css3 var in packages/theme-chalk/src/popup.scss
$popup: () !default;
$popup: map.merge(
  (
    'modal-bg-color': var(--el-color-black),
    'modal-opacity': 0.5,
  ),
  $popup
);

// Popover
// css3 var in packages/theme-chalk/src/popover.scss
$popover: () !default;
$popover: map.merge(
  (
    'bg-color': var(--el-color-white),
    'font-size': var(--el-font-size-base),
    'border-color': var(--el-border-color-lighter),
    'padding': 12px,
    'padding-large': 18px 20px,
    'title-font-size': 16px,
    'title-text-color': var(--el-text-color-primary),
    'border-radius': 4px,
  ),
  $popover
);

// popper
$popper: () !default;
$popper: map.merge(
  (
    'border-radius': var(--el-popover-border-radius, 4px),
  ),
  $popper
);

// skeleton
$skeleton: () !default;
$skeleton: map.merge(
  (
    'color': #f2f2f2,
    'to-color': #e6e6e6,
  ),
  $skeleton
);

// Tooltip
// css3 var in packages/theme-chalk/src/tooltip.scss
$tooltip: () !default;
$tooltip: map.merge(
  (
    'fill': var(--el-text-color-primary),
    'text-color': var(--el-color-white),
    'font-size': 12px,
    'border-color': var(--el-text-color-primary),
    'arrow-size': 6px,
    'padding': 10px,
  ),
  $tooltip
);

// Tag
// css3 var in packages/theme-chalk/src/tag.scss
$tag: () !default;
$tag: map.merge(
  (
    'font-size': 12px,
    'border-radius': 4px,
  ),
  $tag
);

$tag-color: () !default;

$tag-height: () !default;
$tag-height: map.merge(
  (
    'large': 32px,
    'default': 24px,
    'small': 20px,
  ),
  $tag-height
);

$tag-padding: () !default;
$tag-padding: map.merge(
  (
    'large': 12px,
    'default': 10px,
    'small': 8px,
  ),
  $tag-padding
);

$tag-icon-size: () !default;
$tag-icon-size: map.merge(
  (
    'large': 14px,
    'default': 14px,
    'small': 12px,
  ),
  $tag-icon-size
);

@each $type in $types {
  $tag-color: map.merge(
    (
      $type: map.get($colors, $type, 'base'),
    ),
    $tag-color
  ) !global;
}

// Tree
// css3 var in packages/theme-chalk/src/tree.scss
$tree: () !default;
$tree: map.merge(
  (
    'node-hover-bg-color': var(--el-bg-color),
    'text-color': var(--el-text-color-regular),
    'expand-icon-color': var(--el-text-color-placeholder),
  ),
  $tree
);

// Dropdown

$dropdown: () !default;
$dropdown: map.merge(
  (
    'menu-box-shadow': var(--el-box-shadow-light),
    'menuItem-hover-fill': var(--el-color-primary-light-9),
    'menuItem-hover-color': var(--el-color-primary-light-2),
    'menu-index': 10,
  ),
  $dropdown
);

// drawer
$drawer: () !default;
$drawer: map.merge(
  (
    'bg-color': var(--el-dialog-bg-color, var(--el-color-white)),
    'padding-primary': var(--el-dialog-padding-primary, 20px),
  ),
  $drawer
);

// Badge
// css3 var in packages/theme-chalk/src/badge.scss
$badge: () !default;
$badge: map.merge(
  (
    'bg-color': var(--el-color-danger),
    'radius': 10px,
    'font-size': 12px,
    'padding': 6px,
    'size': 18px,
  ),
  $badge
);

// Card
$card: () !default;
$card: map.merge(
  (
    'border-color':
      var(--el-border-color-light, map.get($border-color, 'lighter')),
    'border-radius': 4px,
    'padding': 20px,
    'bg-color': var(--el-color-white),
  ),
  $card
);

$dark-card: () !default;
$dark-card: map.merge(
  (
    'bg-color': var(--el-color-black),
  ),
  $dark-card
);

// Slider
// css3 var in packages/theme-chalk/src/slider.scss
$slider: () !default;
$slider: map.merge(
  (
    'main-bg-color': var(--el-color-primary),
    'runway-bg-color': var(--el-border-color-light),
    'stop-bg-color': var(--el-color-white),
    'disable-color': var(--el-text-color-placeholder),
    'margin': 16px 0,
    'border-radius': 3px,
    'height': 6px,
    'button-size': 20px,
    'button-wrapper-size': 36px,
    'button-wrapper-offset': -15px,
  ),
  $slider
);

// Menu
// css3 var in packages/theme-chalk/src/menu.scss
$menu: () !default;
$menu: map.merge(
  (
    'active-color': var(--el-color-primary),
    'text-color': var(--el-text-color-placeholder),
    'hover-text-color': var(--el-text-color-placeholder),
    'bg-color': $bg-color,
    'hover-bg-color': var(--el-color-primary-light-9),
    'item-height': 56px,
    'item-font-size': var(--el-font-size-base),
    'item-hover-fill': var(--el-color-primary-light-9),
    'border-color': $bg-color,
  ),
  $menu
);

// Rate
$rate: () !default;
$rate: map.merge(
  (
    'height': 20px,
    'font-size': var(--el-font-size-base),
    'icon-size': 18px,
    'icon-margin': 6px,
    'icon-color': var(--el-text-color-placeholder),
  ),
  $rate
);

// DatePicker
// css3 var packages/theme-chalk/src/date-picker/var.scss
$datepicker: () !default;
$datepicker: map.merge(
  (
    'text-color': var(--el-text-color-regular),
    'off-text-color': var(--el-text-color-placeholder),
    'header-text-color': var(--el-text-color-regular),
    'icon-color': var(--el-text-color-primary),
    'border-color': var(--el-disabled-border-color),
    'inner-border-color': var(--el-border-color-light),
    'inrange-bg-color': var(--el-border-color-extra-light),
    'inrange-hover-bg-color': var(--el-border-color-extra-light),
    'active-color': var(--el-color-primary),
    'hover-text-color': var(--el-color-primary),
  ),
  $datepicker
);

$date-editor: () !default;
$date-editor: map.merge(
  (
    'width': 220px,
    'monthrange-width': 300px,
    'daterange-width': 350px,
    'datetimerange-width': 400px,
  ),
  $date-editor
);

// Loading
// css3 var in packages/theme-chalk/src/loading.scss
$loading: () !default;
$loading: map.merge(
  (
    'spinner-size': 42px,
    'fullscreen-spinner-size': 50px,
  ),
  $loading
);

// Scrollbar
// css3 var in packages/theme-chalk/src/scrollbar.scss
$scrollbar: () !default;
$scrollbar: map.merge(
  (
    'opacity': 0.3,
    'bg-color': var(--el-text-color-secondary),
    'hover-opacity': 0.5,
    'hover-bg-color': var(--el-text-color-secondary),
  ),
  $scrollbar
);

// Carousel
// css3 var in packages/theme-chalk/src/carousel.scss
$carousel: () !default;
$carousel: map.merge(
  (
    'arrow-font-size': 12px,
    'arrow-size': 36px,
    'arrow-background': rgba(31, 45, 61, 0.11),
    'arrow-hover-background': rgba(31, 45, 61, 0.23),
    'indicator-width': 30px,
    'indicator-height': 2px,
    'indicator-padding-horizontal': 4px,
    'indicator-padding-vertical': 12px,
    'indicator-out-color': var(--el-border-color-hover),
  ),
  $carousel
);

// Collapse
// css3 var in packages/theme-chalk/src/collapse.scss
$collapse: () !default;
$collapse: map.merge(
  (
    'border-color': var(--el-border-color-lighter),
    'header-height': 48px,
    'header-bg-color': var(--el-color-white),
    'header-text-color': var(--el-text-color-primary),
    'header-font-size': 13px,
    'content-bg-color': var(--el-color-white),
    'content-font-size': 13px,
    'content-text-color': var(--el-text-color-primary),
  ),
  $collapse
);

// Transfer
// css3 var in packages/theme-chalk/src/transfer.scss
$transfer: () !default;
$transfer: map.merge(
  (
    'border-color': var(--el-border-color-lighter),
    'border-radius': var(--el-border-radius-base),
    'panel-width': 200px,
    'panel-header-height': 40px,
    'panel-header-bg-color': var(--el-bg-color),
    'panel-footer-height': 40px,
    'panel-body-height': 246px,
    'item-height': 30px,
    'filter-height': 32px,
  ),
  $transfer
);

// Timeline
// css3 var in packages/theme-chalk/src/timeline-item.scss
$timeline: () !default;
$timeline: map.merge(
  (
    'node-size-normal': 12px,
    'node-size-large': 14px,
    'node-color': var(--el-border-color-light),
  ),
  $timeline
);

// Backtop
// css3 var in packages/theme-chalk/src/backtop.scss
$backtop: () !default;
$backtop: map.merge(
  (
    'bg-color': var(--el-color-white),
    'text-color': var(--el-color-primary),
    'hover-bg-color': var(--el-border-color-extra-light),
  ),
  $backtop
);

// Link
// css3 var in packages/theme-chalk/src/link.scss
$link: () !default;
$link: map.merge(
  (
    'font-size': var(--el-font-size-base),
    'font-weight': var(--el-font-weight-primary),
    'default-text-color': var(--el-text-color-regular),
    'default-active-color': var(--el-color-primary),
    'disabled-text-color': var(--el-text-color-placeholder),
  ),
  $link
);

$link-text-color: () !default;

@each $type in $types {
  $link-text-color: map.merge(
    $link-text-color,
    (
      $type: map.get($colors, $type, 'base'),
    )
  ) !global;
}

// Calendar
// css3 var in packages/theme-chalk/src/calendar.scss
$calendar: () !default;
$calendar: map.merge(
  (
    'border': var(--el-table-border, 1px solid var(--el-border-color-lighter)),
    'header-border-bottom': var(--el-calendar-border),
    'selected-bg-color': #f2f8fe,
    'cell-width': 85px,
  ),
  $calendar
);

// Form

// css3 var in packages/theme-chalk/src/form.scss
$form: () !default;
$form: map.merge(
  (
    'label-font-size': var(--el-font-size-base),
  ),
  $form
);

// Avatar
// css3 var in packages/theme-chalk/src/avatar.scss
$avatar: () !default;
$avatar: map.merge(
  (
    'text-color': #fff,
    'bg-color': #c0c4cc,
    'text-size': 14px,
    'icon-size': 18px,
    'border-radius': var(--el-border-radius-base),
  ),
  $avatar
);

$avatar-size: () !default;
$avatar-size: map.merge(
  (
    'large': 56px,
    'default': 40px,
    'small': 24px,
  ),
  $avatar-size
);

// Empty

// css3 var in packages/theme-chalk/src/empty.scss
$empty: () !default;
$empty: map.merge(
  (
    'padding': 40px 0,
    'image-width': 160px,
    'description-margin-top': 20px,
    'bottom-margin-top': 20px,
  ),
  $empty
);

// Descriptions

// css3 var in packages/theme-chalk/src/descriptions.scss
$descriptions: () !default;
$descriptions: map.merge(
  (
    'table-border': 1px solid var(--el-border-color-lighter),
    'item-bordered-label-background': #f5f7fa,
  ),
  $descriptions
);

// Result

// css3 var in packages/theme-chalk/src/result.scss
$result: () !default;
$result: map.merge(
  (
    'padding': 40px 30px,
    'icon-font-size': 64px,
    'title-font-size': 20px,
    'title-margin-top': 20px,
    'subtitle-margin-top': 10px,
    'extra-margin-top': 30px,
  ),
  $result
);

// transition
$transition: () !default;
$transition: map.merge(
  (
    'all': all var(--el-transition-duration)
      var(--el-transition-function-ease-in-out-bezier),
    'fade': opacity var(--el-transition-duration)
      var(--el-transition-function-fast-bezier),
    'md-fade': (
      transform var(--el-transition-duration)
        var(--el-transition-function-fast-bezier),
      opacity var(--el-transition-duration)
        var(--el-transition-function-fast-bezier),
    ),
    'fade-linear': opacity var(--el-transition-duration-fast) linear,
    'border': border-color var(--el-transition-duration-fast)
      var(--el-transition-function-ease-in-out-bezier),
    'color': color var(--el-transition-duration-fast)
      var(--el-transition-function-ease-in-out-bezier),
  ),
  $transition
);

$transition-duration: () !default;
$transition-duration: map.merge(
  (
    'default': 0.3s,
    'fast': 0.2s,
  ),
  $transition-duration
);

$transition-function: () !default;
$transition-function: map.merge(
  (
    'ease-in-out-bezier': cubic-bezier(0.645, 0.045, 0.355, 1),
    'fast-bezier': cubic-bezier(0.23, 1, 0.32, 1),
  ),
  $transition-function
);

// header
$header: () !default;
$header: map.merge(
  (
    'padding': 0 20px,
    'height': 60px,
  ),
  $header
);
// main
$main: () !default;
$main: map.merge(
  (
    'padding': 20px,
  ),
  $main
);
// footer
$footer: () !default;
$footer: map.merge(
  (
    'padding': 0 20px,
    'height': 60px,
  ),
  $footer
);

// Break-point
$sm: 768px !default;
$md: 992px !default;
$lg: 1200px !default;
$xl: 1920px !default;

$breakpoints: (
  'xs': '(max-width: #{$sm})',
  'sm': '(min-width: #{$sm})',
  'md': '(min-width: #{$md})',
  'lg': '(min-width: #{$lg})',
  'xl': '(min-width: #{$xl})',
) !default;

$breakpoints-spec: (
  'xs-only': '(max-width: #{$sm})',
  'sm-and-up': '(min-width: #{$sm})',
  'sm-only': '(min-width: #{$sm}) and (max-width: #{$md})',
  'sm-and-down': '(max-width: #{$md})',
  'md-and-up': '(min-width: #{$md})',
  'md-only': '(min-width: #{$md}) and (max-width: #{$lg})',
  'md-and-down': '(max-width: #{$lg})',
  'lg-and-up': '(min-width: #{$lg})',
  'lg-only': '(min-width: #{$lg}) and (max-width: #{$xl})',
  'lg-and-down': '(max-width: #{$xl})',
  'xl-only': '(min-width: #{$xl})',
) !default;
