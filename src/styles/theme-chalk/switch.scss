@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(switch) {
  @include set-component-css-var('switch', $switch);
}

@include b(switch) {
  display: inline-flex;
  align-items: center;
  position: relative;
  font-size: var(--el-switch-font-size);
  line-height: var(--el-switch-height);
  height: var(--el-switch-height);
  vertical-align: middle;
  @include when(disabled) {
    & .#{$namespace}-switch__core,
    & .#{$namespace}-switch__label {
      cursor: not-allowed;
    }
  }

  @include e(label) {
    transition: var(--el-transition-duration-fast);
    height: var(--el-switch-height);
    display: inline-block;
    font-size: var(--el-switch-font-size);
    font-weight: 500;
    cursor: pointer;
    vertical-align: middle;
    color: var(--el-text-color-primary);

    @include when(active) {
      color: var(--el-color-primary);
    }

    @include m(left) {
      margin-right: 10px;
    }
    @include m(right) {
      margin-left: 10px;
    }
    & * {
      line-height: 1;
      font-size: var(--el-switch-font-size);
      display: inline-block;
    }
    .#{$namespace}-icon {
      height: inherit;
      svg {
        vertical-align: middle;
      }
    }
  }

  @include e(input) {
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    margin: 0;
  }

  @include e(core) {
    margin: 0;
    display: inline-block;
    position: relative;
    width: var(--el-switch-width);
    height: var(--el-switch-height);
    border: 1px solid var(--el-switch-off-color);
    outline: none;
    border-radius: var(--el-switch-core-border-radius);
    box-sizing: border-box;
    background: var(--el-switch-off-color);
    cursor: pointer;
    transition: border-color var(--el-transition-duration),
      background-color var(--el-transition-duration);
    vertical-align: middle;

    .#{$namespace}-switch__inner {
      position: absolute;
      top: 1px;
      left: 1px;
      transition: all var(--el-transition-duration);
      width: var(--el-switch-button-size);
      height: var(--el-switch-button-size);
      display: flex;
      justify-content: center;
      align-items: center;
      left: 50%;

      .is-icon,
      .is-text {
        color: var(--el-color-white);
        transition: opacity var(--el-transition-duration);
        position: absolute;
        user-select: none;
      }
    }

    .#{$namespace}-switch__action {
      position: absolute;
      top: 1px;
      left: 1px;
      border-radius: var(--el-border-radius-circle);
      transition: all var(--el-transition-duration);
      width: var(--el-switch-button-size);
      height: var(--el-switch-button-size);
      background-color: var(--el-color-white);
      display: flex;
      justify-content: center;
      align-items: center;
      color: var(--el-switch-off-color);

      .is-icon,
      .is-text {
        transition: opacity var(--el-transition-duration);
        position: absolute;
        user-select: none;
      }
    }

    .is-text {
      font-size: 12px;
    }

    .is-show {
      opacity: 1;
    }

    .is-hide {
      opacity: 0;
    }
  }

  @include when(checked) {
    .#{$namespace}-switch__core {
      border-color: var(--el-switch-on-color);
      background-color: var(--el-switch-on-color);
      .#{$namespace}-switch__action {
        left: 100%;
        margin-left: calc(-1px - var(--el-switch-button-size));
        color: var(--el-switch-on-color);
      }
      .#{$namespace}-switch__inner {
        left: 50%;
        margin-left: calc(-1px - var(--el-switch-button-size));
      }
    }
  }

  @include when(disabled) {
    opacity: 0.6;
  }

  @include m(wide) {
    .#{$namespace}-switch__label {
      &.#{$namespace}-switch__label--left {
        span {
          left: 10px;
        }
      }
      &.#{$namespace}-switch__label--right {
        span {
          right: 10px;
        }
      }
    }
  }

  & .label-fade-enter-from,
  & .label-fade-leave-active {
    opacity: 0;
  }
}
