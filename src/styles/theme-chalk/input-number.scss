@use 'sass:math';
@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'common/var' as *;

@include b(input-number) {
  position: relative;
  display: inline-block;
  width: map.get($input-number-width, 'default');
  line-height: #{map.get($input-height, 'default') - 2};

  .#{$namespace}-input {
    display: block;

    &__inner {
      -webkit-appearance: none;
      -moz-appearance: textfield;
      &::-webkit-inner-spin-button,
      &::-webkit-outer-spin-button {
        margin: 0;
        -webkit-appearance: none;
      }
      padding-left: #{map.get($input-height, 'default') + 10};
      padding-right: #{map.get($input-height, 'default') + 10};
      text-align: center;
    }
  }

  @include e((increase, decrease)) {
    display: flex;
    justify-content: center;
    align-items: center;
    height: auto;

    position: absolute;
    z-index: 1;
    top: 1px;
    bottom: 1px;

    width: map.get($input-height, 'default');
    background: var(--el-bg-color);
    color: var(--el-text-color-regular);
    cursor: pointer;
    font-size: 13px;
    user-select: none;

    &:hover {
      color: var(--el-color-primary);

      & ~ .#{$namespace}-input:not(.is-disabled) .#{$namespace}-input__inner {
        border-color: var(
          --el-input-focus-border,
          map.get($input, 'focus-border')
        );
      }
    }

    &.is-disabled {
      color: var(--el-disabled-text-color);
      cursor: not-allowed;
    }
  }

  @include e(increase) {
    right: 1px;
    border-radius: 0 var(--el-border-radius-base) var(--el-border-radius-base) 0;
    border-left: var(--el-border-base);
  }

  @include e(decrease) {
    left: 1px;
    border-radius: var(--el-border-radius-base) 0 0 var(--el-border-radius-base);
    border-right: var(--el-border-base);
  }

  @include when(disabled) {
    @include e((increase, decrease)) {
      border-color: var(--el-disabled-border-color);
      color: var(--el-disabled-border-color);

      &:hover {
        color: var(--el-disabled-border-color);
        cursor: not-allowed;
      }
    }
  }

  @each $size in (large, small) {
    @include m($size) {
      width: map.get($input-number-width, $size);
      line-height: #{map.get($input-height, $size) - 2};

      @include e((increase, decrease)) {
        width: map.get($input-height, $size);
        font-size: map.get($input-font-size, $size);
      }

      .#{$namespace}-input__inner {
        padding-left: #{map.get($input-height, $size) + 7};
        padding-right: #{map.get($input-height, $size) + 7};
      }
    }
  }

  @include m(small) {
    @include e((increase, decrease)) {
      [class*='#{$namespace}-icon'] {
        transform: scale(0.9);
      }
    }
  }

  @include when(without-controls) {
    .#{$namespace}-input__inner {
      padding-left: 15px;
      padding-right: 15px;
    }
  }

  @include when(controls-right) {
    .#{$namespace}-input__inner {
      padding-left: 15px;
      padding-right: #{map.get($input-height, 'default') + 10};
    }

    @include e((increase, decrease)) {
      height: auto;
      line-height: #{math.div(map.get($input-height, 'default') - 2, 2)};

      [class*='#{$namespace}-icon'] {
        transform: scale(0.8);
      }
    }

    @include e(increase) {
      border-radius: 0 var(--el-border-radius-base) 0 0;
      border-bottom: var(--el-border-base);
    }

    @include e(decrease) {
      right: 1px;
      bottom: 1px;
      top: auto;
      left: auto;
      border-right: none;
      border-left: var(--el-border-base);
      border-radius: 0 0 var(--el-border-radius-base) 0;
    }

    @each $size in (large, small) {
      &[class*='#{$size}'] {
        [class*='increase'],
        [class*='decrease'] {
          line-height: #{math.div(map.get($input-height, $size) - 2, 2)};
        }
      }
    }
  }
}
