@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'common/var' as *;

@include b(select-dropdown) {
  z-index: calc(var(--el-index-top) + 1);
  border-radius: var(--el-border-radius-base);
  box-sizing: border-box;

  @include when(multiple) {
    & .#{$namespace}-select-dropdown__item.selected {
      color: map.get($select-option, 'selected-text-color');
      background-color: map.get($select-dropdown, 'bg-color');

      &.hover {
        background-color: map.get($select-option, 'hover-background');
      }

      & .el-icon {
        position: absolute;
        right: 20px;
        top: 0;
        height: inherit;
        font-size: 12px;

        svg {
          height: inherit;
          vertical-align: middle;
        }
      }
    }
  }

  .#{$namespace}-scrollbar.is-empty .#{$namespace}-select-dropdown__list {
    padding: 0;
  }
}

@include b(select-dropdown__empty) {
  padding: map.get($select-dropdown, 'empty-padding');
  margin: 0;
  text-align: center;
  color: map.get($select-dropdown, 'empty-color');
  font-size: var(--el-select-font-size);
}

@include b(select-dropdown__wrap) {
  max-height: map.get($select-dropdown, 'max-height');
}

@include b(select-dropdown__list) {
  list-style: none;
  padding: map.get($select-dropdown, 'padding');
  margin: 0;
  box-sizing: border-box;
}
