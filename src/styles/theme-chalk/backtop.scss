@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(backtop) {
  @include set-component-css-var('backtop', $backtop);

  position: fixed;
  background-color: var(--el-backtop-bg-color);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: var(--el-backtop-text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.12);
  cursor: pointer;
  z-index: 5;

  &:hover {
    background-color: var(--el-backtop-hover-bg-color);
  }

  @include e(icon) {
    font-size: 20px;
  }
}
