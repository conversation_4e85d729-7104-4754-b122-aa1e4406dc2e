@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(alert) {
  @include set-component-css-var('alert', $alert);

  width: 100%;
  padding: var(--el-alert-padding);
  margin: 0;
  box-sizing: border-box;
  border-radius: var(--el-alert-border-radius-base);
  position: relative;
  background-color: var(--el-color-white);
  overflow: hidden;
  opacity: 1;
  display: flex;
  align-items: center;
  transition: opacity var(--el-transition-duration-fast);

  @include when(light) {
    .#{$namespace}-alert__closebtn {
      color: var(--el-text-color-placeholder);
    }
  }

  @include when(dark) {
    .#{$namespace}-alert__closebtn {
      color: var(--el-color-white);
    }
    .#{$namespace}-alert__description {
      color: var(--el-color-white);
    }
  }

  @include when(center) {
    justify-content: center;
  }

  @each $type in (success, info, warning, error) {
    @include m($type) {
      --el-alert-bg-color: #{map.get($colors, $type, 'light-9')};

      &.is-light {
        background-color: var(--el-alert-bg-color);
        color: var(--el-color-#{$type});

        .#{$namespace}-alert__description {
          color: var(--el-color-#{$type});
        }
      }

      &.is-dark {
        background-color: var(--el-color-#{$type});
        color: var(--el-color-white);
      }
    }
  }

  @include e(content) {
    display: table-cell;
    padding: 0 8px;
  }

  @include e(icon) {
    font-size: var(--el-alert-icon-size);
    width: var(--el-alert-icon-size);
    @include when(big) {
      font-size: var(--el-alert-icon-large-size);
      width: var(--el-alert-icon-large-size);
    }
  }

  @include e(title) {
    font-size: var(--el-alert-title-font-size);
    line-height: 18px;
    @include when(bold) {
      font-weight: bold;
    }
  }

  & .#{$namespace}-alert__description {
    font-size: var(--el-alert-description-font-size);
    margin: 5px 0 0 0;
  }

  @include e(closebtn) {
    font-size: var(--el-alert-close-font-size);
    opacity: 1;
    position: absolute;
    top: 12px;
    right: 15px;
    cursor: pointer;

    @include when(customed) {
      font-style: normal;
      font-size: var(--el-alert-close-customed-font-size);
      top: 9px;
    }
  }
}

.#{$namespace}-alert-fade-enter-from,
.#{$namespace}-alert-fade-leave-active {
  opacity: 0;
}
