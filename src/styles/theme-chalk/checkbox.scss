@use 'sass:map';

@use 'common/var' as *;
@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'mixins/_button';
@use 'mixins/utils' as *;

$checkbox-height: () !default;
$checkbox-height: map.merge($common-component-size, $checkbox-height);

$checkbox-bordered-input-height: () !default;
$checkbox-bordered-input-height: map.merge(
  (
    'large': 14px,
    'default': 12px,
    'small': 12px,
  ),
  $checkbox-bordered-input-height
);

$checkbox-bordered-input-width: () !default;
$checkbox-bordered-input-width: map.merge(
  $checkbox-bordered-input-height,
  $checkbox-bordered-input-width
);

@include b(checkbox) {
  @include set-component-css-var('checkbox', $checkbox);
}

@include b(checkbox) {
  color: var(--el-checkbox-text-color);
  font-weight: var(--el-checkbox-font-weight);
  font-size: var(--el-font-size-base);
  position: relative;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  user-select: none;
  margin-right: 30px;
  height: map.get($checkbox-height, 'default');

  @each $size in (large, small) {
    &.#{$namespace}-checkbox--#{$size} {
      height: map.get($checkbox-height, $size);
    }
  }

  @include when(bordered) {
    padding: 0 map.get($checkbox-bordered-padding-right, 'default')-$border-width-base
      0 map.get($checkbox-bordered-padding-left, 'default')-$border-width-base;
    border-radius: var(--el-border-radius-base);
    border: var(--el-border-base);
    box-sizing: border-box;

    &.is-checked {
      border-color: var(--el-color-primary);
    }

    &.is-disabled {
      border-color: var(--el-border-color-lighter);
      cursor: not-allowed;
    }

    @each $size in (large, small) {
      &.#{$namespace}-checkbox--#{$size} {
        padding: 0
          map.get($checkbox-bordered-padding-right, $size)-$border-width-base
          0
          map.get($checkbox-bordered-padding-left, $size)-$border-width-base;

        border-radius: map.get($button-border-radius, $size);

        .#{$namespace}-checkbox__label {
          font-size: map.get($button-font-size, $size);
        }

        .#{$namespace}-checkbox__inner {
          height: map.get($checkbox-bordered-input-height, $size);
          width: map.get($checkbox-bordered-input-width, $size);
        }
      }
    }

    &.#{$namespace}-checkbox--small {
      .#{$namespace}-checkbox__inner {
        &::after {
          height: 6px;
          width: 2px;
        }
      }
    }
  }

  @include e(input) {
    white-space: nowrap;
    cursor: pointer;
    outline: none;
    display: inline-flex;
    position: relative;

    @include when(disabled) {
      .#{$namespace}-checkbox__inner {
        background-color: var(--el-checkbox-disabled-input-fill);
        border-color: var(--el-checkbox-disabled-border-color);
        cursor: not-allowed;

        &::after {
          cursor: not-allowed;
          border-color: var(--el-checkbox-disabled-icon-color);
        }

        & + .#{$namespace}-checkbox__label {
          cursor: not-allowed;
        }
      }

      &.is-checked {
        .#{$namespace}-checkbox__inner {
          background-color: var(--el-checkbox-disabled-checked-input-fill);
          border-color: var(--el-checkbox-disabled-checked-input-border-color);

          &::after {
            border-color: var(--el-checkbox-disabled-checked-icon-color);
          }
        }
      }

      &.is-indeterminate {
        .#{$namespace}-checkbox__inner {
          background-color: var(--el-checkbox-disabled-checked-input-fill);
          border-color: var(--el-checkbox-disabled-checked-input-border-color);

          &::before {
            background-color: var(--el-checkbox-disabled-checked-icon-color);
            border-color: var(--el-checkbox-disabled-checked-icon-color);
          }
        }
      }

      & + span.#{$namespace}-checkbox__label {
        color: var(--el-disabled-text-color);
        cursor: not-allowed;
      }
    }

    @include when(checked) {
      .#{$namespace}-checkbox__inner {
        background-color: var(--el-checkbox-checked-bg-color);
        border-color: var(--el-checkbox-checked-input-border-color);

        &::after {
          transform: rotate(45deg) scaleY(1);
        }
      }

      & + .#{$namespace}-checkbox__label {
        color: var(--el-checkbox-checked-text-color);
      }
    }
    @include when(focus) {
      // Visually distinguish when focus
      .#{$namespace}-checkbox__inner {
        border-color: var(--el-checkbox-input-border-color-hover);
      }
    }
    @include when(indeterminate) {
      .#{$namespace}-checkbox__inner {
        background-color: var(--el-checkbox-checked-bg-color);
        border-color: var(--el-checkbox-checked-input-border-color);

        &::before {
          content: '';
          position: absolute;
          display: block;
          background-color: var(--el-checkbox-checked-icon-color);
          height: 2px;
          transform: scale(0.5);
          left: 0;
          right: 0;
          top: 5px;
        }

        &::after {
          display: none;
        }
      }
    }
  }
  @include e(inner) {
    display: inline-block;
    position: relative;
    border: var(--el-checkbox-input-border);
    border-radius: var(--el-checkbox-border-radius);
    box-sizing: border-box;
    width: var(--el-checkbox-input-width);
    height: var(--el-checkbox-input-height);
    background-color: var(--el-checkbox-bg-color);
    z-index: var(--el-index-normal);
    transition: border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46),
      background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);

    &:hover {
      border-color: var(--el-checkbox-input-border-color-hover);
    }

    &::after {
      box-sizing: content-box;
      content: '';
      border: 1px solid var(--el-checkbox-checked-icon-color);
      border-left: 0;
      border-top: 0;
      height: 7px;
      left: 4px;
      position: absolute;
      top: 1px;
      transform: rotate(45deg) scaleY(0);
      width: 3px;
      transition: transform 0.15s ease-in 0.05s;
      transform-origin: center;
    }
  }

  @include e(original) {
    opacity: 0;
    outline: none;
    position: absolute;
    margin: 0;
    width: 0;
    height: 0;
    z-index: -1;
  }

  @include e(label) {
    display: inline-block;
    padding-left: 8px;
    line-height: 1;
    font-size: var(--el-checkbox-font-size);
  }

  &:last-of-type {
    margin-right: 0;
  }
}
