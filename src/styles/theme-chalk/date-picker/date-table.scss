@use '../common/var' as *;
@use '../mixins/mixins' as *;

@include b(date-table) {
  font-size: 12px;
  user-select: none;

  @include when(week-mode) {
    .#{$namespace}-date-table__row {
      &:hover {
        .el-date-table-cell {
          background-color: var(--el-datepicker-inrange-bg-color);
        }
        td.available:hover {
          color: var(--el-datepicker-text-color);
        }
        td:first-child .el-date-table-cell {
          margin-left: 5px;
          border-top-left-radius: 15px;
          border-bottom-left-radius: 15px;
        }
        td:last-child .el-date-table-cell {
          margin-right: 5px;
          border-top-right-radius: 15px;
          border-bottom-right-radius: 15px;
        }
      }

      &.current .el-date-table-cell {
        background-color: var(--el-datepicker-inrange-bg-color);
      }
    }
  }

  td {
    width: 32px;
    height: 30px;
    padding: 4px 0;
    box-sizing: border-box;
    text-align: center;
    cursor: pointer;
    position: relative;

    @include b(date-table-cell) {
      height: 30px;
      padding: 3px 0;
      box-sizing: border-box;
      @include b(date-table-cell__text) {
        width: 24px;
        height: 24px;
        display: block;
        margin: 0 auto;
        line-height: 24px;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 50%;
      }
    }

    &.next-month,
    &.prev-month {
      color: var(--el-datepicker-off-text-color);
    }

    &.today {
      position: relative;
      .el-date-table-cell__text {
        color: var(--el-color-primary);
        font-weight: bold;
      }
      &.start-date .el-date-table-cell__text,
      &.end-date .el-date-table-cell__text {
        color: $color-white;
      }
    }

    &.available:hover {
      color: var(--el-datepicker-hover-text-color);
    }

    &.in-range .el-date-table-cell {
      background-color: var(--el-datepicker-inrange-bg-color);
      &:hover {
        background-color: var(--el-datepicker-inrange-hover-bg-color);
      }
    }

    &.current:not(.disabled) .el-date-table-cell__text {
      color: $color-white;
      background-color: var(--el-datepicker-active-color);
    }
    &.start-date .el-date-table-cell,
    &.end-date .el-date-table-cell {
      color: $color-white;
    }

    &.start-date .el-date-table-cell__text,
    &.end-date .el-date-table-cell__text {
      background-color: var(--el-datepicker-active-color);
    }

    &.start-date .el-date-table-cell {
      margin-left: 5px;
      border-top-left-radius: 15px;
      border-bottom-left-radius: 15px;
    }

    &.end-date .el-date-table-cell {
      margin-right: 5px;
      border-top-right-radius: 15px;
      border-bottom-right-radius: 15px;
    }

    &.disabled .el-date-table-cell {
      background-color: var(--el-bg-color);
      opacity: 1;
      cursor: not-allowed;
      color: var(--el-text-color-placeholder);
    }

    &.selected .el-date-table-cell {
      margin-left: 5px;
      margin-right: 5px;
      background-color: var(--el-datepicker-inrange-bg-color);
      border-radius: 15px;
      &:hover {
        background-color: var(--el-datepicker-inrange-hover-bg-color);
      }
    }

    &.selected .el-date-table-cell__text {
      background-color: var(--el-datepicker-active-color);
      color: $color-white;
      border-radius: 15px;
    }

    &.week {
      font-size: 80%;
      color: var(--el-datepicker-header-text-color);
    }
  }

  th {
    padding: 5px;
    color: var(--el-datepicker-header-text-color);
    font-weight: 400;
    border-bottom: solid 1px var(--el-border-color-lighter);
  }
}
