@use '../mixins/mixins' as *;
@use '../common/var' as *;

@include b(year-table) {
  font-size: 12px;
  margin: -1px;
  border-collapse: collapse;

  .#{$namespace}-icon {
    color: var(--el-datepicker-icon-color);
  }

  td {
    text-align: center;
    padding: 20px 3px;
    cursor: pointer;

    &.today {
      .cell {
        color: var(--el-color-primary);
        font-weight: bold;
      }
    }

    &.disabled .cell {
      background-color: var(--el-bg-color);
      cursor: not-allowed;
      color: var(--el-text-color-placeholder);

      &:hover {
        color: var(--el-text-color-placeholder);
      }
    }

    .cell {
      width: 48px;
      height: 32px;
      display: block;
      line-height: 32px;
      color: var(--el-datepicker-text-color);
      margin: 0 auto;

      &:hover {
        color: var(--el-datepicker-hover-text-color);
      }
    }

    &.current:not(.disabled) .cell {
      color: var(--el-datepicker-active-color);
    }
  }
}
