@use '../mixins/mixins' as *;
@use '../common/var' as *;

@include b(month-table) {
  font-size: 12px;
  margin: -1px;
  border-collapse: collapse;

  td {
    text-align: center;
    padding: 8px 0px;
    cursor: pointer;
    & div {
      height: 48px;
      padding: 6px 0;
      box-sizing: border-box;
    }
    &.today {
      .cell {
        color: var(--el-color-primary);
        font-weight: bold;
      }
      &.start-date .cell,
      &.end-date .cell {
        color: $color-white;
      }
    }

    &.disabled .cell {
      background-color: var(--el-bg-color);
      cursor: not-allowed;
      color: var(--el-text-color-placeholder);

      &:hover {
        color: var(--el-text-color-placeholder);
      }
    }

    .cell {
      width: 60px;
      height: 36px;
      display: block;
      line-height: 36px;
      color: var(--el-datepicker-text-color);
      margin: 0 auto;
      border-radius: 18px;
      &:hover {
        color: var(--el-datepicker-hover-text-color);
      }
    }

    &.in-range div {
      background-color: var(--el-datepicker-inrange-bg-color);
      &:hover {
        background-color: var(--el-datepicker-inrange-hover-bg-color);
      }
    }
    &.start-date div,
    &.end-date div {
      color: $color-white;
    }

    &.start-date .cell,
    &.end-date .cell {
      color: $color-white;
      background-color: var(--el-datepicker-active-color);
    }

    &.start-date div {
      border-top-left-radius: 24px;
      border-bottom-left-radius: 24px;
    }

    &.end-date div {
      border-top-right-radius: 24px;
      border-bottom-right-radius: 24px;
    }

    &.current:not(.disabled) .cell {
      color: var(--el-datepicker-active-color);
    }
  }
}
