module.exports = {
  root: true,
  // globals: {
  //   defineProps: "readonly",
  //   defineEmits: "readonly",
  //   defineExpose: "readonly",
  // },
  env: {
    'node': true,
    'vue/setup-compiler-macros': true,
  },
  extends: [
    'plugin:vue/vue3-strongly-recommended',
    // 'plugin:vue/vue3-essential',
    '@vue/standard',
    '@vue/typescript/recommended',
  ],
  parserOptions: {
    ecmaVersion: 2020,
  },
  rules: {
    /* Enable vue/script-setup-uses-vars rule */
    'vue/script-setup-uses-vars': 'error',
    "@typescript-eslint/explicit-module-boundary-types": "off",
    /* Enable vue/max-attributes-per-line */
    'vue/max-attributes-per-line': ['error', {
      singleline: { 'max': 12 },
      multiline: { 'max': 11 },
    }],
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'comma-dangle': ['warn', {
      arrays: 'always-multiline',
      objects: 'always-multiline',
      imports: 'only-multiline',
      exports: 'only-multiline',
      functions: 'only-multiline',
    }],
    'quote-props': ['warn', 'consistent'],
    'dot-notation': 'off',
  },
}
