{"name": "app", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^2.0.10", "axios": "^0.24.0", "core-js": "^3.6.5", "echarts": "^5.2.2", "element-plus": "^2.2.25", "lodash": "^4.17.21", "moment": "^2.29.4", "qrcode": "^1.5.0", "qrcode.vue": "^3.3.3", "vue": "^3.0.0", "vue-router": "^4.0.0-0", "vuex": "^4.0.0-0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-standard": "^5.1.2", "@vue/eslint-config-typescript": "^7.0.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^8.2.0", "filemanager-webpack-plugin": "^6.1.7", "lint-staged": "^9.5.0", "node-sass": "^4.12.0", "sass": "^1.45.2", "sass-loader": "^8.0.2", "typescript": "~4.1.5"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["vue-cli-service lint", "git add"]}}