/* eslint-disable @typescript-eslint/no-var-requires */
const FileManagerPlugin = require('filemanager-webpack-plugin')
process.env.VUE_APP_VERSION = `${require('./package.json').version}`
let Month = (new Date().getMonth() + 1).toString()
if (Month.length !== 2) Month = `0${Month}`
let Dates = (new Date().getDate()).toString()
if (Dates.length !== 2) Dates = `0${Dates}`
let Hours = (new Date().getHours()).toString()
if (Hours.length !== 2) Hours = `0${Hours}`
let Minut = (new Date().getMinutes()).toString()
if (Minut.length !== 2) Minut = `0${Minut}`
const PackName = 'dist'
const FileName = `${process.env.VUE_APP_BASE_NAME}-${process.env.VUE_APP_TITLE}-${new Date().getFullYear()}-${Month}-${Dates} ${Hours} ${Minut}`
const DATA_PACK = { 环境_ENV: process.env.NODE_ENV, 程序版本: process.env.VUE_APP_VERSION, 程序标题: process.env.VUE_APP_TITLE, 程序_API: process.env.VUE_APP_BASE_URL, ENV_标签: process.env.VUE_APP_BASE_NAME }
if (process.env.NODE_ENV === 'production') Object.assign(DATA_PACK, { 输出路径: `./dist-file/${PackName}`, 压缩路径: `./dist-pack/${FileName}.zip` })
console.table(DATA_PACK)
module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  outputDir: `./dist-file/${PackName}`,
  // assetsDir: 'static',
  filenameHashing: true,
  // lintOnSave: true,
  lintOnSave: false,
  runtimeCompiler: true,

  productionSourceMap: process.env.NODE_ENV !== 'production',
  css: {
    extract: process.env.NODE_ENV === 'production',
    sourceMap: process.env.NODE_ENV !== 'production',
    loaderOptions: {
      sass: {
        // prependData: '@import "@/styles/index.scss";'
      },
    },
  },
  devServer: {
    proxy: {
      '/': {
        target: process.env.VUE_APP_BASE_URL,
        changeOrigin: true,
        pathRewrite: {
          '^/': '',
        },
      },
    },
  },
  parallel: require('os').cpus().length > 1,
  configureWebpack: config => {
    if (!config.devtool) {
      config.performance = {
        hints: 'warning',
        maxEntrypointSize: 1024 * 1000 * 3,
        maxAssetSize: 1024 * 1000 * 12,
      }
      return {
        plugins: [
          new FileManagerPlugin({
            events: {
              onEnd: { delete: ['./dist-pack/*.zip'], archive: [{ source: './dist-file', destination: `./dist-pack/${FileName}.zip`, format: 'zip' }] },
            },
          }),
        ],
      }
    }
  },
  pluginOptions: {},
  chainWebpack: config => {
    config.plugin('html').tap(args => {
      args[0].title = process.env.VUE_APP_TITLE || ''
      return args
    })
    // config.module.rule('scss').oneOfs.store.forEach(item => {
    //   item.use('sass-resources-loader').loader('sass-resources-loader').options({ resources: ['./src/styles/index.scss'] }).end()
    // })
  },
}
